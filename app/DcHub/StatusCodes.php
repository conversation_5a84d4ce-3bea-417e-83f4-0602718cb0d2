<?php

namespace App\DcHub;

class StatusCodes
{
    const ON_HOLD = [
        'label' => 'On Hold',
        'code' => '025',
        'descrption' => ' Order on hold: Indicates it is currently not processed by the vendor due to any error, lack of information, title version not mapped, etc. The ‘vendorDescription’ should always be provided.',
    ];
    const PROCESSING = [
        'label' => 'Processing',
        'code' => '026',
        'descrption' => 'The request is being processed.',
    ];
    const GENERATED = [
        'label' => 'Generated',
        'code' => '028',
        'descrption' => 'The KDMs are generated.',
    ];
    const DISPATCHING = [
        'label' => 'Dispatching',
        'code' => '029',
        'descrption' => 'When a hard drive or material is being worked on before it is being dispatched.',
    ];
    const DISPATCHED = [
        'label' => 'Dispatched',
        'code' => '030',
        'descrption' => 'When a hard drive or material is shipped for a delivery.',
    ];
    const READY_TO_SEND = [
        'label' => 'Ready to Send',
        'code' => '031',
        'descrption' => 'When a digital delivery is yet to be started.',
    ];
    const SENDING = [
        'label' => 'Sending',
        'code' => '032',
        'descrption' => 'Ongoing digital delivery.',
    ];
    const SENDING_PAUSED = [
        'label' => 'Sending Paused',
        'code' => '033',
        'descrption' => 'When a digital delivery is paused. Could be if the connection with the cinema somehow is lost for example. Or if due to traffic prioritization the sending is paused.',
    ];
    const SENT = [
        'label' => 'Sent',
        'code' => '040',
        'descrption' => 'When a digital delivery is complete. This also counts for KDMs.',
    ];
    const SENT_BY_SATELLITE = [
        'label' => 'Sent by Satellite',
        'code' => '041',
        'descrption' => 'When content is sent by satellite and is complete.',
    ];
    const INGESTED = [
        'label' => 'Ingested',
        'code' => '045',
        'descrption' => 'When the DCP or KDM ingest is complete.',
    ];
    const RETURNED = [
        'label' => 'Returned',
        'code' => '050',
        'descrption' => 'When a hard drive or material is returned from the cinema.',
    ];
    const DELIVERED = [
        'label' => 'Delivered',
        'code' => '060',
        'descrption' => 'If the hard drive, material or KDM has actually been delivered at the cinema.',
    ];
    const UNDELIVERED = [
        'label' => 'Undelivered',
        'code' => '070',
        'descrption' => 'When the hard drive, material or KDM is not delivered.',
    ];
    const CANCELLED = [
        'label' => 'Cancelled',
        'code' => '900',
        'descrption' => 'The order is cancelled. This would normally be done by the distributor only but could be done in some cases by the vendor as well.',
    ];
    const REJECTED = [
        'label' => 'Rejected',
        'code' => '901',
        'descrption' => 'When the vendor is unable to fulfil the delivery at all. For example: for a cinema or area where the vendor doesn’t support delivery or can’t deliver by the timelines expected.
        If the order can be fulfilled but there would be an issue please use the ‘On Hold’ status.',
    ];
    const FAILED = [
        'label' => 'Failed',
        'code' => '902',
        'descrption' => 'When a vendor is unable to deliver the DCP due to a broken line during electronic delivery for example.',
    ];
    const CANCELLED_AFTER_COMPLETION = [
        'label' => 'Cancelled after Completion',
        'code' => '903',
        'descrption' => 'When an order has been cancelled but already been delivered and completed.',
    ];
}
