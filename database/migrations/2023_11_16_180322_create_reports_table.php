<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('reports', function (Blueprint $table) {
            $table->id();
            $table->string('type');
            $table->json('parameters'); //any additional attributes (title_id, start/end, etc. that the generater needs)
            $table->foreignId('user_id');// who requested it
            $table->string('s3_path')->nullable();
            $table->boolean('is_processing')->default(0);
            $table->boolean('is_ready')->default(0);
            $table->boolean('is_error')->default(0);
            $table->integer('time_to_generate')->default(0);
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('reports');
    }
};
