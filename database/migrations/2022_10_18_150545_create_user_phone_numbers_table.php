<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_phone_numbers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id');
            $table->string('phone_number')->nullable();
            $table->dateTime('last_verified_at')->nullable(); // the latest time the user verified.
            $table->dateTime('original_verified_at')->nullable(); // the original time the user verified, if this is null, 2fa via SMS is disabled.
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_phone_numbers');
    }
};
