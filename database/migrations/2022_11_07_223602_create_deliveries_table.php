<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('deliveries', function (Blueprint $table) {
            $table->id();
            $table->timestamps();
            $table->string('status');
            $table->integer('progress');
            $table->foreignId('booking_id');
            $table->foreignId('organization_id');
            $table->foreignId('cinema_site_id')->nullable();
            $table->foreignId('version_id')->nullable();
            $table->foreignId('title_id')->nullable();
            $table->string('cpl_uuid')->nullable();
            $table->boolean('is_electronic');
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('deliveries');
    }
};
