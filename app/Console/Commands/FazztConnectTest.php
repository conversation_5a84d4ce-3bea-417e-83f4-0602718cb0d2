<?php

namespace App\Console\Commands;

use App\Fazzt\Json;
use Illuminate\Console\Command;

class FazztConnectTest extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fazzt:test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test connection to Fazzt Tunnel';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {


        \App\Fazzt\Cinema\Content::exec(909920);

        // dump($response);
        return Command::SUCCESS;
    }
}
