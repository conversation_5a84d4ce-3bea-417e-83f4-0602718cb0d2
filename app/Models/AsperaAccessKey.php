<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use GuzzleHttp\Client;

class AsperaAccessKey extends Model
{
    use SoftDeletes;

    protected $casts = ['config' => 'json'];
    protected $fillable = ['path'];

    public function createKeyInAspera($where = 's3')
    {
        $url = config('aspera.ats.access_keys_url');
        $key = config('aspera.ats.key');
        $secret = config('aspera.ats.secret');
        $bucket = config("filesystems.disks.$where.bucket");
        $arn = config('aspera.ats.aws_role_arn');
        $externalId = config('aspera.ats.aspera_external_id');

        if ($where === 's3-deluxe') {
            //            $key = config('aspera.deluxe.key');
            //            $secret = config('aspera.deluxe.secret');
            $arn = config('aspera.deluxe.aws_role_arn');
            $externalId = config('aspera.deluxe.aspera_external_id');
        }

        $accessKeyData = [
            'storage' => [
                'type' => 'aws_s3',
                'endpoint' => "s3.amazonaws.com",
                'bucket' => $bucket,
                'path' => $this->path,
                'storage_class' => 'STANDARD',
                'server_side_encryption' => 'AES256',
                'credentials' => [
                    'type' => 'assume-role',
                    'assume_role_arn' => $arn,
                    'assume_role_external_id' => $externalId,
                ],
            ],
            'transfer_server_id' => config('aspera.ats.transfer_server_id'),
        ];

        try {
            $response = (new Client())->post(
                $url,
                [
                    'auth' => [$key, $secret],
                    'json' => $accessKeyData,
                ]
            );

            $resultString = $response->getBody();
            $result = json_decode($resultString);
        }
        catch (Exception $e) {
            $result = $e;
        }

        $this->config = $result;
        $this->save();

        if (isset($result->error)) {
            logger()->error("Aspera Key Gen error: {$result->error}");
            return;
        }
        if (!isset($result->id)) {
            abort(500, "Aspera credentials were not successfully generated.");
        }

        return $result;
    }

    public function deleteKeyFromAspera()
    {
        $id = $this->getAsperaID();
        if (!$id) {
            return;
        }

        $url = config('aspera.ats.access_keys_url') . '/' . $id;
        $key = config('aspera.ats.key');
        $secret = config('aspera.ats.secret');

        $command = 'curl -s -i -u ' . $key . ':' . $secret . ' -X DELETE ' . $url;
        $output = shell_exec($command);

        $accessKeyResult = json_decode($output);
        $this->delete();
    }

    private function getAsperaID()
    {
        return is_array($this->config) ? $this->config['id'] : $this->config->id ?? null;
    }
}
