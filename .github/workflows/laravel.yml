name: Laravel Unit Tests

on:
  push:
    branches:
      - '*'
  pull_request:
    branches: [ main, staging ]

jobs:
  laravel-tests:
    permissions:
      id-token: write
      contents: read
    runs-on: ubuntu-latest
    steps:
      - uses: shivammathur/setup-php@v2
        with:
          php-version: '8.2'
      - uses: actions/checkout@v3
      - name: Copy .env
        run: php -r "file_exists('.env') || copy('.env.example', '.env');"
      - name: Install Dependencies
        run: composer install -q --no-ansi --no-interaction --no-scripts --no-progress --prefer-dist
      - name: Generate key
        run: php artisan key:generate
      - name: Directory Permissions
        run: chmod -R 777 storage bootstrap/cache
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          role-to-assume: ${{ secrets.AWS_ROLE_TO_ASSUME }}
          aws-region: us-east-1
      - name: Create Database
        run: |
          mkdir -p database
          touch database/database.sqlite
          php artisan --env testing migrate
      - name: Execute tests (Unit and Feature tests) via PHPUnit
        env:
          DB_CONNECTION: sqlite
          DB_DATABASE: database/database.sqlite
        run: php artisan test --parallel --coverage-clover=.badges/clover.xml
      - name: Generate Code Coverage Badge
        uses: timkrase/phpunit-coverage-badge@v1.2.0
        with:
          report: .badges/clover.xml
          coverage_badge_path: .badges/code-coverage.svg
          push_badge: false
          repo_token: ${{ secrets.GITHUB_TOKEN }}
      - name: Copy Coverage Badge to S3 Bucket
        run: |
          aws s3 cp .badges/code-coverage.svg s3://cinesend-public-assets-dev/github-badges/dcdc-api-badge.svg
  deploy-to-staging:
    if: github.ref == 'refs/heads/staging'
    name: Deploy To Vapor Staging
    runs-on: ubuntu-latest
    needs: [ laravel-tests ]
    env:
      VAPOR_API_TOKEN: ${{ secrets.VAPOR_API_TOKEN }}
    steps:
      - uses: shivammathur/setup-php@v2
        with:
          php-version: '8.2'
      - uses: actions/checkout@v3
      - run: composer install --no-dev --prefer-dist --no-ansi --no-interaction --no-progress --no-scripts
      - run: ./vendor/bin/vapor team:switch --id 30166
      - run: ./vendor/bin/vapor deploy staging --without-waiting
  deploy-to-production:
    if: github.ref == 'refs/heads/main'
    name: Deploy To Vapor Production
    runs-on: ubuntu-latest
    needs: [ laravel-tests ]
    env:
      VAPOR_API_TOKEN: ${{ secrets.VAPOR_API_TOKEN }}
    steps:
      - uses: shivammathur/setup-php@v2
        with:
          php-version: '8.2'
      - uses: actions/checkout@v3
      - run: composer install --no-dev --prefer-dist --no-ansi --no-interaction --no-progress --no-scripts
      - run: ./vendor/bin/vapor team:switch --id 30166
      - run: ./vendor/bin/vapor deploy production --without-waiting
