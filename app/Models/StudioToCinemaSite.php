<?php

namespace App\Models;

use Exception;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StudioToCinemaSite extends Model
{
    use HasFactory;

    protected $fillable = ['organization_id', 'cinema_site_id', 'studio_site_id', 'studio_data'];
    protected $casts = ['studio_data' => 'json'];

    public function cinemaSite()
    {
        return $this->belongsTo(CinemaSite::class);
    }

    static function getCinemaSiteByStudioId($orgId, $id)
    {
        return self::where('organization_id', $orgId)->where('studio_site_id', $id)->first()->cinemaSite ?? null;
    }

    static function setCinemaSiteByStudioData($orgId, $studioData)
    {
        // try to find existing by address.
        $address = $studioData['physicalAddress'];
        $site = CinemaSite::where('country_code', $address['iso'])
            ->where('circuit', $address['circuitName'])
            ->where('state', $address['region'])
            ->where('zip', $address['postalCode'])
            ->where('address', $address['address1'])
            ->first();

        if (!$site) {
            // create site.
            $site = CinemaSite::create([
                'organization_id' => $orgId,
                'name' => $address['siteName'],
                'country_code' => $address['iso'],
                'address' => $address['address1'],
                'circuit' => $address['circuitName'] ?? '',
                'city' => $address['city'],
                'state' => $address['region'],
                'zip' => $address['postalCode'],
            ]);
        }

        // create mapping
        try {
            $site->mappings()->create([
                'organization_id' => $orgId,
                'studio_site_id' => $address['siteId'],
                'studio_data' => $studioData,
            ]);
        } catch (Exception $e) {
            // likely a duplicate.
            logger($e->getMessage());
        }

        return $site;
    }
}
