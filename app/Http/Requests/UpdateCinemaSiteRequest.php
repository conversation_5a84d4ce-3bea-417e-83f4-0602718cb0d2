<?php

namespace App\Http\Requests;

use App\Models\CinemaSite;
use App\Models\Enum\SiteStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Enum;

class UpdateCinemaSiteRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return auth()->user()->isAdmin();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [ // this can allow for incremental updates
            'name' => 'sometimes',
            'address' => 'sometimes',
            'city' => 'sometimes',
            'circuit' => 'sometimes',
            'state' => ['sometimes', Rule::in(array_keys(CinemaSite::$stateList))],
            'zip' => 'sometimes',
            'csx_serial_number' => ['sometimes', 'string'],
            'disney_site_id' => ['sometimes', 'int', 'nullable'],
            'paramount_theatre_id' => ['sometimes', 'string', 'nullable'],
            'lionsgate_theatre_id' => ['sometimes', 'string', 'nullable'],
            'dchub_cinema_id' => ['sometimes', 'string', 'nullable'],
            'universal_site_id' => ['sometimes', 'string', 'nullable'],
            'sony_site_id' => ['sometimes', 'string', 'nullable'],
            'tcn' => ['sometimes', 'string'],
            'rentrack' => ['sometimes', 'string', 'nullable'],
            'sage_customer_number' => ['sometimes', 'string', 'nullable'],
            'secondary_tcn' => ['sometimes', 'string', 'nullable'],
            'primary_contact_name' => ['sometimes', 'string'],
            'primary_contact_number' => ['sometimes', 'string'],
            'primary_contact_email' => ['sometimes', 'string'],
            'secondary_contact_name' => ['sometimes', 'string'],
            'secondary_contact_number' => ['sometimes', 'string'],
            'secondary_contact_email' => ['sometimes', 'string'],
            'status' => ['sometimes', new Enum(SiteStatus::class)],
        ];
    }
}
