<?php

namespace App\Console\Commands;

use App\Models\Enum\BookingStatus;
use Illuminate\Console\Command;
use App\Models\Booking;
use App\StudioHandlers\Disney\InboundProcessor;
use Sentry\Tracing\Transaction;

class RecheckBookings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bookings:recheck';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Re-check any booking that has no title, to be run post-uploads to see if new content matches.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $bookings = Booking::whereNull('title_id')->whereIn('overall_status', BookingStatus::getStatusesByGroup('booked'))->get();

        /** @var Booking $booking */
        foreach ($bookings as $booking) {
            /** @var Transaction $transaction */
            foreach ($booking->transactions as $transaction) {
                $handler = new InboundProcessor($booking->id, $transaction->id, true);
                $handler->processBooking();
            }
        }
    }
}
