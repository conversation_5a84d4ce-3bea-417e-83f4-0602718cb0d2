<?php

namespace App\Http\Controllers;

use App\Fazzt\DCP\Cancel;
use App\Models\CinemaProContent;
use App\Models\FazztContent;
use App\Models\FazztRequestLog;
use App\Models\FazztTransfer;
use App\Models\Release;

class FazztController extends Controller
{
    /**
     * Return trip payload from Cinema Pro Server with all content that the server has.
     * @return false|string
     */
    public function content()
    {

        $realResponse = request()->post('params');

        dump($realResponse);

        $status = $realResponse[0]['status'];
        $uuid = $realResponse[0]['uuid'];
        $siteId = $realResponse[0]['siteid'];

        $values = $status['Value'];

        foreach ($values as $v) {
            logger($v);
            $unique = [
                'asset_uuid' => $v['AssetUUID'],
                'site_id' => $siteId,
            ];
            $theRest = [
                'file_count' => $v['FileCount'],
                'library_status' => $v['Status'],
                'cru_status' => $v['Status_cru'],
            ];
            CinemaProContent::updateOrCreate($unique, $theRest);

            // "AssetUUID" => "a7d496a0-495f-4342-9bfd-01da3ba78619"
            //  "FileCount" => 6
            //  "FTPCount" => 6
            //  "Status_cru" => "UNPUBLISH"
            //  "OnExternal" => "Internal Storage"
            //  "Status" => "PUBLISH"
        }

        // todo: delete any content that is in the db that was not in this return payload. (it's gone!)

        $logLine = FazztRequestLog::where('response_code', $uuid)->first();
        $logLine->is_completed = true;
        $logLine->save();

        // this needs to be encoded json, no funny business.
        return json_encode(['id' => request()->post('id')]);
    }

    /**
     * Return trip payload from Cinema Pro servers after a publish command.
     * @return void
     */
    public function publish()
    {
        $realResponse = request()->post();

        dump($realResponse);

        // todo: we're not actually doing anything here yet, but when we do
        // whatever release this is related to run $release->setFirstOrLastPublished()

        return json_encode(['id' => request()->post('id')]);

    }

    public function cruReport()
    {
        $realResponse = request()->post('params');

        dump($realResponse);

        // flag the logged request as completed by its UUID.
        $uuid = $realResponse[0]['uuid'];
        $log = FazztRequestLog::where('response_code', $uuid)->first();
        $log->is_completed = true;
        $log->save();

        return json_encode(['id' => request()->post('id')]);
    }

    public function cruMode()
    {
        $realResponse = request()->post('params');

        dump($realResponse);

        // flag the logged request as completed by its UUID.
        $uuid = $realResponse[0]['uuid'];
        $log = FazztRequestLog::where('response_code', $uuid)->first();
        $log->is_completed = true;
        $log->save();

        return json_encode(['id' => request()->post('id')]);

    }

    public function cruPublish()
    {
        $realResponse = request()->post('params');

        dump($realResponse);

        return json_encode(['id' => request()->post('id')]);
    }

    /**
     * Return trip payload from Cinema Pro servers after an unpublish command.
     * @return void
     */
    public function unpublish()
    {
        $realResponse = request()->post('params');

        dump($realResponse);
        return json_encode(['id' => request()->post('id')]);

    }

    /**
     * Return trip payload from fazzt server during an ingest command (onProcessChange)
     * @return void
     */
    public function ingest()
    {

        $realResponse = request()->post('params');

        dump($realResponse);
        return json_encode(['id' => request()->post('id')]);

    }

    /**
     * Read only data for the UI
     */
    public function activeTransfers()
    {
        // grab the list of known transfers from the db
        /**
         * - Priority (index order)
         * - Status (read-only from Fazzt)
         * - Progress (percent)
         * - Title (from release->title)
         * - Release (from release)
         * - Studio (from release->title->org)
         * - Release day (inferred as day from title date)
         * - Release date (from title)
         * - Scheduled start (inferred from queue)
         * - Scheduled end (inferred from queue)
         * - Actual start (from fazzt server)
         * - Actual end (from fazzt server)
         * - Size (from package)
         * - Estimated Tx Time (from size/time)
         */

        return FazztTransfer::with('version')
            ->where('state', 'PENDING')
            ->orWhere('state', 'SENDING')
            ->orderBy('priority')
            ->paginate();
    }

    public function completedTransfers()
    {
        return FazztTransfer::with('version')
            ->where('state', 'COMPLETE')
            ->orderBy('actual_completed_at')
            ->paginate();
    }


    public function setPriority()
    {
        // input: array of FazztTransfer IDs with priority value
        // Set the priority of pending transfers and calculate estimated start times based on their release size @ 40Mbit per sec.
        $transfersToSave = [];
        foreach (request()->all() as $item) {
            $transfer = FazztTransfer::where('id', $item['id'])->first();
            if (!$transfer) {
                return response()->json([
                    'message' => "Transfer not found.",
                    'transfers' => $this->activeTransfers()
                ], 404);
            }
            if ($transfer->state === 'TRANSMITTING' && $transfer->priority != $item['priority']) {
                return response()->json([
                    'message' => "Cannot change priority of a transfer that is currently transmitting.",
                    'transfers' => $this->activeTransfers()
                ], 400);
            }

            $transfer->priority = $item['priority'];
            $transfersToSave[] = $transfer;
        }

        // Only save the transfers if none of them have an issue
        foreach ($transfersToSave as $transfer) {
            $transfer->save();
        }

        FazztTransfer::recalculatePriorityAndStartTime();
        return $this->activeTransfers();
    }

    public function getAvailableToQueue()
    {
        // anything that has fazzt content available can be queued up.
        // just return the id as value and a nice label for the UI
        $values = [];
        foreach (Release::with('title')->whereHas('content.fazztContent')->get() as $release) {
            $name = sprintf("%s | %s | %s", $release->title->friendly_title, $release->package_name, $release->type->label());
            $values[] = ['label' => $name, 'value' => $release->content->first()->fazztContent->id];
        }
        return $values;
    }

    public function cancelTransfer()
    {
        $activeTransfer = FazztTransfer::where('status', 'PROCESSING')->where('state', 'SENDING')->first();
        //
        $activeTransfer->fill(['priority' => -1, 'status' => 'CANCELLED', 'state' => 'CANCELLED'])->save();

        Cancel::exec($activeTransfer->transmit_id);

        FazztTransfer::recalculatePriorityAndStartTime();
        return $this->activeTransfers();
    }

    public function deleteFromQueue()
    {

        $id = request()->post('deleteId');
        FazztTransfer::where('id', $id)->delete();

        FazztTransfer::recalculatePriorityAndStartTime();
        return $this->activeTransfers();
    }

    public function addToQueue()
    {
        // verify this id is 'ready to transmit'
        // add the fazzt_content_id to the fazzt_transfers table with last queue priority
        $fcId = request()->post('fcId');
        $fc = FazztContent::find($fcId);
        if (!$fc) {
            abort(404);
        }

        logger($fc);
        $duration = ($fc->package_size / 40000000) * 1000; // duration is ms
        $minutes = $duration / 1000 / 60;

        $data = [
            'asset_uuid' => $fc->asset_uuid,
            // this will get updated when fazzt transfer actually starts, but for now, we will calculate it
            'duration' => $duration, // calc this based on the package size and 40 Mbps. get size from fc->package_size
            'estimated_minutes' => $minutes,
            'priority' => FazztTransfer::getMaxPriority()
        ];

        logger($data);
        $ft = FazztTransfer::create($data);

        FazztTransfer::recalculatePriorityAndStartTime();

        return $ft;
    }

    public function dcps()
    {
        // grab a list of known dcps so a transfer can be triggered if necessary
        return FazztContent::with('version')->latest()->paginate();
    }

    public function commandLog()
    {
        // command logs.
        return FazztRequestLog::latest()->paginate();
    }
}
