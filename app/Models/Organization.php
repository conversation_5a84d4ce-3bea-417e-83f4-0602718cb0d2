<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Permission\Traits\HasRoles;

class Organization extends Model
{
    use SoftDeletes, HasFactory, HasRoles, LogsActivity;

    protected $fillable = ['owner_id', 'name', 'type'];

    protected $casts = ['owner_id' => 'integer'];

    const TYPE_STUDIO = 'studio';
    const TYPE_VENDOR = 'vendor';
    const TYPE_EXHIBITOR = 'exhibitor';
    const TYPE_ADMIN = 'admin';

    public function users()
    {
        return $this->hasMany(User::class);
    }

    public function owner()
    {
        return $this->hasOne(User::class, 'owner_id');
    }

    public function titles()
    {
        return $this->hasMany(Title::class);
    }

    public function imports()
    {
        return $this->hasMany(ImportBooking::class);
    }

    public function cinemas()
    {
        return $this->hasMany(CinemaSite::class);
    }

    public function transactions()
    {
        return $this->hasMany(StudioTransaction::class);
    }

    public function bookings()
    {
        return $this->hasMany(Booking::class);
    }

    public function webhooks() {
        return $this->hasMany(OrganizationWebhookUrl::class);
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->dontSubmitEmptyLogs()
            ->logFillable()
            ->logOnlyDirty();
    }

}
