<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth:sanctum'])->group(function () {
    // The primary auth check route will return user info if signed in, they can be signed in without verified email.
    // expose the lists so the UI can do its thing.
    Route::get('/user', function (Request $request) {
        return auth()->user()->setAppends([
            'all_permissions',
            'all_roles',
            'organization_type',
            'two_factor_activated',
            'two_factor_pending',
            'two_factor_sms_pending',
            'needs_password',
            'profile_image',
        ]);
    });
    Route::group(['prefix' => '2fa'], function () {
        Route::post('options', [\App\Http\Controllers\Auth\Verify2faController::class, 'getOptions']);
        Route::post('send', [\App\Http\Controllers\Auth\Verify2faController::class, 'sendCode']);
        Route::post('verify', [\App\Http\Controllers\Auth\Verify2faController::class, 'verifyCode']);
    });

});

Route::middleware(['auth.basic'])->group(function () {
    Route::post(
        '/booking/{studio}',
        [\App\Http\Controllers\StudioBookingController::class, 'createTransaction']
    )->name('studio-booking');
});

// hold the old route in case existing jobs are still calling it for a little while...
Route::post('e-deliveries/{delivery}',
    [\App\Http\Controllers\DeliveryController::class, 'updateElectronic']);
Route::post('e-deliveries/{delivery}/job/{mediaManagerJob}',
    [\App\Http\Controllers\DeliveryController::class, 'updateElectronic'])->name('update-e-delivery');

// any regular user routes need to have 'verified' middleware to force the email to be valid
// the user is technically logged in otherwise, but can't access anything.
Route::middleware(['auth:sanctum', 'verified', 'verify-sms'])->group(function () {

    Route::group(['prefix' => '2fa'], function () {
        Route::post('setup-sms', [\App\Http\Controllers\Auth\Verify2faController::class, 'setPhoneNumber']);
        Route::post('remove', [\App\Http\Controllers\Auth\Remove2faController::class, 'remove']);
        Route::post('verify-setup', [\App\Http\Controllers\Auth\Verify2faController::class, 'verifySetupCode']);

    });

    Route::get(
        '/booking/{studio}/{transaction}',
        [\App\Http\Controllers\StudioBookingController::class, 'retryTransaction']
    )->name('studio-retry-transaction');

    Route::get('reports', [\App\Http\Controllers\ReportController::class, 'reportsList'])->name('reports-index');
    Route::post('reports', [\App\Http\Controllers\ReportController::class, 'generateReport'])->name('reports-generate');
    Route::get('reports/{report}',
        [\App\Http\Controllers\ReportController::class, 'downloadReport'])->name('reports-download');
    Route::delete('reports/{report}',
        [\App\Http\Controllers\ReportController::class, 'deleteReport'])->name('reports-delete');

    Route::post('aspera/upload', [\App\Http\Controllers\AsperaController::class, 'upload'])->name('aspera-upload');
    Route::get('aspera/uploads/{title}',
        [\App\Http\Controllers\AsperaController::class, 'uploadsForTitle'])->name('aspera-uploads-for-title');
    Route::post('aspera/download',
        [\App\Http\Controllers\AsperaController::class, 'download'])->name('aspera-download');

    // resource routes for all the models... these will all be wrapped in policies with
    // form request objects for any write operations (Create or Update)
    Route::get(
        'users/{user}/restore',
        [\App\Http\Controllers\UserController::class, 'restore']
    )->name('users.restore');
    Route::get(
        'users/{user}/resend',
        [\App\Http\Controllers\UserController::class, 'resend']
    )->name('users.resend');
    Route::apiResource('users', \App\Http\Controllers\UserController::class);
    Route::apiResource('users.tokens', \App\Http\Controllers\UserTokenController::class)
        ->only(['index', 'store', 'destroy']);

    Route::post('user/upload-profile', [\App\Http\Controllers\UserProfileController::class, 'uploadProfilePicture']);
    Route::put('user/upload-profile/{user}',
        [\App\Http\Controllers\UserProfileController::class, 'storeProfilePicture'])->name('upload-profile-image');
    Route::delete('user/upload-profile', [\App\Http\Controllers\UserProfileController::class, 'deleteProfilePicture']);

    Route::get(
        'organizations/{organization}/restore',
        [\App\Http\Controllers\OrganizationController::class, 'restore']
    )->name('organizations.restore');
    Route::get(
        'organizations/{organization}/users',
        [\App\Http\Controllers\UserController::class, 'indexByOrganization']
    )->name('organizations.users');
    Route::get(
        'organizations/{organization}/transactions',
        [\App\Http\Controllers\OrganizationController::class, 'transactions']
    )->name('organizations.transactions');

    Route::get(
        'organizations/{organization}/webhooks',
        [\App\Http\Controllers\OrganizationController::class, 'webhooks']
    )->name('organizations.webhooks');

    Route::put(
        'organizations/{organization}/webhooks/{organizationWebhookUrl}',
        [\App\Http\Controllers\OrganizationController::class, 'updateWebhook']
    )->name('organizations.update-webhook');

    Route::delete(
        'organizations/{organization}/webhooks/{organizationWebhookUrl}',
        [\App\Http\Controllers\OrganizationController::class, 'deleteWebhook']
    )->name('organizations.delete-webhook');

    Route::post(
        'organizations/{organization}/webhooks',
        [\App\Http\Controllers\OrganizationController::class, 'storeWebook']
    )->name('organizations.store-webhook');

    Route::apiResource('organizations', \App\Http\Controllers\OrganizationController::class);

    Route::apiResource('roles', \App\Http\Controllers\RoleController::class)->except(['destroy']);
    Route::apiResource('permissions', \App\Http\Controllers\PermissionController::class)->except(['destroy']);

    route::get('titles/orphans', [\App\Http\Controllers\AsperaController::class, 'getFakeDeluxeTransfers']);
    route::post('titles/orphans/{title}/{asperaTransfer}',
        [\App\Http\Controllers\AsperaController::class, 'linkFakeDeluxTransferToTitle']);

    Route::apiResource('titles', \App\Http\Controllers\TitleController::class);
    Route::apiResource('titles.releases', \App\Http\Controllers\TitleReleaseController::class);
    Route::post('titles/{title}/releases/{release}/trigger-fazzt',
        [\App\Http\Controllers\TitleReleaseController::class, 'prepareForFazzt']);

    Route::apiResource('releases', \App\Http\Controllers\ReleaseController::class)->except(['destroy', 'store']);

    Route::get('releases/{release}/toggle-pin', [\App\Http\Controllers\ReleaseController::class, 'togglePin'])->name('releases.toggle-pin');
    Route::get('releases/{release}/issues', [\App\Http\Controllers\ReleaseController::class, 'getIssues'])->name('releases.get-issues');
    Route::post('releases/upload-letter/{release}',
        [\App\Http\Controllers\TitleReleaseController::class, 'uploadIngestLetter']);
    Route::put('releases/upload-letter/{release}',
        [\App\Http\Controllers\TitleReleaseController::class, 'storeIngestLetter'])->name('upload-ingest-letter');
    // this exists because the JS uploader always appends an ID and will 404 at the end of a successful upload. :(
    Route::put('releases/upload-letter/{release}/{extraId}',
        [\App\Http\Controllers\TitleReleaseController::class, 'storeIngestLetter']);


    Route::get(
        'titles/{title}/requests',
        [\App\Http\Controllers\TitleController::class, 'getUploadLinksForTitle']
    )->name('titles.upload-links-for-title');
    Route::post(
        'titles/{title}/requests',
        [\App\Http\Controllers\TitleController::class, 'storeUploadLinkForTitle']
    )->name('titles.store-upload-links-for-title');
    Route::put(
        'titles/{title}/requests/{link}',
        [\App\Http\Controllers\TitleController::class, 'updateUploadLinkForTitle']
    )->name('titles.update-upload-links-for-title');

    Route::get(
        'titles/{title}/versions',
        [\App\Http\Controllers\VersionController::class, 'indexForTitle']
    )->name('versions.index-for-title');
    Route::post(
        'titles/{title}/versions',
        [\App\Http\Controllers\VersionController::class, 'storeForTitle']
    )->name('versions.store-for-title');

    Route::apiResource('versions', \App\Http\Controllers\VersionController::class);

    Route::get('warehouses', [\App\Http\Controllers\CinemaSiteController::class, 'warehouses']);
    Route::apiResource('cinemas', \App\Http\Controllers\CinemaSiteController::class);
    Route::post('cinemas/{cinemaSite}/contacts', [\App\Http\Controllers\CinemaSiteController::class, 'createContact']);
    Route::delete('cinemas/{cinemaSite}/contacts/{cinemaSiteContact}',
        [\App\Http\Controllers\CinemaSiteController::class, 'deleteContact']);

    Route::put(
        'bookings/bulk-update',
        [\App\Http\Controllers\BookingController::class, 'bulkUpdateStatuses']
    )->name('bookings.bulk-update');
    Route::apiResource('bookings', \App\Http\Controllers\BookingController::class);

    Route::apiResource('import-bookings', \App\Http\Controllers\ImportBookingController::class);
    Route::get('import-bookings/{import_booking}/start',
        [\App\Http\Controllers\ImportBookingController::class, 'fireCreateBookingJob']);
    Route::get('import-bookings/{import_booking}/{filter}',
        [\App\Http\Controllers\ImportBookingController::class, 'filtered'])
        ->whereIn('filter', ['pending', 'matched', 'booked']);
    Route::apiResource('import-booking-records', \App\Http\Controllers\ImportBookingRecordController::class);

    Route::put(
        'transfers/bulk-update',
        [\App\Http\Controllers\DeliveryController::class, 'bulkUpdateStatuses']
    )->name('deliveries.bulk-update');
    Route::apiResource('deliveries', \App\Http\Controllers\DeliveryController::class);
    Route::apiResource('transfers', \App\Http\Controllers\DeliveryController::class);

    Route::post('transfers/{delivery}/send-download-job',
        [\App\Http\Controllers\DeliveryController::class, 'sendDownloadJob'])->name('deliveries.send-download-job');
    Route::get('bookings/{booking}/transfers',
        [\App\Http\Controllers\DeliveryController::class, 'indexByBooking'])->name('deliveries.get-by-booking');

    Route::get('titles/{title}/bookings',
        [\App\Http\Controllers\BookingController::class, 'indexByTitle'])->name('bookings.get-by-title');
    Route::get('cinemas/{cinemaSite}/bookings',
        [\App\Http\Controllers\BookingController::class, 'indexByCinemaSite'])->name('bookings.get-by-cinema-site');
    Route::post('bookings/{booking}/start-transfers',
        [\App\Http\Controllers\BookingController::class, 'startDeliveries'])->name('bookings.start-deliveries');

    // Submit jobs via the UI
    Route::post('media-managers/jobs', [\App\Http\Controllers\JobController::class, 'store'])->name('post-store-job');
    Route::delete('media-managers/jobs/{mediaManagerJob}',
        [\App\Http\Controllers\JobController::class, 'delete'])->name('delete-job');
    Route::post('media-managers/retry-job/{mediaManagerJob}',
        [\App\Http\Controllers\JobController::class, 'retryJob'])->name('post-retry-job');
    //
    Route::apiResource('apple-televisions', \App\Http\Controllers\AppleTelevisionController::class);
    Route::apiResource('media-managers', \App\Http\Controllers\MediaManagerController::class);
    // do not need, keep a very specific list of routes for notes since it's morphable to anything.
    Route::apiResource('notes', \App\Http\Controllers\NoteController::class)->only(['destroy', 'update']);
    Route::apiResource('cinema-pro-servers', \App\Http\Controllers\CinemaProServerController::class)->only([
        'index',
        'show',
        'update',
    ]);
    Route::post('cinema-pro-servers/{cinema_pro_server}/refresh',
        [\App\Http\Controllers\CinemaProServerController::class => 'refreshContent']);
    Route::post('cinema-pro-servers/{cinema_pro_server}/publish',
        [\App\Http\Controllers\CinemaProServerController::class => 'publish']);
    Route::post('cinema-pro-servers/{cinema_pro_server}/unpublish',
        [\App\Http\Controllers\CinemaProServerController::class => 'unpublish']);
    Route::post('cinema-pro-servers/{cinema_pro_server}/publish-cru',
        [\App\Http\Controllers\CinemaProServerController::class => 'publishCru']);
    Route::post('cinema-pro-servers/{cinema_pro_server}/unpublish-cru',
        [\App\Http\Controllers\CinemaProServerController::class => 'upublishCru']);

    // see other fazzt endpoints below (direct access from fazzt or cinema pro servers, no user auth)
    Route::group(['prefix' => 'fazzt'], function () {
        // read only data
        Route::get('active-transfers', [\App\Http\Controllers\FazztController::class, 'activeTransfers']);
        Route::get('completed-transfers', [\App\Http\Controllers\FazztController::class, 'completedTransfers']);
        Route::post('set-priority', [\App\Http\Controllers\FazztController::class, 'setPriority']);
        Route::post('cancel-transfer', [\App\Http\Controllers\FazztController::class, 'cancelTransfer']);
        Route::post('delete-from-queue', [\App\Http\Controllers\FazztController::class, 'deleteFromQueue']);
        Route::post('add-to-queue', [\App\Http\Controllers\FazztController::class, 'addToQueue']);
        Route::get('available-to-queue', [\App\Http\Controllers\FazztController::class, 'getAvailableToQueue']);
        Route::get('dcps', [\App\Http\Controllers\FazztController::class, 'dcps']);
        Route::get('command-log', [\App\Http\Controllers\FazztController::class, 'commandLog']);

        // initiate broadcast commands
        Route::get('send-transfer', [\App\Http\Controllers\FazztController::class, 'send-transfer']);
        Route::get('ingest-dcp', [\App\Http\Controllers\FazztController::class, 'ingest-dcp']);

    });

    Route::post('notes/equipment/{mediaManager}', [\App\Http\Controllers\NoteController::class, 'saveEquipmentNote']);
    Route::get('notes/equipment/{mediaManager}', [\App\Http\Controllers\NoteController::class, 'getEquipmentNotes']);
    Route::post('notes/site/{cinemaSite}', [\App\Http\Controllers\NoteController::class, 'saveSiteNote']);
    Route::get('notes/site/{cinemaSite}', [\App\Http\Controllers\NoteController::class, 'getSiteNotes']);

    // display jobs in the UI via this
    Route::get('cinemas/{cinemaSite}/jobs',
        [\App\Http\Controllers\JobController::class, 'index'])->name('get-jobs-by-cinema-site');

    Route::get('cinemas/{cinemaSite}/analytics',
        [\App\Http\Controllers\CinemaSiteAnalyticsController::class, 'index'])->name('get-analytics-by-cinema-site');

    // any specialized routes can follow...
    Route::get('/dashboard', [\App\Http\Controllers\DashboardController::class, 'dashboard']);
    Route::get('/dashboard/studio', [\App\Http\Controllers\DashboardController::class, 'studio']);
});

Route::group(['middleware' => 'web', 'prefix' => 'media-managers', 'as' => 'media-managers.'], function () {
    // Auth
    Route::post('login', [\App\Http\Controllers\MediaManagerController::class, 'login'])->name('post-login');

    // Health
    Route::group([
        'middleware' => [
            'media-manager',
            'auth:media-manager',
        ],
        'prefix' => '{serialNumber}',
    ], function () {

        // Jobs callback from cmms will be authenticated as a media-manager
        Route::post('jobs/{mediaManagerJob}',
            [\App\Http\Controllers\JobController::class, 'update'])->name('post-update-jobs');

        // Heartbeats
        Route::post('trigger-heartbeat',
            [\App\Http\Controllers\MediaManagerController::class, 'triggerHeartbeat'])->name('post-trigger-heartbeat');

        Route::post('settings',
            [\App\Http\Controllers\MediaManagerController::class, 'updateSystemSettings'])->name('post-settings');
        Route::post('smart-info',
            [\App\Http\Controllers\MediaManagerController::class, 'updateSmartInfo'])->name('post-smart-info');

        // Update routes for device fields: downloads, drive, library
        Route::post('downloads',
            [\App\Http\Controllers\MediaManagerController::class, 'updateDownloads'])->name('post-downloads');
        Route::post('media-library',
            [\App\Http\Controllers\MediaManagerController::class, 'updateMediaLibrary'])->name('post-media-library');
        Route::post('drives',
            [\App\Http\Controllers\MediaManagerController::class, 'updateDrives'])->name('post-drives');

        // Update route for drive jobs and library jobs:
        Route::post('media-library/jobs',
            [\App\Http\Controllers\JobController::class, 'updateMediaLibraryJobs',])->name('post-media-library-jobs');
        Route::post('drives/jobs',
            [\App\Http\Controllers\JobController::class, 'updateDriveJobs'])->name('post-drives-jobs');

    });
});

/* Unauthenticated signed URLs for upload Requests. */
Route::group(['prefix' => 'requests'], function () {
    Route::get('validate/{link}',
        [\App\Http\Controllers\TitleController::class, 'validateUploadLinkForTitle'])->name('verify-upload-link');
    Route::post('upload/{link}',
        [\App\Http\Controllers\TitleController::class, 'createAsperaUploadForLink'])->name('start-upload-link');
});

// used for anything inbound via AWs.
Route::group(['middleware' => 'web', 'prefix' => 'aws'], function () {
    Route::post('ftp-bridge', [\App\Http\Controllers\AwsController::class, 'ftpBridge'])->name('aws-ftp-bridge');
});

// todo: Authenticate these round trip URLs somehow (long term signed URLS?)
// Unauthenticated return data from Cinema Pro or Fazzt servers depending on command.
Route::group(['prefix' => 'fazzt'], function () {
    Route::post('content', [\App\Http\Controllers\FazztController::class, 'content']);
    Route::post('publish', [\App\Http\Controllers\FazztController::class, 'publish']);
    Route::post('unpublish', [\App\Http\Controllers\FazztController::class, 'unpublish']);
    Route::post('ingest', [\App\Http\Controllers\FazztController::class, 'ingest']);
    Route::post('cru-mode', [\App\Http\Controllers\FazztController::class, 'cruMode']);
    Route::post('cru-publish', [\App\Http\Controllers\FazztController::class, 'cruPublish']);
    Route::post('cru-report', [\App\Http\Controllers\FazztController::class, 'cruReport']);
});

Route::get('request-access', [\App\Http\Controllers\Auth\RequestAccessController::class, 'requestAccess']);
