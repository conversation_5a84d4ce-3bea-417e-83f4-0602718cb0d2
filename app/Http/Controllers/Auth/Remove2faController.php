<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;

class Remove2faController extends Controller
{
    function remove() {
        // todo: allow admin users to remove it from anyone.
        // for now just remove it from the calling user.
        $user = auth()->user();
        $user->two_factor_secret = null;
        $user->two_factor_recovery_codes = null;
        $user->two_factor_confirmed_at = null;
        $user->save();

        if($user->phone)
        {
            $user->phone->phone_number = null;
            $user->phone->last_verified_at = null;
            $user->phone->save();
        }

        return ['ok' => 'Disabled 2FA'];
    }
}
