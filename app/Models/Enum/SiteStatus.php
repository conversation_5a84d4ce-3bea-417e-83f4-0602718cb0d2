<?php

namespace App\Models\Enum;

use App\Models\Booking;

enum SiteStatus: string
{
    case Pending = 'pending';
    case Online = 'online';
    case Fault = 'fault';
    case Recovered = 'recovered';
    case ForcedOffline = 'forced';

    public function label()
    {
        return match ($this) {
            SiteStatus::Pending => 'Pending Installation',
            SiteStatus::Online => 'Online',
            SiteStatus::Fault => 'Fault',
            SiteStatus::Recovered => 'Automatically Recovered',
            SiteStatus::ForcedOffline => 'Manually Disabled',
        };
    }

    public static function contains($value): self|null
    {
        return self::tryFrom($value);
    }
}
