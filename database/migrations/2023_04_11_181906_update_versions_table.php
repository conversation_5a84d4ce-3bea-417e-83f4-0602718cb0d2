<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('versions', function (Blueprint $table) {
            $table->dropColumn('storage_name');
        });

        Schema::table('versions', function (Blueprint $table) {
            $table->dropColumn('path');
        });

        Schema::table('versions', function (Blueprint $table) {
            $table->json('s3_details')->nullable(); // i don't trust path will always be less than 255, so we'll put it in here with a cached filelist and the names
            /*
             * { path: '', storage_name: '', files: [ ... ]}
             */
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
};
