<?php

namespace App\Console\Commands;

use App\Models\FazztContent;
use App\Models\FazztRequestLog;
use Illuminate\Console\Command;

class FazztCheckDCPIngest extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fazzt:check-dcp-ingest';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check Fazzt server for any Ingest DCP jobs';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        //
        $toCheck = FazztRequestLog::where('method', 'CinemaDCPIngestSubmit')->where('is_completed', false)->first();

        if (!$toCheck) {
            $this->info("Nothing to check.");
            return;
        }

        $response = \App\Fazzt\DCP\Status::exec($toCheck->response_code);

        // check the data, update the request log and/or supplimental data.
        /**
         * array:3 [▼
         * "result" => array:12 [▶
         * "PackageFile" => "C:\Program Files\KenCast\Fazzt\cache\CinemaDCP\bf67d64f-d48b-4aa9-9f72-921d138b9033\DCP_bf67d64f-d48b-4aa9-9f72-921d138b9033.fpk"
         * "AssetUUID" => "bf67d64f-d48b-4aa9-9f72-921d138b9033"
         * "AssetMapFile" => "c:\DCPs\DCDC-ROCK-OF-AGES_TST_S_EN-XX_US-GB_51_2K_WR_20130417_DLA_OV/ASSETMAP"
         * "EntryID" => "9C46B2F2-D7DA-4C2E-856A-65640373793B"
         * "ContentType" => "DCP"
         * "Message" => "Successfully package files."
         * "Percent" => 0
         * "Status" => "SUCCESS"
         * "Result" => array:1 [▶
         * "Error" => ""
         * ]
         * "State" => "PACKAGE_FILES"
         * "Estimate_Size" => 1996331706
         * ]
         * "error" => null
         * "id" => 1720709062
         * ]
         */

        // todo: update the package file URL that we store, we'll need to use this for content
        // management and transfers.

        if (empty($response['result']) && is_null($response['error'])) {
            $this->info("Empty Response.");
            $toCheck->is_completed = true;
        } else {
            $data = $response['result'];

            if ($data['Status'] === 'FAILURE') {
                // log some type of error/issue.
                $this->error("Failed: " . $data['Message']);
                $toCheck->is_completed = true;

            } else if ($data["Status"] == "PROCESSING") {
                $this->info("still processing. {$data['Percent']}%");
//                dump($data);
            } else {

                $dataset = [
                    'package_file' => $data['PackageFile'],
                    'package_size' => $data['Estimate_Size'],
                    'status' => $data['Status'],
                    'message' => $data['Message'],
                    'error' => $data['Result']['Error'],
                ];

                $this->info("Creating Fazzt Content Record: " . json_encode($dataset));

                $fazztContent = FazztContent::firstOrCreate(
                    ['asset_uuid' => $data['AssetUUID']], $dataset);

                if (!$fazztContent->wasRecentlyCreated) {
                    $this->info("Content existed, so we'll update the data.");
                    $fazztContent->update($dataset);
                }
                $toCheck->is_completed = true;
            }
            // if result -> status is success or failure it's complete
            /* create a fazzt content record with
            * "PackageFile" => "C:\Program Files\KenCast\Fazzt\cache\CinemaDCP\bf67d64f-d48b-4aa9-9f72-921d138b9033\DCP_bf67d64f-d48b-4aa9-9f72-921d138b9033.fpk"
            * "AssetUUID" => "bf67d64f-d48b-4aa9-9f72-921d138b9033"
            * "Status" => "SUCCESS"
            */
            $this->info("Done.");
        }

        $toCheck->save();

        return Command::SUCCESS;

    }
}
