<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Storage;
use App\Models\AsperaTransfer;

class CheckForDeluxeContent extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'deluxe:check';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check for inbound Deluxe content';

    const BUCKET = 's3-deluxe';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // check the bucket root directories
        $disk = Storage::disk(self::BUCKET);

        // all root directories of this bucket.
        $dirs = $disk->directories();

        // check for a fake 'aspera_transfer' record that references the directory as transfer_spec
        foreach ($dirs as $dir) {
            $this->info("Checking Top Level S3 Folder: $dir");

            /** @var AsperaTransfer $at */
            $at = AsperaTransfer::whereJsonContains('transfer_spec->path', $dir)->withTrashed()->first();
            if (!$at) {
                $this->info("-- Creating Transfer Record for new folder: $dir");
                $at = AsperaTransfer::create([
                    'transfer_uuid' => \Illuminate\Support\Str::uuid()->toString(),
                    'direction' => 'upload',
                    'status' => 'pending',
                    'transfer_spec' => [
                        'bucket' => self::BUCKET,
                        'root' => '/',
                        'path' => $dir,
                        'contents' => [],
                        'content_updated' => now(),
                    ],
                    'aspera_transfer_data' => [],
                ]);
            }
            if ($at->trashed()) {
                $this->info("-- This transfer folder is flagged as deleted. Skipping.");
                continue;
            }
            if ($at->status == 'completed') {
                $this->info("-- This transfer folder is flagged as completed.");
                continue;
            }
            // check and see if the contents have changed in the folder since last check
            $contents = $disk->allFiles($dir);

            $oldContents = $at->transfer_spec['contents'] ?? [];
            $lastUpdate = isset($at->transfer_spec['content_updated']) ? Carbon::create($at->transfer_spec['content_updated']) : now();

            // when we determine the contents have settled?
            if ($oldContents != $contents || !isset($at->transfer_spec['content_updated'])) {
                $this->info('-- Updated contents');
                $at->transfer_spec = [
                    'bucket' => self::BUCKET,
                    'root' => '/',
                    'path' => $dir,
                    'contents' => $contents,
                    'content_updated' => now(),
                ];

                $at->save();
            }
            else {
                $this->info('-- Contents unchanged from last update.');

                // have they been unchanged for an hour?
                if ($lastUpdate->addHour() < now()) {
                    $this->info("-- An hour has passed since last data update, will flag as completed.");
                    // build out the aspera_transfer_data file list for compatability
                    $files = [];                    /*
                     * {
                        "path": "/aspera-uploads/212f7768-2545-4aa1-a362-f2c2345ec9a9/BigBuckBunny_SHR_F_EN-XX_CA_51_2K_20190401_BIT_IOP_OV/.DS_Store",
                        "size": 6148,
                        "bytes_written": 6148
                    }, */

                    foreach ($contents as $file) {
                        $size = $disk->size($file);
                        $fileObject = new \stdClass();
                        $fileObject->size = $size;
                        $fileObject->bytes_written = $size;
                        $fileObject->path = $file;
                        $files[] = $fileObject;
                    }

                    $at->transfer_spec = [
                        'bucket' => self::BUCKET,
                        'root' => '/',
                        'path' => $dir,
                        'contents' => $contents,
                    ];

                    // this is used for the splitter utility post aspera upload.
                    $at->aspera_transfer_data = [
                        'files' => $files,
                    ];

                    // flag it as completed
                    $at->status = 'completed';

                    $at->save();
                    // Once completed, it is in the Title Content UI for Assignment to any title.
                    // After being assigned to a title in the UI, it will be treated as a full upload,
                    // split and checked if required.
                }
            }
        }
        $this->info("Done.");
        return Command::SUCCESS;
    }
}
