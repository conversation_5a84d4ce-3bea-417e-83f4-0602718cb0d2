<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('media_managers', function (Blueprint $table) {
            $table->foreignId('cinema_site_id')->nullable();
            $table->boolean('is_primary')->default(0);
        });

        // any cinema_site with a csx_serial_number needs to have it's ID placed into
        // that media manager's cinema_site_id value and is_primary = 1.
        $sites = \App\Models\CinemaSite::whereNotNull('csx_serial_number')->get();
        foreach ($sites as $site) {
            $mm = \App\Models\MediaManager::where('serial_number', $site->csx_serial_number)->first();
            if ($mm) {
                $mm->update(['cinema_site_id' => $site->id, 'is_primary' => 1]);
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('media_managers', function (Blueprint $table) {
            $table->dropColumn('cinema_site_id');
            $table->dropColumn('is_primary');
        });
    }
};
