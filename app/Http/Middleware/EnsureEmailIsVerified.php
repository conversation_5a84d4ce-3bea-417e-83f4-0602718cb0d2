<?php

namespace App\Http\Middleware;

use App\Models\User;
use Closure;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Support\Carbon;
use Spatie\Activitylog\Models\Activity;
use Symfony\Component\HttpFoundation\Response;

class EnsureEmailIsVerified
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @param string|null $redirectToRoute
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse|null
     */
    public function handle($request, Closure $next, $redirectToRoute = null)
    {
        if (!$request->user() ||
            ($request->user() instanceof MustVerifyEmail &&
                !$request->user()->hasVerifiedEmail())) {
            return response()->json(['message' => 'Your email address is not verified.'], Response::HTTP_CONFLICT);
        }

        if ($request->user()->status === 'inactive') {
            return response()->json(['message' => 'Your account is inactive.'], Response::HTTP_FORBIDDEN);
        }

        // the user has passed through the verification gateway, let's update their daily dashboard/sign in/activity log.
        // cache it until tomorrow!
        cache()->remember('user_verif_check_' . $request->user()->id, now()->secondsUntilEndOfDay(), function () use ($request) {
            $verifiedRecord = $request->user()->getLastVerifiedEvent();
            if (!$verifiedRecord) {
                $request->user()->activityWithIp('Verified Visit', 'verified');
            }

            // update the user's status once time only.
            if ($request->user()->status === 'pending' && $request->user()->email_verified_at !== null) {
                $request->user()->status = 'active';
                $request->user()->save();
            }

            return true;
        });


        return $next($request);
    }
}
