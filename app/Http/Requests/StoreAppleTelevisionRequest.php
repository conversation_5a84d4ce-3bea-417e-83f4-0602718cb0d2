<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreAppleTelevisionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return false; // not alloewd via API front-end intentionally. apple tvs are created when they call home.
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'serial_number' => ['required', 'string'],
            'organization_id' => ['sometimes', 'integer'],
            'cinema_site_id' => ['sometimes', 'integer'],
        ];
    }
}
