<?php

namespace Tests\Feature\Http\Controllers;

use App\Models\Organization;
use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Event;
use Symfony\Component\HttpFoundation\Response;
use Tests\TestCase;

class UserControllerTest extends TestCase
{

    public function test_it_gets_an_authed_user()
    {
        $user = User::factory()->create()->assignRole('admin');
        $organization = Organization::factory()->withOwner($user)->create();
        $organization->users()->save($user);

        $response = $this->actingAs($user)->getJson('/api/user');
        $response->assertSuccessful();
        $response->assertJsonStructure(['all_roles', 'all_permissions']);
    }

    public function test_admin_can_access_user_list()
    {
        $user = User::factory()->create()->assignRole('admin');
        $response = $this->actingAs($user)->getJson(route('users.index'));
        $response->assertSuccessful();
    }

    public function test_unauthorized_attempt_to_list_users()
    {
        $response = $this->getJson(route('users.index'));
        $response->assertStatus(Response::HTTP_UNAUTHORIZED);
    }

    public function test_user_can_list_own_org_users()
    {
        $user = User::factory()->create();
        $response = $this->actingAs($user)->getJson(route('users.index'));
        $response->assertSuccessful();
    }

    public function test_forbidden_attempt_to_see_another_user()
    {
        $user = User::factory()->create();
        $user2 = User::factory()->create();
        $response = $this->actingAs($user)->getJson(route('users.show', $user2));
        $response->assertStatus(Response::HTTP_FORBIDDEN);
    }

    public function test_can_see_own_profile()
    {
        $user = User::factory()->create();
        $response = $this->actingAs($user)->getJson(route('users.show', $user));
        $response->assertSuccessful();
    }

    public function test_can_edit_own_profile()
    {
        $user = User::factory()->create();
        $response = $this->actingAs($user)->putJson(route('users.update', $user), ['name' => 'new']);
        $response->assertSuccessful();
    }

    public function test_non_admin_forbidden_to_create_users()
    {
        $user = User::factory()->create();
        $response = $this->actingAs($user)->postJson(route('users.store'), ['name']);
        $response->assertForbidden();
    }

    public function test_admin_can_create_users_without_password()
    {
        $user = User::factory()->create()->assignRole('admin');
        $organization = Organization::factory()->withOwner($user)->create();
        $organization->users()->save($user);

        $newUser = User::factory()->make();

        $newUser = $newUser->toArray();
        unset($newUser['password']);
        unset($newUser['password_confirmation']);
        unset($newUser['role']);

        $response = $this->actingAs($user)->postJson(route('users.store'), $newUser);
        $response->assertSuccessful();
    }

    public function test_admin_cannot_create_invalid_users()
    {
        $user = User::factory()->create()->assignRole('admin');
        $newUser = User::factory()->make();

        $newUser = $newUser->toArray();
        $newUser['password'] = 'xxx';
        $newUser['password_confirmation'] = 'passwaaaord';

        $response = $this->actingAs($user)->postJson(route('users.store'), $newUser);
        $response->assertUnprocessable();
    }

    public function test_admin_can_delete_user()
    {
        $user = User::factory()->create()->assignRole('admin');
        $newUser = User::factory()->create();
        $response = $this->actingAs($user)->deleteJson(route('users.destroy', $newUser));
        $response->assertSuccessful();
    }

    public function test_admin_cannot_delete_self()
    {
        $user = User::factory()->create()->assignRole('admin');
        $response = $this->actingAs($user)->deleteJson(route('users.destroy', $user));
        $response->assertUnprocessable();
    }

    public function test_admin_can_update_users()
    {
        $user = User::factory()->create()->assignRole('admin');
        $newUser = User::factory()->create();

        $newData = $newUser->toArray();
        $newData['password'] = 'password$1!@A';
        $newData['password_confirmation'] = 'password$1!@A';
        unset($newData['role']);

        $response = $this->actingAs($user)->putJson(route('users.update', $newUser), $newData);
        $response->assertSuccessful();
    }

    public function test_admin_cannot_restore_user()
    {
        $user = User::factory()->create()->assignRole('admin');
        $newUser = User::factory()->create()->delete();
        $response = $this->actingAs($user)->getJson(route('users.restore', $newUser));
        $response->assertForbidden();
    }

    public function test_super_admin_can_restore_user()
    {
        $user = User::factory()->create()->assignRole('super-admin');
        $newUser = User::factory()->create()->delete();
        $response = $this->actingAs($user)->getJson(route('users.restore', $newUser));
        $response->assertSuccessful();
    }


    public function test_user_can_update_own_profile()
    {
        $user = User::factory()->create();

        $password = 'aBcD123$!@';
        $newData = ['name' => 'new name', 'password' => $password, 'password_confirmation' => $password];

        $response = $this->actingAs($user)->putJson(route('users.update', $user), $newData);
        $response->assertSuccessful();

    }

    public function test_user_cannot_change_own_role()
    {
        $user = User::factory()->create();

        $newData = ['name' => 'new name', 'password' => 'new_password', 'password_confirmation' => 'new_password',
            'role' => 'anything'];

        $response = $this->actingAs($user)->putJson(route('users.update', $user), $newData);
        $response->assertStatus(422);

    }

    public function test_user_cannot_update_another_profile()
    {
        $user = User::factory()->create();
        $anotherUser = User::factory()->create();
        $newData = ['name' => 'new name', 'password' => 'new_password', 'password_confirmation' => 'new_password'];

        $response = $this->actingAs($user)->getJson(route('users.update', $anotherUser), $newData);
        $response->assertForbidden();

    }

    public function test_admin_can_resend_invite()
    {
        $admin = User::factory()->create()->assignRole('admin');
        Event::fake();

        $newUser = User::factory()->create();
        $response = $this->actingAs($admin)->getJson(route('users.resend', $newUser));

        Event::assertDispatched(Registered::class);
        $response->assertSuccessful();
    }

    public function test_user_cannot_resend_invite()
    {
        $user = User::factory()->create();
        Event::fake();

        $newUser = User::factory()->create();
        $response = $this->actingAs($user)->getJson(route('users.resend', $newUser));

        Event::assertNotDispatched(Registered::class);
        $response->assertSuccessful();
    }

}
