<?php

namespace Tests\Feature\Auth;

use App\Models\User;
use Tests\TestCase;

class TwoFactorTest extends TestCase
{

    public function test_disabled_2fa_does_not_get_qr_code()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->getJson('/user/two-factor-qr-code');
        $response->assertSuccessful();
        $response->assertJson([]);
    }

    public function test_enable_2fa_gets_qr_code()
    {
        $user = User::factory()->create();
        $user->two_factor_secret = encrypt('xyz'); // not really a code, but doesn't matter.
        $user->save();

        $response = $this->actingAs($user)->getJson('/user/two-factor-qr-code');
        $response->assertSuccessful();
        $response->assertJsonStructure(['svg', 'url']);
    }

    public function test_users_requires_2fa_confirmation()
    {
        $user = User::factory()->create(['two_factor_secret' => 'xyz', 'two_factor_confirmed_at' => now()]);

        $response = $this->postJson('/login', [
            'email' => $user->email,
            'password' => 'testing123',
        ]);

        $response->assertSuccessful();
        $response->assertJson(['two_factor' => true]);
    }

    public function test_user_enables_2fa()
    {
        $user = User::factory()->create();
        $response = $this->actingAs($user)->postJson(route('two-factor.enable'));

        $response->assertSuccessful();
        $user->refresh();
        $this->assertNotNull($user->two_factor_secret);
        $this->assertNull($user->two_factor_confirmed_at);
    }

    public function test_user_disables_2fa()
    {
        $user = User::factory()->create(['two_factor_secret' => 'xyz', 'two_factor_confirmed_at' => now()]);

        $response = $this->actingAs($user)->deleteJson(route('two-factor.disable'));

        $response->assertSuccessful();
        $user->refresh();
        $this->assertNull($user->two_factor_secret);
    }

}
