<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1653502479">
  <project timestamp="1653502479">
    <file name="/home/<USER>/work/dcdc-api/dcdc-api/app/Actions/Fortify/CreateNewUser.php">
      <class name="App\Actions\Fortify\CreateNewUser" namespace="global">
        <metrics complexity="1" methods="1" coveredmethods="1" conditionals="0" coveredconditionals="0" statements="5" coveredstatements="5" elements="6" coveredelements="6"/>
      </class>
      <line num="21" type="method" name="create" visibility="public" complexity="1" crap="1" count="1"/>
      <line num="23" type="stmt" count="1"/>
      <line num="25" type="stmt" count="1"/>
      <line num="26" type="stmt" count="1"/>
      <line num="27" type="stmt" count="1"/>
      <line num="28" type="stmt" count="1"/>
      <metrics loc="32" ncloc="26" classes="1" methods="1" coveredmethods="1" conditionals="0" coveredconditionals="0" statements="5" coveredstatements="5" elements="6" coveredelements="6"/>
    </file>
    <file name="/home/<USER>/work/dcdc-api/dcdc-api/app/Actions/Fortify/PasswordValidationRules.php">
      <class name="App\Actions\Fortify\PasswordValidationRules" namespace="global">
        <metrics complexity="1" methods="1" coveredmethods="1" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="1" elements="2" coveredelements="2"/>
      </class>
      <line num="14" type="method" name="passwordRules" visibility="protected" complexity="1" crap="1" count="4"/>
      <line num="16" type="stmt" count="4"/>
      <metrics loc="19" ncloc="14" classes="1" methods="1" coveredmethods="1" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="1" elements="2" coveredelements="2"/>
    </file>
    <file name="/home/<USER>/work/dcdc-api/dcdc-api/app/Actions/Fortify/ResetUserPassword.php">
      <class name="App\Actions\Fortify\ResetUserPassword" namespace="global">
        <metrics complexity="1" methods="1" coveredmethods="1" conditionals="0" coveredconditionals="0" statements="6" coveredstatements="6" elements="7" coveredelements="7"/>
      </class>
      <line num="20" type="method" name="reset" visibility="public" complexity="1" crap="1" count="1"/>
      <line num="22" type="stmt" count="1"/>
      <line num="23" type="stmt" count="1"/>
      <line num="24" type="stmt" count="1"/>
      <line num="26" type="stmt" count="1"/>
      <line num="27" type="stmt" count="1"/>
      <line num="28" type="stmt" count="1"/>
      <metrics loc="31" ncloc="24" classes="1" methods="1" coveredmethods="1" conditionals="0" coveredconditionals="0" statements="6" coveredstatements="6" elements="7" coveredelements="7"/>
    </file>
    <file name="/home/<USER>/work/dcdc-api/dcdc-api/app/Actions/Fortify/UpdateUserPassword.php">
      <class name="App\Actions\Fortify\UpdateUserPassword" namespace="global">
        <metrics complexity="3" methods="1" coveredmethods="1" conditionals="0" coveredconditionals="0" statements="11" coveredstatements="11" elements="12" coveredelements="12"/>
      </class>
      <line num="20" type="method" name="update" visibility="public" complexity="3" crap="3" count="3"/>
      <line num="22" type="stmt" count="3"/>
      <line num="23" type="stmt" count="3"/>
      <line num="24" type="stmt" count="3"/>
      <line num="25" type="stmt" count="3"/>
      <line num="26" type="stmt" count="3"/>
      <line num="27" type="stmt" count="1"/>
      <line num="28" type="stmt" count="1"/>
      <line num="30" type="stmt" count="3"/>
      <line num="32" type="stmt" count="1"/>
      <line num="33" type="stmt" count="1"/>
      <line num="34" type="stmt" count="1"/>
      <metrics loc="37" ncloc="30" classes="1" methods="1" coveredmethods="1" conditionals="0" coveredconditionals="0" statements="11" coveredstatements="11" elements="12" coveredelements="12"/>
    </file>
    <file name="/home/<USER>/work/dcdc-api/dcdc-api/app/Actions/Fortify/UpdateUserProfileInformation.php">
      <class name="App\Actions\Fortify\UpdateUserProfileInformation" namespace="global">
        <metrics complexity="4" methods="2" coveredmethods="2" conditionals="0" coveredconditionals="0" statements="16" coveredstatements="16" elements="18" coveredelements="18"/>
      </class>
      <line num="19" type="method" name="update" visibility="public" complexity="3" crap="3" count="2"/>
      <line num="21" type="stmt" count="2"/>
      <line num="22" type="stmt" count="2"/>
      <line num="25" type="stmt" count="2"/>
      <line num="29" type="stmt" count="2"/>
      <line num="31" type="stmt" count="2"/>
      <line num="33" type="stmt" count="2"/>
      <line num="35" type="stmt" count="1"/>
      <line num="38" type="stmt" count="1"/>
      <line num="39" type="stmt" count="1"/>
      <line num="40" type="stmt" count="1"/>
      <line num="41" type="stmt" count="1"/>
      <line num="52" type="method" name="updateVerifiedUser" visibility="protected" complexity="1" crap="1" count="1"/>
      <line num="54" type="stmt" count="1"/>
      <line num="55" type="stmt" count="1"/>
      <line num="56" type="stmt" count="1"/>
      <line num="58" type="stmt" count="1"/>
      <line num="60" type="stmt" count="1"/>
      <metrics loc="63" ncloc="49" classes="1" methods="2" coveredmethods="2" conditionals="0" coveredconditionals="0" statements="16" coveredstatements="16" elements="18" coveredelements="18"/>
    </file>
    <file name="/home/<USER>/work/dcdc-api/dcdc-api/app/Console/Kernel.php">
      <class name="App\Console\Kernel" namespace="global">
        <metrics complexity="2" methods="1" coveredmethods="1" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="2" elements="3" coveredelements="3"/>
      </class>
      <line num="26" type="method" name="commands" visibility="protected" complexity="1" crap="1" count="30"/>
      <line num="28" type="stmt" count="30"/>
      <line num="30" type="stmt" count="30"/>
      <metrics loc="33" ncloc="21" classes="1" methods="1" coveredmethods="1" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="2" elements="3" coveredelements="3"/>
    </file>
    <file name="/home/<USER>/work/dcdc-api/dcdc-api/app/Exceptions/Handler.php">
      <class name="App\Exceptions\Handler" namespace="global">
        <metrics complexity="1" methods="1" coveredmethods="1" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="1" elements="2" coveredelements="2"/>
      </class>
      <line num="44" type="method" name="register" visibility="public" complexity="1" crap="1" count="30"/>
      <line num="46" type="stmt" count="30"/>
      <metrics loc="51" ncloc="30" classes="1" methods="1" coveredmethods="1" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="1" elements="2" coveredelements="2"/>
    </file>
    <file name="/home/<USER>/work/dcdc-api/dcdc-api/app/Http/Controllers/Controller.php">
      <class name="App\Http\Controllers\Controller" namespace="global">
        <metrics complexity="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
      </class>
      <metrics loc="14" ncloc="14" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/home/<USER>/work/dcdc-api/dcdc-api/app/Http/Controllers/UserController.php">
      <class name="App\Http\Controllers\UserController" namespace="global">
        <metrics complexity="8" methods="7" coveredmethods="7" conditionals="0" coveredconditionals="0" statements="15" coveredstatements="15" elements="22" coveredelements="22"/>
      </class>
      <line num="12" type="method" name="__construct" visibility="public" complexity="1" crap="1" count="13"/>
      <line num="14" type="stmt" count="13"/>
      <line num="22" type="method" name="index" visibility="public" complexity="1" crap="1" count="1"/>
      <line num="25" type="stmt" count="1"/>
      <line num="34" type="method" name="store" visibility="public" complexity="1" crap="1" count="1"/>
      <line num="37" type="stmt" count="1"/>
      <line num="40" type="stmt" count="1"/>
      <line num="42" type="stmt" count="1"/>
      <line num="52" type="method" name="show" visibility="public" complexity="1" crap="1" count="1"/>
      <line num="54" type="stmt" count="1"/>
      <line num="65" type="method" name="update" visibility="public" complexity="1" crap="1" count="2"/>
      <line num="68" type="stmt" count="2"/>
      <line num="71" type="stmt" count="2"/>
      <line num="73" type="stmt" count="2"/>
      <line num="84" type="method" name="destroy" visibility="public" complexity="2" crap="2" count="2"/>
      <line num="86" type="stmt" count="2"/>
      <line num="87" type="stmt" count="1"/>
      <line num="90" type="stmt" count="1"/>
      <line num="93" type="method" name="restore" visibility="public" complexity="1" crap="1" count="1"/>
      <line num="96" type="stmt" count="1"/>
      <line num="97" type="stmt" count="1"/>
      <line num="98" type="stmt" count="1"/>
      <metrics loc="102" ncloc="66" classes="1" methods="7" coveredmethods="7" conditionals="0" coveredconditionals="0" statements="15" coveredstatements="15" elements="22" coveredelements="22"/>
    </file>
    <file name="/home/<USER>/work/dcdc-api/dcdc-api/app/Http/Kernel.php">
      <class name="App\Http\Kernel" namespace="global">
        <metrics complexity="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
      </class>
      <metrics loc="68" ncloc="49" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/home/<USER>/work/dcdc-api/dcdc-api/app/Http/Middleware/Authenticate.php">
      <class name="App\Http\Middleware\Authenticate" namespace="global">
        <metrics complexity="2" methods="1" coveredmethods="1" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="2" elements="3" coveredelements="3"/>
      </class>
      <line num="15" type="method" name="redirectTo" visibility="protected" complexity="2" crap="2" count="3"/>
      <line num="17" type="stmt" count="3"/>
      <line num="18" type="stmt" count="1"/>
      <metrics loc="22" ncloc="16" classes="1" methods="1" coveredmethods="1" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="2" elements="3" coveredelements="3"/>
    </file>
    <file name="/home/<USER>/work/dcdc-api/dcdc-api/app/Http/Middleware/EncryptCookies.php">
      <class name="App\Http\Middleware\EncryptCookies" namespace="global">
        <metrics complexity="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
      </class>
      <metrics loc="18" ncloc="13" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/home/<USER>/work/dcdc-api/dcdc-api/app/Http/Middleware/EnsureEmailIsVerified.php">
      <class name="App\Http\Middleware\EnsureEmailIsVerified" namespace="global">
        <metrics complexity="4" methods="1" coveredmethods="1" conditionals="0" coveredconditionals="0" statements="5" coveredstatements="5" elements="6" coveredelements="6"/>
      </class>
      <line num="18" type="method" name="handle" visibility="public" complexity="4" crap="4" count="14"/>
      <line num="20" type="stmt" count="14"/>
      <line num="21" type="stmt" count="14"/>
      <line num="22" type="stmt" count="14"/>
      <line num="23" type="stmt" count="1"/>
      <line num="26" type="stmt" count="13"/>
      <metrics loc="29" ncloc="21" classes="1" methods="1" coveredmethods="1" conditionals="0" coveredconditionals="0" statements="5" coveredstatements="5" elements="6" coveredelements="6"/>
    </file>
    <file name="/home/<USER>/work/dcdc-api/dcdc-api/app/Http/Middleware/PreventRequestsDuringMaintenance.php">
      <class name="App\Http\Middleware\PreventRequestsDuringMaintenance" namespace="global">
        <metrics complexity="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
      </class>
      <metrics loc="18" ncloc="13" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/home/<USER>/work/dcdc-api/dcdc-api/app/Http/Middleware/RedirectIfAuthenticated.php">
      <class name="App\Http\Middleware\RedirectIfAuthenticated" namespace="global">
        <metrics complexity="4" methods="1" coveredmethods="1" conditionals="0" coveredconditionals="0" statements="4" coveredstatements="4" elements="5" coveredelements="5"/>
      </class>
      <line num="20" type="method" name="handle" visibility="public" complexity="4" crap="4" count="5"/>
      <line num="22" type="stmt" count="5"/>
      <line num="24" type="stmt" count="5"/>
      <line num="25" type="stmt" count="5"/>
      <line num="30" type="stmt" count="5"/>
      <metrics loc="33" ncloc="24" classes="1" methods="1" coveredmethods="1" conditionals="0" coveredconditionals="0" statements="4" coveredstatements="4" elements="5" coveredelements="5"/>
    </file>
    <file name="/home/<USER>/work/dcdc-api/dcdc-api/app/Http/Middleware/TrimStrings.php">
      <class name="App\Http\Middleware\TrimStrings" namespace="global">
        <metrics complexity="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
      </class>
      <metrics loc="20" ncloc="15" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/home/<USER>/work/dcdc-api/dcdc-api/app/Http/Middleware/TrustHosts.php">
      <class name="App\Http\Middleware\TrustHosts" namespace="global">
        <metrics complexity="1" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
      </class>
      <metrics loc="22" ncloc="16" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/home/<USER>/work/dcdc-api/dcdc-api/app/Http/Middleware/TrustProxies.php">
      <class name="App\Http\Middleware\TrustProxies" namespace="global">
        <metrics complexity="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
      </class>
      <metrics loc="29" ncloc="19" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/home/<USER>/work/dcdc-api/dcdc-api/app/Http/Middleware/VerifyCsrfToken.php">
      <class name="App\Http\Middleware\VerifyCsrfToken" namespace="global">
        <metrics complexity="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
      </class>
      <metrics loc="21" ncloc="16" classes="0" methods="0" coveredmethods="0" conditionals="0" coveredconditionals="0" statements="0" coveredstatements="0" elements="0" coveredelements="0"/>
    </file>
    <file name="/home/<USER>/work/dcdc-api/dcdc-api/app/Http/Requests/StoreUserRequest.php">
      <class name="App\Http\Requests\StoreUserRequest" namespace="global">
        <metrics complexity="2" methods="2" coveredmethods="2" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="2" elements="4" coveredelements="4"/>
      </class>
      <line num="15" type="method" name="authorize" visibility="public" complexity="1" crap="1" count="2"/>
      <line num="17" type="stmt" count="2"/>
      <line num="25" type="method" name="rules" visibility="public" complexity="1" crap="1" count="2"/>
      <line num="27" type="stmt" count="2"/>
      <metrics loc="30" ncloc="20" classes="1" methods="2" coveredmethods="2" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="2" elements="4" coveredelements="4"/>
    </file>
    <file name="/home/<USER>/work/dcdc-api/dcdc-api/app/Http/Requests/UpdateUserRequest.php">
      <class name="App\Http\Requests\UpdateUserRequest" namespace="global">
        <metrics complexity="3" methods="2" coveredmethods="2" conditionals="0" coveredconditionals="0" statements="4" coveredstatements="4" elements="6" coveredelements="6"/>
      </class>
      <line num="17" type="method" name="authorize" visibility="public" complexity="2" crap="2" count="2"/>
      <line num="20" type="stmt" count="2"/>
      <line num="21" type="stmt" count="2"/>
      <line num="29" type="method" name="rules" visibility="public" complexity="1" crap="1" count="2"/>
      <line num="31" type="stmt" count="2"/>
      <line num="32" type="stmt" count="2"/>
      <metrics loc="35" ncloc="24" classes="1" methods="2" coveredmethods="2" conditionals="0" coveredconditionals="0" statements="4" coveredstatements="4" elements="6" coveredelements="6"/>
    </file>
    <file name="/home/<USER>/work/dcdc-api/dcdc-api/app/Models/User.php">
      <class name="App\Models\User" namespace="global">
        <metrics complexity="5" methods="3" coveredmethods="3" conditionals="0" coveredconditionals="0" statements="10" coveredstatements="10" elements="13" coveredelements="13"/>
      </class>
      <line num="58" type="method" name="validationRules" visibility="public" complexity="2" crap="2" count="5"/>
      <line num="60" type="stmt" count="5"/>
      <line num="63" type="stmt" count="2"/>
      <line num="64" type="stmt" count="2"/>
      <line num="68" type="stmt" count="3"/>
      <line num="69" type="stmt" count="3"/>
      <line num="73" type="stmt" count="5"/>
      <line num="75" type="stmt" count="5"/>
      <line num="81" type="stmt" count="5"/>
      <line num="88" type="method" name="isSuperAdmin" visibility="public" complexity="1" crap="1" count="1"/>
      <line num="90" type="stmt" count="1"/>
      <line num="93" type="method" name="isAdmin" visibility="public" complexity="2" crap="2" count="11"/>
      <line num="95" type="stmt" count="11"/>
      <metrics loc="99" ncloc="74" classes="1" methods="3" coveredmethods="3" conditionals="0" coveredconditionals="0" statements="10" coveredstatements="10" elements="13" coveredelements="13"/>
    </file>
    <file name="/home/<USER>/work/dcdc-api/dcdc-api/app/Policies/UserPolicy.php">
      <class name="App\Policies\UserPolicy" namespace="global">
        <metrics complexity="7" methods="5" coveredmethods="5" conditionals="0" coveredconditionals="0" statements="5" coveredstatements="5" elements="10" coveredelements="10"/>
      </class>
      <line num="18" type="method" name="viewAny" visibility="public" complexity="1" crap="1" count="2"/>
      <line num="20" type="stmt" count="2"/>
      <line num="30" type="method" name="view" visibility="public" complexity="2" crap="2" count="2"/>
      <line num="32" type="stmt" count="2"/>
      <line num="41" type="method" name="create" visibility="public" complexity="1" crap="1" count="3"/>
      <line num="43" type="stmt" count="3"/>
      <line num="53" type="method" name="update" visibility="public" complexity="2" crap="2" count="2"/>
      <line num="55" type="stmt" count="2"/>
      <line num="65" type="method" name="delete" visibility="public" complexity="1" crap="1" count="2"/>
      <line num="67" type="stmt" count="2"/>
      <metrics loc="71" ncloc="38" classes="1" methods="5" coveredmethods="5" conditionals="0" coveredconditionals="0" statements="5" coveredstatements="5" elements="10" coveredelements="10"/>
    </file>
    <file name="/home/<USER>/work/dcdc-api/dcdc-api/app/Providers/AppServiceProvider.php">
      <class name="App\Providers\AppServiceProvider" namespace="global">
        <metrics complexity="2" methods="1" coveredmethods="1" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="2" elements="3" coveredelements="3"/>
      </class>
      <line num="26" type="method" name="boot" visibility="public" complexity="1" crap="1" count="30"/>
      <line num="29" type="stmt" count="30"/>
      <line num="30" type="stmt" count="1"/>
      <metrics loc="34" ncloc="22" classes="1" methods="1" coveredmethods="1" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="2" elements="3" coveredelements="3"/>
    </file>
    <file name="/home/<USER>/work/dcdc-api/dcdc-api/app/Providers/AuthServiceProvider.php">
      <class name="App\Providers\AuthServiceProvider" namespace="global">
        <metrics complexity="1" methods="1" coveredmethods="1" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="1" elements="2" coveredelements="2"/>
      </class>
      <line num="24" type="method" name="boot" visibility="public" complexity="1" crap="1" count="30"/>
      <line num="26" type="stmt" count="30"/>
      <metrics loc="30" ncloc="19" classes="1" methods="1" coveredmethods="1" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="1" elements="2" coveredelements="2"/>
    </file>
    <file name="/home/<USER>/work/dcdc-api/dcdc-api/app/Providers/BroadcastServiceProvider.php">
      <class name="App\Providers\BroadcastServiceProvider" namespace="global">
        <metrics complexity="1" methods="1" coveredmethods="1" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="2" elements="3" coveredelements="3"/>
      </class>
      <line num="15" type="method" name="boot" visibility="public" complexity="1" crap="1" count="30"/>
      <line num="17" type="stmt" count="30"/>
      <line num="18" type="stmt" count="30"/>
      <metrics loc="21" ncloc="16" classes="1" methods="1" coveredmethods="1" conditionals="0" coveredconditionals="0" statements="2" coveredstatements="2" elements="3" coveredelements="3"/>
    </file>
    <file name="/home/<USER>/work/dcdc-api/dcdc-api/app/Providers/EventServiceProvider.php">
      <class name="App\Providers\EventServiceProvider" namespace="global">
        <metrics complexity="2" methods="1" coveredmethods="1" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="1" elements="2" coveredelements="2"/>
      </class>
      <line num="38" type="method" name="shouldDiscoverEvents" visibility="public" complexity="1" crap="1" count="30"/>
      <line num="40" type="stmt" count="30"/>
      <metrics loc="43" ncloc="27" classes="1" methods="1" coveredmethods="1" conditionals="0" coveredconditionals="0" statements="1" coveredstatements="1" elements="2" coveredelements="2"/>
    </file>
    <file name="/home/<USER>/work/dcdc-api/dcdc-api/app/Providers/FortifyServiceProvider.php">
      <class name="App\Providers\FortifyServiceProvider" namespace="global">
        <metrics complexity="1" methods="1" coveredmethods="1" conditionals="0" coveredconditionals="0" statements="9" coveredstatements="9" elements="10" coveredelements="10"/>
      </class>
      <line num="24" type="method" name="boot" visibility="public" complexity="1" crap="1" count="30"/>
      <line num="26" type="stmt" count="30"/>
      <line num="27" type="stmt" count="30"/>
      <line num="28" type="stmt" count="30"/>
      <line num="29" type="stmt" count="30"/>
      <line num="31" type="stmt" count="30"/>
      <line num="32" type="stmt" count="1"/>
      <line num="35" type="stmt" count="30"/>
      <line num="36" type="stmt" count="2"/>
      <line num="37" type="stmt" count="2"/>
      <metrics loc="47" ncloc="38" classes="1" methods="1" coveredmethods="1" conditionals="0" coveredconditionals="0" statements="9" coveredstatements="9" elements="10" coveredelements="10"/>
    </file>
    <file name="/home/<USER>/work/dcdc-api/dcdc-api/app/Providers/RouteServiceProvider.php">
      <class name="App\Providers\RouteServiceProvider" namespace="global">
        <metrics complexity="3" methods="2" coveredmethods="2" conditionals="0" coveredconditionals="0" statements="9" coveredstatements="9" elements="11" coveredelements="11"/>
      </class>
      <line num="27" type="method" name="boot" visibility="public" complexity="1" crap="1" count="30"/>
      <line num="29" type="stmt" count="30"/>
      <line num="31" type="stmt" count="30"/>
      <line num="32" type="stmt" count="30"/>
      <line num="33" type="stmt" count="30"/>
      <line num="34" type="stmt" count="30"/>
      <line num="36" type="stmt" count="30"/>
      <line num="37" type="stmt" count="30"/>
      <line num="46" type="method" name="configureRateLimiting" visibility="protected" complexity="2" crap="2" count="30"/>
      <line num="48" type="stmt" count="30"/>
      <line num="49" type="stmt" count="14"/>
      <metrics loc="53" ncloc="36" classes="1" methods="2" coveredmethods="2" conditionals="0" coveredconditionals="0" statements="9" coveredstatements="9" elements="11" coveredelements="11"/>
    </file>
    <metrics files="29" loc="1125" ncloc="790" classes="21" methods="37" coveredmethods="37" conditionals="0" coveredconditionals="0" statements="113" coveredstatements="113" elements="150" coveredelements="150"/>
  </project>
</coverage>
