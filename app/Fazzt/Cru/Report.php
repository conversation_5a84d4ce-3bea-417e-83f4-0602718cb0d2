<?php

namespace App\Fazzt\Cru;

use App\Fazzt\Json;

class Report
{
    static function exec()
    {
        $params = [
            0 => 'cru-report',
            1 => '',
            2 => [
                'OnClientCallback' => config('fazzt.callback_url') . 'cru-report',
                'SiteID' => 909920,
                'QueueName' => 'Channel 1 Preprocess',
                'TransmissionModeName' => 'With Reports Command',
            ],
        ];
        try {
            $response = Json::jsonCall(
                'CinemaCmdSubmit',
                null,
                $params,
                true);

            // dump($response);

            return true;
        }
        catch (\Exception $e) {

        }

        return false;

    }
}
