<?php

namespace Database\Seeders;

use App\Models\Organization;
use Exception;
use Illuminate\Database\Seeder;

class VendorSeeder extends Seeder
{
    public function run()
    {
        try {
            $vendor = \App\Models\User::factory()->create([
                'name' => 'Demo Vendor',
                'email' => '<EMAIL>',
            ]);

            $vendor->assignRole('vendor-read-write');

            $org = Organization::factory()->withOwner($vendor)->create([
                'name' => 'Example Vendor',
                'type' => 'vendor',
            ]);
            $org->users()->save($vendor);
        }
        catch (Exception $e) {
        }
    }
}
