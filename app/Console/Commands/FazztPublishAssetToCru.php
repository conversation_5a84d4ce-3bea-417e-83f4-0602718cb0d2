<?php

namespace App\Console\Commands;

use App\Fazzt\Json;
use Illuminate\Console\Command;

class FazztPublishAssetToCru extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fazzt:publish-cru {uuid}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Publish a DCP by its AssetMap UUID to the CRU drive';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $params = [
            0 => 'cru-sync',
            1 => [$this->argument('uuid')],
            2 => [
                'OnClientCallback' => config('fazzt.callback_url') . 'publish',
                'SiteID' => 909920,
                'QueueName' => 'Channel 1 Preprocess',
                'TransmissionModeName' => 'With Reports Command',
            ]
        ];
        try {
            $response = Json::jsonCall(
                'CinemaCmdSubmit',
                null,
                $params,
                true);
            dump($response);
        }
        catch (\Exception $e) {
            $this->error("Error: " . $e->getMessage());
        }

        return Command::SUCCESS;
    }
}
