<?php

namespace App\Console\Commands;

use App\Jobs\UpdateBookingCounts;
use App\Jobs\UpdateTransferCounts;
use App\Models\Booking;
use Illuminate\Console\Command;

class FillBookingTransfers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fill-booking-transfers {bookingId}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update a booking transfer counts';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        UpdateTransferCounts::dispatch(Booking::findOrFail($this->argument('bookingId')));
        return Command::SUCCESS;
    }
}
