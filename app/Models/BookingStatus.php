<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class BookingStatus extends Model
{
    use SoftDeletes;

    protected $fillable = ['booking_id', 'user_id', 'status'];

    protected $appends = ['label'];

    protected $casts = [
        'status' => Enum\BookingStatus::class,
    ];

    public function booking()
    {
        return $this->belongsTo(Booking::class);
    }

    public function getLabelAttribute()
    {
        // label is derived from the Enum class.
        return $this->status->label();
    }

}
