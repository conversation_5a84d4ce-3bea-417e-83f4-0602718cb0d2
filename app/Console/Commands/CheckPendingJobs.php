<?php

namespace App\Console\Commands;

use App\Models\MediaManagerJob;
use GuzzleHttp\Client;
use Illuminate\Console\Command;

class CheckPendingJobs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:pending-jobs';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This will push along some jobs that might need it.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // check any job that isn't a known 'complete' status, or pending.
        $potentiallyInProgressJobs = MediaManagerJob::whereNotIn('status',
            ['completed', 'error', 'failed', 'stale', 'cleared', 'pending', 'retrying'])->get();
        $pendingJobs = MediaManagerJob::where('status', 'pending')->get();

        $activeMMs = [];

        // jobs that claim they are running:
        /** @var MediaManagerJob $job */
        foreach ($potentiallyInProgressJobs as $job) {

            $this->info("Checking Job ID: {$job->id} with status {$job->status}...");

            // fix these
            if ($job->status == 'Complete') {
                $job->status = "Completed";
                $job->save();
                continue;
            }

            // check cinesend if this job has an ID attached...
            // if cinesend says it's still working, then cool.
            // update the job to match the cinesend status
            if (!$job->checkCinesendJob()) {
                // flag this media manger as having an active job:
                $activeMMs[] = $job->media_manager_id;
            }

        }

        /** @var MediaManagerJob $job */
        foreach ($pendingJobs as $job) {
            $this->info("Media Manager ID: {$job->media_manager_id} -> Status: {$job->status}\n-> Job ID: {$job->id} - {$job->name}");

            if ($job->checkCinesendJob()) {
                $this->warn('-- This job is pending on cinesend, will check again later.');
                continue;
            }

            if (in_array($job->media_manager_id, $activeMMs)) {
                $this->warn('-- This media manager has an active job, will check again next time.');
                continue;
            }

            if (!$job->mediaManager) {
                $this->warn('-- There is no media manager assigned to this old job.');
                $job->status = 'stale';
                $job->save();
                continue;
            }

            if ($job->created_at->isBefore(now()->subDay(3))) {
                $this->warn('-- This Job is too old to send. Please retry manually if it is intended.');
                $job->status = 'stale';
                $job->save();
                continue;
            }

            if ($job->mediaManager->isOnline()) {
                try {
                    if ($job->sendJob()) {
                        $activeMMs[] = $job->media_manager_id;
                        $this->info('-- Sent job!');
                    }
                }
                catch (\Exception $e) {
                    // nothing to worry about here, job will be flagged.
                    $this->error('-- Sending to Media Manager failed - likely a port forward configuration - flagging as error - retry manually if you want.');
                    $job->status = 'error';
                    $job->save();
                }
            }
            else {
                $this->error('-- Media Manager is Offline. Will continue to check it for up to 3 days.');
            }
        }
        return Command::SUCCESS;
    }

}
