<?php

namespace Database\Seeders;

use App\Models\Organization;
use App\Models\Title;
use App\Models\User;
use App\Models\Version;
use Illuminate\Database\Seeder;

class TitleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Generate a user in an Org with Titles and Versions?
        $user = User::factory()->create();
        $user->assignRole('exhibitor-read-write');

        $organization = Organization::factory()
            ->has(Title::factory(1)
                ->state(function (array $attributes, Organization $organization) {
                    return ['creator_id' => $organization->owner_id];
                })
                ->has(Version::factory(5)
                    ->state(function (array $attributes, Title $title) {
                        return ['creator_id' => $title->creator_id];
                    })
                ))
            ->state(function (array $attributes) use ($user) {
                return ['owner_id' => $user->id];
            })
            ->create();

        $organization->users()->save($user);

    }
}
