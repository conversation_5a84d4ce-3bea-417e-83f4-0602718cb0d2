<?php

namespace App\Policies;

use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;
use Laravel\Sanctum\PersonalAccessToken;

class PersonalAccessTokenPolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user)
    {
        return true; // temporary $user->isAdmin();
    }

    public function view(User $user, PersonalAccessToken $token)
    {
        return false;
    }

    public function create(User $user)
    {
        return true;
    }

    public function update(User $user, PersonalAccessToken $token)
    {
        return false;
    }

    public function delete($class, User $user, PersonalAccessToken $token)
    {
        return $user->isAdmin() || $user->tokens->contains($token);
    }

}
