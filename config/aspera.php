<?php

$accountID = "************"; // env('AWS_ACCOUNT_ID');

// Our main Aspera account uses this key for all nodes
$key = "38bb43b8-23ae-4c2a-a915-bd38451ef2ea";

// Role and secret from trust entity
$role = "aspera-node-staging-role";
$secret = "1c00dfa5-1056-40da-b36b-6c0266ebf393";

if (env('APP_ENV') === 'production') {
    $role = "aspera-node-production-role";
    $secret = "177c314d-6130-439d-8cf7-6526e1d268b1";
}

return [
    's3' => [
        'transfer' => [
            'url' => 'ats-aws-us-east-1.aspera.io',
            'port' => '443',
            'host_name' => 'https://ats-aws-us-east-1.aspera.io',
            'transfer_name' => 'https://ats-aws-us-east-1.aspera.io/ops/transfers',
        ],
        'user' => env('ASPERA_USERNAME'),
        'password' => env('ASPERA_PASSWORD'),
    ],
    'ats' => [
        'access_keys_url' => 'https://ats.aspera.io/pub/v1/access_keys',
        'key' => 'ats_NIqVJSOjQWHrzooRYYkSC6mV',
        'secret' => 'PpI4EtPbq3DFp6ya46duS2Bb_jT2rPcXX3uCgSUBgQe2',
        'aws_role_arn' => "arn:aws:iam::{$accountID}:role/{$role}",
        'aspera_external_id' => "{$key}:{$secret}",
        'transfer_server_id' => '6e2c5cc5-6641-4c68-a225-d0f370610d7d',
    ],
    'deluxe' => [ // overrides for aspera downloads of deluxe content.
        'role' => 'aspera-node-dcdc-deluxe-inbound-role',
        'aspera_external_id' => "38bb43b8-23ae-4c2a-a915-bd38451ef2ea:8800798a-6191-4eb0-aaf7-4050c6069a90",
        'bucket' => 'dcdc-deluxe-inbound',
        'key' => 'UK02uEJGwM8FW7xJOXa99MoU',
        'secret' => 'riFgcffNfV6hCarUDs_',
        'aws_role_arn' => "arn:aws:iam::{$accountID}:role/aspera-node-dcdc-deluxe-inbound-role",
    ],
];
