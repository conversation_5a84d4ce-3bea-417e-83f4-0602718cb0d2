<?php

namespace Tests\Feature\Http\Controllers;

use App\Models\Organization;
use App\Models\OrganizationWebhookUrl;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class OrganizationControllerTest extends TestCase
{
    public function test_admin_can_see_organizations()
    {
        $user = User::factory()->create()->assignRole('admin');
        $response = $this->actingAs($user)->getJson(route('organizations.index'));
        $response->assertSuccessful();
        $response->assertJsonStructure([]);
    }

    public function test_admin_can_create_organization()
    {
        $user = User::factory()->create()->assignRole('admin');
        $organization = Organization::factory()->asType('admin')->withOwner($user)->make(); // in memory
        $response = $this->actingAs($user)->postJson(route('organizations.store'), $organization->toArray());
        $response->assertSuccessful();
    }

    public function test_admin_cannot_delete_own_organization()
    {
        $user = User::factory()->create()->assignRole('admin');
        $organization = Organization::factory()->withOwner($user)->create(); // in db
        $response = $this->actingAs($user)->deleteJson(route('organizations.destroy', $organization));
        $response->assertUnprocessable();
    }

    public function test_admin_can_delete_other_organization()
    {
        $user = User::factory()->create()->assignRole('admin');
        $userTwo = User::factory()->create();
        $organization = Organization::factory()->withOwner($userTwo)->create(); // in db
        $response = $this->actingAs($user)->deleteJson(route('organizations.destroy', $organization));
        $response->assertSuccessful();
    }

    public function test_admin_cannot_restore_organization()
    {
        $user = User::factory()->create()->assignRole('admin');
        $organization = Organization::factory()->withOwner($user)->create()->delete(); // in db
        $response = $this->actingAs($user)->getJson(route('organizations.restore', $organization));
        $response->assertForbidden();
    }

    public function test_super_admin_can_restores_organization()
    {
        $user = User::factory()->create()->assignRole('super-admin');
        $organization = Organization::factory()->withOwner($user)->create()->delete(); // in db
        $response = $this->actingAs($user)->getJson(route('organizations.restore', $organization));
        $response->assertSuccessful();
    }

    public function test_non_admin_can_see_own_organization()
    {
        $user = User::factory()->create();
        $organization = Organization::factory()->withOwner($user)->create();
        $response = $this->actingAs($user)->getJson(route('organizations.index'));
        $response->assertSuccessful();

    }

    public function test_member_of_organization_can_see_self()
    {
        $user = User::factory()->create();
        $organization = Organization::factory()->withOwner($user)->create();
        $response = $this->actingAs($user)->getJson(route('organizations.show', $organization));
        $response->assertSuccessful();

    }

    public function test_admin_can_update_organization()
    {
        $user = User::factory()->create()->assignRole('admin');
        $organization = Organization::factory()->withOwner($user)->create();
        $response = $this->actingAs($user)->putJson(route('organizations.update', $organization), ['name' => 'new']);
        $response->assertSuccessful();
    }

    public function test_owner_can_update_own_organization()
    {
        $user = User::factory()->create();
        $organization = Organization::factory()->withOwner($user)->create();
        $response = $this->actingAs($user)->putJson(route('organizations.update', $organization), ['name' => 'new']);
        $response->assertSuccessful();
    }

    public function test_owner_cannot_update_other_organization()
    {
        $user = User::factory()->create();
        $userTwo = User::factory()->create();
        $organization = Organization::factory()->withOwner($userTwo)->create();
        $response = $this->actingAs($user)->putJson(route('organizations.update', $organization), ['name' => 'new']);
        $response->assertForbidden();

    }

    public function test_admin_can_see_organization_users()
    {
        $user = User::factory()->create()->assignRole('admin');

        $userTwo = User::factory()->create();
        $organization = Organization::factory()->withOwner($userTwo)->create();

        $response = $this->actingAs($user)->getJson(route('organizations.users', $organization));
        $response->assertSuccessful();

    }

    public function test_user_can_see_organization_users()
    {
        $user = User::factory()->create();
        $organization = Organization::factory()->withOwner($user)->create();

        $response = $this->actingAs($user)->getJson(route('organizations.users', $organization));
        $response->assertSuccessful();

    }

    public function test_user_cannot_see_another_organization_users()
    {
        $user = User::factory()->create();
        $organization = Organization::factory()->withOwner($user)->create();

        $anotherUser = User::factory()->create();
        $anotherOrg = Organization::factory()->withOwner($anotherUser)->create();

        $response = $this->actingAs($user)->getJson(route('organizations.users', $anotherOrg));
        $response->assertForbidden();

    }

    public function test_user_can_create_webhook_url_on_own_organization()
    {
        $user = User::factory()->create()->assignRole('studio-read-write');
        $organization = Organization::factory()->withOwner($user)->create();

        $response = $this->actingAs($user)->postJson(route('organizations.store-webhook', $organization),
            [
                'event_model' => 'booking',
                'event_type' => 'update',
                'url' => 'http://google.com',
            ]
        );
        $response->assertSuccessful();

    }

    public function test_user_cannot_create_webhook_url_with_bad_data()
    {
        $user = User::factory()->create()->assignRole('studio-read-write');
        $organization = Organization::factory()->withOwner($user)->create();

        $response = $this->actingAs($user)->postJson(route('organizations.store-webhook', $organization),
            [
                'event_model' => 'booking',
                'event_type' => 'update',
                'url' => 'this-is-not-an-url',
            ]
        );
        $response->assertUnprocessable();
    }

    public function test_user_cannot_create_webhook_url_on_another_organization()
    {
        $user = User::factory()->create()->assignRole('studio-read-write');
        $organization = Organization::factory()->withOwner($user)->create();

        $anotherUser = User::factory()->create();
        $anotherOrg = Organization::factory()->withOwner($anotherUser)->create();

        $response = $this->actingAs($user)->postJson(route('organizations.store-webhook', $anotherOrg),
            [
                'event_model' => 'booking',
                'event_type' => 'update',
                'url' => 'http://google.com',
            ]
        );
        $response->assertForbidden();

    }

    public function test_user_can_see_own_webhook_urls()
    {

        $user = User::factory()->create()->assignRole('studio-read-write');
        $organization = Organization::factory()->withOwner($user)->create();

        $anotherUser = User::factory()->create();
        $anotherOrg = Organization::factory()->withOwner($anotherUser)->create();

        $response = $this->actingAs($user)->getJson(route('organizations.webhooks', $organization));
        $response->assertSuccessful();

    }

    public function test_user_can_edit_own_webhook_url()
    {

        $user = User::factory()->create()->assignRole('studio-read-write');
        $organization = Organization::factory()->withOwner($user)->create();

        $webhook = OrganizationWebhookUrl::factory()->withCreator($user)->withOrganization($organization)->create();

        $response = $this->actingAs($user)->putJson(route('organizations.update-webhook',
            ['organization' => $organization, 'organizationWebhookUrl' => $webhook]), ['bearer_token' => '12345']);
        $response->assertSuccessful();

    }

    public function test_user_cannot_edit_anothers_webhook_url()
    {

        $user = User::factory()->create()->assignRole('studio-read-write');
        $organization = Organization::factory()->withOwner($user)->create();

        $anotherUser = User::factory()->create();
        $anotherOrg = Organization::factory()->withOwner($anotherUser)->create();
        $webhook = OrganizationWebhookUrl::factory()->withCreator($anotherUser)->withOrganization($organization)->create();

        $response = $this->actingAs($user)->putJson(route('organizations.update-webhook',
            ['organization' => $organization, 'organizationWebhookUrl' => $webhook]), ['bearer_token' => '12345']);
        $response->assertSuccessful();

    }
}
