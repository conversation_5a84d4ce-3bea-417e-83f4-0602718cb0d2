<?php

namespace App\Http\Requests;

use App\Models\Enum\BookingStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class UpdateBookingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            // 'sometimes' allows for incremental changes, if the key exists it will be validated, if it is absent it will not fail (and not be updated)
            'organization_id' => ['sometimes', 'numeric'],
            'cinema_site_id' => ['sometimes', 'numeric'],
            'title_id' => ['sometimes', 'numeric'],
            'deliver_at' => ['sometimes', 'date'],
            'release_date' => ['sometimes', 'date'], // aka engagement date
            'status' => ['sometimes', new Enum(BookingStatus::class)],
            'booking_ids' => ['sometimes', 'array'], // a cheat code for bulk updates.
            'is_electronic' => ['sometimes', 'in:true,false,1,0,-1'] // take a boolean or 1, 0, -1 for satellite deliveries
        ];
    }
}
