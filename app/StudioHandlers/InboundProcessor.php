<?php

namespace App\StudioHandlers;

use App\Models\{Booking, StudioTransaction};

abstract class InboundProcessor
{
    protected Booking $booking;
    protected StudioTransaction $transaction;
    protected bool $rerun;

    function __construct($bookingId = null, $transactionId = null, $rerun = false)
    {
        if ($bookingId) {
            $this->booking = Booking::findOrFail($bookingId);
        }
        if ($transactionId) {
            $this->transaction = StudioTransaction::findOrFail($transactionId);
        }

        $this->rerun = $rerun;
    }

    abstract function handleInbound();


}
