<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('apple_televisions', function (Blueprint $table) {
            $table->id();
            $table->string('serial_number');

            $table->bigInteger('hexnode_device_id')->nullable();
            $table->foreignId('organization_id')->nullable();
            $table->foreignId('cinema_site_id')->nullable();
            $table->dateTime('status_updated_at')->nullable();
            $table->string('remote_ip_address')->nullable();

            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('apple_televisions');
    }
};
