<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreVersionRequest;
use App\Http\Requests\UpdateVersionRequest;
use App\Models\Title;
use App\Models\Version;
use App\Models\AsperaTransfer;
use Illuminate\Http\Response;
use Storage;

class VersionController extends Controller
{
    public function __construct()
    {
        $this->authorizeResource(Version::class);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return $this->executeQuery(Version::query());
    }

    public function indexForTitle(Title $title)
    {
        if (auth()->user()->cant('view', $title)) {
            abort(Response::HTTP_FORBIDDEN);
        }
        return $this->executeQuery($title->versions());
    }

    private function executeQuery($query)
    {
        $search = request()->get('search');
        return $query->select([
            'id',
            'title_id',
            'version_name',
            'cpl_uuid',
            'is_ready',
            'size',
            'created_at',
        ])
            ->with(['asperaTransfer:id,version_id,status,direction,aspera_transfer_data'])
            ->when($search, function ($q, $search) {
                $search = "%{$search}%";
                $q->where('version_name', 'like', $search);
            })
            ->paginate(request()->get('take'));
    }

    public function storeForTitle(StoreVersionRequest $request, Title $title)
    {

        $data = $request->validated();

        // check the parent object permissions.
        if (auth()->user()->cant('update', $title)) {
            abort(Response::HTTP_FORBIDDEN);
        }

        $data['creator_id'] = auth()->user()->id;
        $version = $title->versions()->create($data);

        return response(['data' => $version]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \App\Http\Requests\StoreVersionRequest $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreVersionRequest $request)
    {
        //
        $data = $request->validated();

        $data['creator_id'] = auth()->user()->id;
        $title = Title::findOrFail($data['title_id']);

        $version = $title->versions()->create($data);

        return response(['data' => $version]);

    }

    /**
     * Display the specified resource.
     *
     * @param \App\Models\Version $version
     * @return \Illuminate\Http\Response
     */
    public function show(Version $version)
    {
        return response(['data' => $version->makeVisible(['s3_details'])->load('asperaTransfer')]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \App\Http\Requests\UpdateVersionRequest $request
     * @param \App\Models\Version $version
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateVersionRequest $request, Version $version)
    {
        $data = $request->validated();
        $version->update($data);
        return response(['data' => $version]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\Models\Version $version
     * @return \Illuminate\Http\Response
     */
    public function destroy(Version $version)
    {
        $version->delete();
        return response(['data' => $version]);

    }
}
