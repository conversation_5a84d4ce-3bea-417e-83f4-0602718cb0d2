<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $tables = [
            'aspera_transfers',
            'bookings',
            'booking_statuses',
            'media_manager_jobs',
            'studio_transactions',
        ];

        foreach ($tables as $table) {
            try {
                Schema::table($table, function (Blueprint $table) {
                    $table->softDeletes();
                });
            } catch (Illuminate\Database\QueryException $e) {
                // ignore on re-runs.
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // no need.
    }
};
