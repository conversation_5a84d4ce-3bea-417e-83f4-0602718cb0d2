<?php

namespace App\Http\Controllers;

use App\Jobs\CheckDCPJob;
use App\Models\AsperaTransfer;
use App\Models\Title;

class AsperaController extends Controller
{

    public function upload()
    {
        // create an upload
        return response()->json(AsperaTransfer::createUpload());
    }

    public function uploadsForTitle(Title $title)
    {
        return response($title->uploads()->where('direction', 'upload')->whereNull('version_id')->paginate());
    }

    public function download()
    {
        // create a download
        return response()->json(AsperaTransfer::createDownload());
    }

    public function getFakeDeluxeTransfers()
    {
        // return any aspera transfers that don't have a title attached with a simplified ID, title, status.
        return response()->json(AsperaTransfer::whereNull('title_id')->latest()->limit(200)->get());
    }

    public function linkFakeDeluxTransferToTitle(Title $title, AsperaTransfer $asperaTransfer)
    {
        // create a 'version' as if it were just uploaded
        $version = $title->versions()->create(
            [
                'creator_id' => auth()->user()->id,
                'version_name' => $asperaTransfer->transfer_spec['path'],
                'is_ready' => 0,
            ]
        );

        // assign the title id to the aspera transfer id
        $asperaTransfer->title_id = $title->id;
        $asperaTransfer->version_id = $version->id;
        $asperaTransfer->save();

        // then fire a job to run Check DCP against this version, which will in turn split it if necesary.
        CheckDCPJob::dispatch($version->id);

        // done.
        return response()->json(['success' => true, 'version' => $version]);
    }
}
