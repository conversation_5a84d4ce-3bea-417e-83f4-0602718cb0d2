<?php

namespace App\Console\Commands;

use App\Jobs\RetryOutboundNotification;
use App\Models\OutboundNotifierLog;
use Illuminate\Console\Command;

class ReplayOutboundNotifications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'replay-outbound-log';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Retry any outbound notification logs that previously failed.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        $outbound = OutboundNotifierLog::where('completed', false)
            ->whereStatus('completed')
            ->limit(1)->get();

        foreach ($outbound as $onl) {
            // fire up the retry job, this will create a new log record and enter retry-loop.

            $this->info("Dispatching Job for ID {$onl->id}");
            RetryOutboundNotification::dispatch($onl);
            // this log is done.
            $onl->completed = true;
            $onl->save();
        }

        return Command::SUCCESS;
    }
}
