<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class StudioTransaction extends Model
{
    use SoftDeletes, HasFactory;

    protected $fillable = ['user_id', 'booking_id', 'organization_id', 'processed', 'transaction_data'];
    protected $casts = ['processed' => 'boolean', 'transaction_data' => 'json'];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function booking()
    {
        return $this->belongsTo(Booking::class);
    }
}
