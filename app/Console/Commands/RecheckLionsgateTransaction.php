<?php

namespace App\Console\Commands;

use App\Models\StudioTransaction;
use Illuminate\Console\Command;
use App\StudioHandlers\Lionsgate\InboundProcessor;

class RecheckLionsgateTransaction extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lionsgate:recheck {transactionId} {--force}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Recheck a lionsgate transaction';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $id = $this->argument('transactionId');
        $force = $this->option('force');

        $transaction = StudioTransaction::findOrFail($id);
        if ($transaction->processed === true && !$force) {
            $this->warn("Transaction has already been procesed.");
            return Command::FAILURE;
        }
        $handler = new InboundProcessor(null, $id, true);
        $this->info("done");
        $handler->handleInbound();

        return Command::SUCCESS;
    }
}
