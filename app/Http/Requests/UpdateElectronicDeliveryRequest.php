<?php

namespace App\Http\Requests;

use App\Models\Enum\BookingStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class UpdateElectronicDeliveryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            // 'sometimes' allows for incremental changes, if the key exists it will be validated, if it is absent it will not fail (and not be updated)
            'status' => ['sometimes', new Enum(BookingStatus::class)],
            'progress' => ['sometimes', 'integer'],
            'speed_in_mbps' => ['sometimes', 'integer'],
            'details' => ['sometimes', 'string']
        ];
    }
}
