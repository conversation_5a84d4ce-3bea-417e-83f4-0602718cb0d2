<?php

namespace Database\Factories;

use App\Models\Title;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Version>
 */
class VersionFactory extends Factory
{
    const FORMAT = '640870_%s_%s-7-2D_F_EN-EN-CCAP_US-GB_51-HI_2K_DI_20210425_DTB_SMPTE_OV';

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'version_name' => sprintf(self::FORMAT, Str::studly($this->faker->words(2, true)), 'TLR'),
            'asset_uuid' => Str::uuid(),
            'nickname' => 'TRL_CCAP',
            'size' => random_int(10000000000, ************),
            'is_ready' => true,
        ];
    }

    public function onTitle(Title $title)
    {
        return $this->state(function (array $attributes) use ($title) {
            return [
                'title_id' => $title->id,
            ];
        });

    }

    public function withCreator(User $user)
    {
        return $this->state(function (array $attributes) use ($user) {
            return [
                'creator_id' => $user->id,
            ];
        });
    }

}
