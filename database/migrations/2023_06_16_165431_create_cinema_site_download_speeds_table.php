<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('cinema_site_download_speeds', function (Blueprint $table) {
            $table->id();
            $table->timestamps();
            $table->boolean('is_aspera_transfer');
            $table->float('download_speed');
            $table->foreignId('cinema_site_id');
            $table->foreignId('media_manager_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('cinema_site_download_speeds');
    }
};
