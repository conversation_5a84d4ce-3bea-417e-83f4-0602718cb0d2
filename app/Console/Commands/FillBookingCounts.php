<?php

namespace App\Console\Commands;

use App\Jobs\UpdateBookingCounts;
use Illuminate\Console\Command;
use App\Models\Booking;

class FillBookingCounts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fill-booking-counts {bookingId}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update booking summaries for titles and packages attached to the booking';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        UpdateBookingCounts::dispatch(Booking::findOrFail($this->argument('bookingId')));
        return Command::SUCCESS;
    }
}
