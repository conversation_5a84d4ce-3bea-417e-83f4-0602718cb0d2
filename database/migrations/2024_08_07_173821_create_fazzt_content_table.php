<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('fazzt_content', function (Blueprint $table) {
            $table->id();

            $table->uuid('asset_uuid'); // this will match the uuid of our 'version'...
            $table->string('package_file')->nullable(); // full path on the fazzt server required to initiate transfers.
            $table->string('status')->default('PENDING');
            $table->string('message')->nullable();
            $table->json('error')->nullable(); // this has a Result->Error or an error... hmm.

            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('fazzt_content');
    }
};
