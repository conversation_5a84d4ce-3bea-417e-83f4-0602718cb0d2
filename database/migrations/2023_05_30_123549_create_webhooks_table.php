<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('webhooks_log', function (Blueprint $table) {
            $table->id();

            $table->foreignId('organization_webhook_url_id');

            $table->json('payload')->nullable();
            $table->json('headers')->nullable();

            $table->integer('http_status')->nullable();
            $table->string('http_error')->nullable();
            $table->text('http_body')->nullable();

            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('webhooks_log');
    }
};
