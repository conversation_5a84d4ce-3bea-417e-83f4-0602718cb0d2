<?php

namespace Tests\Feature\Http\Controllers;

use App\Models\CinemaSite;
use App\Models\Organization;
use App\Models\User;
use Tests\TestCase;

class CinemaSiteControllerTest extends TestCase
{
    /**
     * A basic feature test example.
     *
     * @return void
     */
    public function test_admin_can_get_cinemas_list()
    {
        $admin = User::factory()->create()->assignRole('admin');
        $response = $this->actingAs($admin)->getJson(route('cinemas.index'));
        $response->assertSuccessful();
    }

    public function test_non_admin_cannot_get_cinemas_list()
    {
        $user = User::factory()->create();
        $response = $this->actingAs($user)->getJson(route('cinemas.index'));
        $response->assertForbidden();
    }

    public function test_admin_can_create_cinema()
    {
        $admin = User::factory()->create()->assignRole('admin');
        $organization = Organization::factory()->withOwner($admin)->create();
        $organization->users()->save($admin);
        $newCinema = CinemaSite::factory()->withOrganization($organization)->make()->toArray();
        $response = $this->actingAs($admin)->postJson(route('cinemas.store'), $newCinema);
        $response->assertSuccessful();
    }

    public function test_admin_cannot_create_cinema_with_bad_data()
    {
        $admin = User::factory()->create()->assignRole('admin');
        $newCinema = CinemaSite::factory()
            ->withOrganization(Organization::factory()->withOwner($admin)->create())->create()->toArray();
        unset($newCinema['name']);
        $response = $this->actingAs($admin)->postJson(route('cinemas.store'), $newCinema);
        $response->assertUnprocessable();
    }

    public function test_admin_can_view_cinema()
    {
        $admin = User::factory()->create()->assignRole('admin');
        $newCinema = CinemaSite::factory()->withOrganization(Organization::factory()->withOwner($admin)->create())->create();
        $response = $this->actingAs($admin)->getJson(route('cinemas.show', $newCinema));
        $response->assertSuccessful();
    }

    public function test_admin_can_edit_cinema()
    {
        $admin = User::factory()->create()->assignRole('admin');
        $newCinema = CinemaSite::factory()->withOrganization(Organization::factory()->withOwner($admin)->create())->create();
        $response = $this->actingAs($admin)->putJson(route('cinemas.update', $newCinema),
            ['name' => uniqid('new-name')]);
        $response->assertSuccessful();
    }


    public function test_admin_can_delete_cinema()
    {
        $admin = User::factory()->create()->assignRole('admin');
        $newCinema = CinemaSite::factory()->withOrganization(Organization::factory()->withOwner($admin)->create())->create();
        $response = $this->actingAs($admin)->deleteJson(route('cinemas.destroy', $newCinema));
        $response->assertSuccessful();

    }

}
