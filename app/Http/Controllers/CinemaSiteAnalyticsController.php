<?php

namespace App\Http\Controllers;

use App\Models\{CinemaSite, DownloadSpeed};
use Carbon\Carbon;

class CinemaSiteAnalyticsController extends Controller
{

    public function __construct()
    {
        $this->authorizeResource(CinemaSite::class, 'cinema');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(CinemaSite $cinemaSite)
    {

        $startDate = (new Carbon(request()->get('start', now()->subDays(7))))->setTime(0, 0, 0);
        $endDate = (new Carbon(request()->get('end', now())))->setTime(23, 59, 59);

        $timeFrame = $startDate->diffInDays($endDate);

        $slots = [];
        $slotDate = $startDate->copy();
        for ($i = 0; $i <= $timeFrame; $i++) {

            $slotString = $slotDate->year . '-' . $slotDate->dayOfYear;

            for ($h = 0; $h < 24; $h++) {
                $slotDate->hour($h);
                $slotObject = new \StdClass();
                $slots[$slotString . '-' . $h] = $slotObject;
            }

            $slotDate->addDay(1);

        }

        $speeds = $cinemaSite->downloadSummaries()
            ->where('year', '>=', $startDate->year)
            ->where('day_of_year', '>=', $startDate->dayOfYear())
            ->where('year', '<=', $endDate->year)
            ->where('day_of_year', '<=', $endDate->dayOfYear())
            ->orderBy('year')
            ->orderBy('day_of_year')
            ->orderBy('hour_of_day')
            ->get()
            ->toArray();

        // fill in empty hours/slots in this entire thing.
        foreach ($speeds as $speed) {
            $slotString = $speed['year'] . '-' . $speed['day_of_year'] . '-' . $speed['hour_of_day'];
            $slots[$slotString] = $speed;
        }

        return [
            'start' => $startDate->format('Y-m-d'),
            'end' => $endDate->format('Y-m-d'),
            'speeds' => array_values($slots),
        ];
    }

}
