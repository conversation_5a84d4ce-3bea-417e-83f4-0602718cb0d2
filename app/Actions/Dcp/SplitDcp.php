<?php

namespace App\Actions\Dcp;

use App\Models\AsperaTransfer;
use App\Models\Version;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Storage;
use Str;

class SplitDcp
{
    static Collection $files;
    static array $directories = [];
    static array $results = [];

    static function execute(Version $previousVersion)
    {

        // 1. verifyDcp - it's results must return 'multi_directory'= true, otherwise there's nothing to split.
        $s3root = $previousVersion->s3_details['root'];
        $s3bucket = $previousVersion->s3_details['bucket'];

        $s3 = Storage::disk($s3bucket);

        if ($s3root === '/') {
            $s3root = '/' . $previousVersion->s3_details['path'];
        }

        self::$results['root'] = $s3root;

        $dcpVerificationResults = VerifyDcp::execute($s3root, $s3bucket);

        self::$results['verify_dcp'] = $dcpVerificationResults;

        if ($dcpVerificationResults['error'] === true) {
            self::$results['message'] = "Initial Verification Failed";
            self::$results['error'] = true;
            return self::$results;
        }
        if ($dcpVerificationResults['multi_directory'] !== true) {
            self::$results['message'] = "S3 Root does not contain multiple directories.";
            self::$results['error'] = true;
            return self::$results;
        }

        $base = $dcpVerificationResults['base_directory'];

        // 2. grab the list of directories within the S3 bucket
        $directories = $s3->directories($base);

        $cplUUIDs = [];
        $versions = collect();

        foreach ($directories as $directory) {

            $newRoot = $directory;
            $newPath = basename($newRoot);

            $files = $s3->allFiles($directory);

            // make sure the new dcp passes the checker
            $totalSize = 0;
            $bucketFiles = [];
            foreach ($files as $file) {
                // recalculate for this single dcp
                $size = $s3->size($file);
                $totalSize += $size;

                // track the pathnames & size on the fly for compat with real uploads.
                $fileObject = new \stdClass();
                $fileObject->size = $size;
                $fileObject->bytes_written = $size;
                $fileObject->path = $file;
                $bucketFiles[] = $fileObject;

            }

            $s3details = new \stdClass();
            $s3details->root = str_replace($newPath, '', $newRoot);
            $s3details->path = $newPath;
            $s3details->bucket = $s3bucket;
            $s3details->files = $bucketFiles;

            // verify the new folder as a DCP, create a version, attach it to title
            // set the cpl_uuid to this dcp.
            $newResults = VerifyDcp::execute($newRoot, $s3bucket);
            $s3details->dcp_results = $newResults;

            $multiUUIds = $newResults['cpl_multi_uuids'] ?? [];

            $version = Version::firstOrCreate([
                'parent_version_id' => $previousVersion->id,
                'title_id' => $previousVersion->title_id,
                'creator_id' => $previousVersion->creator_id,
                'cpl_uuid' => str_replace('urn:uuid:', '', $newResults['cpl_uuid']), //
            ], [
                'version_name' => $newResults['dcp_title'],
                'asset_uuid' => str_replace('urn:uuid:', '', $newResults['dcp_uuid']),
                'multi_cpl_uuids' => $multiUUIds,
                'size' => $totalSize,
            ]);


            if ($newResults['error'] === true) {
                $version->s3_details = $newResults;
                $version->save();
                $version->delete();
            }
            else {
                $version->s3_details = $s3details;
                $version->is_ready = true;
                $version->save();
            }
            // append each split CPL to the parent version multi_cpl_uuids
            $cplUUIDs = array_merge($multiUUIds, $cplUUIDs);
            // save the parent id on the new version id

            $versions->add($version);
        }

        $previousVersion->multi_cpl_uuids = array_unique($cplUUIDs);
        $previousVersion->save();

        self::$results['versions'] = $versions->only(['id', 'version_name'])->toArray();
        self::$results['error'] = false;
        return self::$results;
    }

}
