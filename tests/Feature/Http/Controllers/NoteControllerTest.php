<?php

namespace Tests\Feature\Http\Controllers;

use App\Models\Role;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class NoteControllerTest extends TestCase
{

    public function test_user_without_site_permission_cannot_create_site_note()
    {

    }

    public function test_user_without_equipment_permission_cannot_create_equipment_note()
    {

    }
}
