<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('fazzt_content', function (Blueprint $table) {
            //
            $table->bigInteger('package_size')->after('package_file')->nullable();
            $table->unique('asset_uuid');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('fazzt_content', function (Blueprint $table) {
            //
            $table->dropColumn('package_size');
            $table->dropUnique('fazzt_content_asset_uuid');

        });
    }
};
