<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {

        Schema::create('cinema_site_contacts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('cinema_site_id');

            $table->string('name')->nullable();
            $table->string('email')->nullable();
            $table->string('phone_number')->nullable();
            $table->string('role')->nullable();

            // “Name”, “Emails”, “Phone Number”, “Role”.

            $table->softDeletes();
            $table->timestamps();
        });

        //
        $sites = \App\Models\CinemaSite::get();
        foreach ($sites as $site) {
            if ($site->primary_contact_name || $site->primary_contact_number || $site->primary_contact_email) {
                $site->contacts()->create([
                    'name' => $site->primary_contact_name,
                    'email' => $site->primary_contact_email,
                    'phone_number' => $site->primary_contact_number,
                    'role' => 'Primary',
                ]);

            }
            if ($site->secondary_contact_name || $site->secondary_contact_number || $site->secondary_contact_email) {
                $site->contacts()->create([
                    'name' => $site->secondary_contact_name,
                    'email' => $site->secondary_contact_email,
                    'phone_number' => $site->secondary_contact_number,
                    'role' => 'Secondary',
                ]);
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('cinema_site_contacts');
    }
};
