<?php

namespace Tests\Feature\Http\Controllers;

use App\Models\Booking;
use App\Models\CinemaSite;
use App\Models\Delivery;
use App\Models\Enum\BookingStatus;
use App\Models\MediaManagerJob;
use App\Models\Organization;
use App\Models\Title;
use App\Models\User;
use App\Models\Version;
use App\StudioHandlers\Disney\OutboundNotifier;
use Tests\TestCase;

class DeliveryControllerTest extends TestCase
{
    /**
     * A basic feature test example.
     *
     * @return void
     */
    public function test_admin_can_list_deliveries()
    {
        $admin = User::factory()->create()->assignRole('admin');
        $response = $this->actingAs($admin)->getJson(route('deliveries.index'));
        $response->assertSuccessful();
    }

    public function test_admin_can_see_delivery()
    {
        $admin = User::factory()->create()->assignRole('admin');
        $response = $this->actingAs($admin)->getJson(route('deliveries.index'));
        $response->assertSuccessful();
    }

    public function test_admin_can_update_delivery()
    {
        $admin = User::factory()->create()->assignRole('admin');
        $organization = Organization::factory()->withOwner($admin)->create();
        $title = Title::factory()->withOrganization($organization)->withCreator($admin)->create();
        $version = Version::factory()->onTitle($title)->withCreator($admin)->create();
        $cinema = CinemaSite::factory()->withOrganization($organization)->create();
        $booking = Booking::factory()->withTitle($title)->withCreator($admin)
            ->withCinema($cinema)->withOrganization($organization)->create();
        $delivery = Delivery::factory()->withVersion($version)->forBooking($booking)
            ->withCinema($cinema)->withOrganization($organization)->create();

        $response = $this->actingAs($admin)->putJson(route('deliveries.update', $delivery),
            [
                'deliver_at' => now()->addMonth(),
            ]
        );
        $response->assertSuccessful();
    }

    public function test_admin_can_update_delivery_status()
    {
        $admin = User::factory()->create()->assignRole('admin');
        $organization = Organization::factory()->withOwner($admin)->create();
        $title = Title::factory()->withOrganization($organization)->withCreator($admin)->create();
        $version = Version::factory()->onTitle($title)->withCreator($admin)->create();
        $cinema = CinemaSite::factory()->withOrganization($organization)->create();
        $booking = Booking::factory()->withTitle($title)->withCreator($admin)
            ->withCinema($cinema)->withOrganization($organization)->create();
        $delivery = Delivery::factory()->withVersion($version)->forBooking($booking)
            ->withCinema($cinema)->withOrganization($organization)->create();

        $response = $this->actingAs($admin)->putJson(route('deliveries.update', $delivery),
            [
                'status' => "completed",
            ]
        );

        $response->assertSuccessful();
        $this->assertEquals($delivery->fresh()->status, BookingStatus::Completed);
    }

    // public function test_admin_can_bulk_update_delivery_status()
    // {
    //     $admin = User::factory()->create()->assignRole('admin');
    //     $organization = Organization::factory()->withOwner($admin)->create();
    //     $title = Title::factory()->withOrganization($organization)->withCreator($admin)->create();
    //     $version = Version::factory()->onTitle($title)->withCreator($admin)->create();
    //     $cinema = CinemaSite::factory()->withOrganization($organization)->create();
    //     $booking = Booking::factory()->withTitle($title)->withCreator($admin)
    //         ->withCinema($cinema)->withOrganization($organization)->create();
    //     $delivery = Delivery::factory()->withVersion($version)->forBooking($booking)
    //         ->withCinema($cinema)->withOrganization($organization)->create();

    //     $response = $this->actingAs($admin)->putJson(route('deliveries.bulk-update'),
    //         [
    //             'delivery_ids' => [$delivery->id],
    //             'status' => "completed",
    //         ]
    //     );

    //     $response->assertSuccessful();
    //     $this->assertEquals($delivery->status, "completed");

    // }

    public function test_non_admin_cannot_update_delivery()
    {
        $user = User::factory()->create();
        $admin = User::factory()->create()->assignRole('admin');
        $organization = Organization::factory()->withOwner($admin)->create();
        $title = Title::factory()->withOrganization($organization)->withCreator($admin)->create();
        $version = Version::factory()->onTitle($title)->withCreator($admin)->create();
        $cinema = CinemaSite::factory()->withOrganization($organization)->create();
        $booking = Booking::factory()->withTitle($title)->withCreator($admin)
            ->withCinema($cinema)->withOrganization($organization)->create();
        $delivery = Delivery::factory()->withVersion($version)->forBooking($booking)
            ->withCinema($cinema)->withOrganization($organization)->create();

        $response = $this->actingAs($user)->putJson(route('deliveries.update', $delivery),
            [
                'deliver_at' => now()->addMonth(),
            ]
        );
        $response->assertForbidden();
    }

    public function test_electronic_delivery_receives_status_and_progress()
    {
        $user = User::factory()->create();
        $admin = User::factory()->create()->assignRole('admin');
        $organization = Organization::factory()->withOwner($admin)->create();
        $title = Title::factory()->withOrganization($organization)->withCreator($admin)->create();
        $version = Version::factory()->onTitle($title)->withCreator($admin)->create();
        $cinema = CinemaSite::factory()->withOrganization($organization)->create();
        $booking = Booking::factory()->withTitle($title)->withCreator($admin)
            ->withCinema($cinema)->withOrganization($organization)->create();
        $delivery = Delivery::factory()->withVersion($version)->forBooking($booking)
            ->withCinema($cinema)->withOrganization($organization)->create();

        $job = MediaManagerJob::create([
            'delivery_id' => $delivery->id,
            'name' => 'test',
            'type' => 'aspera_download',
            'code' => 'external_download',
        ]);

        $response = $this->postJson(route('update-e-delivery', ['delivery' => $delivery, 'mediaManagerJob' => $job]),
            [
                'status' => 'transmitting',
                'progress' => 50,
                'speed_in_mbps' => 100,
            ]
        );

        $response->assertSuccessful();
        $this->assertEquals($delivery->fresh()->progress, 50);
        $this->assertEquals($delivery->fresh()->status, BookingStatus::Transmitting);
        $this->assertEquals($delivery->fresh()->transfer_speed, '50% • 100 mbps');
    }

    // public function test_electronic_delivery_only_creates_one_status()
    // {
    //     $user = User::factory()->create();
    //     $admin = User::factory()->create()->assignRole('admin');
    //     $organization = Organization::factory()->withOwner($admin)->create();
    //     $title = Title::factory()->withOrganization($organization)->withCreator($admin)->create();
    //     $version = Version::factory()->onTitle($title)->withCreator($admin)->create();
    //     $cinema = CinemaSite::factory()->withOrganization($organization)->create();
    //     $booking = Booking::factory()->withTitle($title)->withCreator($admin)
    //         ->withCinema($cinema)->withOrganization($organization)->create();
    //     $delivery = Delivery::factory()->withVersion($version)->forBooking($booking)
    //         ->withCinema($cinema)->withOrganization($organization)->create();

    //     $response = $this->postJson(route('update-e-delivery', $delivery),
    //         [
    //             'status' => 'transmitting',
    //             'progress' => 50,
    //         ]
    //     );
    //     $response = $this->postJson(route('update-e-delivery', $delivery),
    //         [
    //             'status' => 'transmitting',
    //             'progress' => 51,
    //         ]
    //     );

    //     $response->assertSuccessful();
    //     $this->assertEquals(1, $delivery->statuses()->count());
    // }

    public function test_it_triggers_outbound_completion_update()
    {
        $user = User::factory()->create();
        $admin = User::factory()->create()->assignRole('admin');
        $organization = Organization::factory()->withOwner($admin)->create();
        $title = Title::factory()->withOrganization($organization)->withCreator($admin)->create();
        $version = Version::factory()->onTitle($title)->withCreator($admin)->create();
        $cinema = CinemaSite::factory()->withOrganization($organization)->create();
        $booking = Booking::factory()->withTitle($title)->withCreator($admin)
            ->withCinema($cinema)->withOrganization($organization)->create([
                'is_electronic' => true,
                'notification_class' => OutboundNotifier::class,
                'notification_parameters' => ['orderId' => '123'],
            ]);
        $delivery = Delivery::factory()->withVersion($version)->forBooking($booking)
            ->withCinema($cinema)->withOrganization($organization)->create();

        $job = MediaManagerJob::create([
            'delivery_id' => $delivery->id,
            'name' => 'test',
            'type' => 'aspera_download',
            'code' => 'external_download',
        ]);

        $response = $this->postJson(route('update-e-delivery', ['delivery' => $delivery, 'mediaManagerJob' => $job]),
            [
                'status' => 'delivered',
                'progress' => 100,
            ]
        );

        $response->assertSuccessful();
    }

    public function test_reject_duplicate_content_in_library()
    {


    }
}

