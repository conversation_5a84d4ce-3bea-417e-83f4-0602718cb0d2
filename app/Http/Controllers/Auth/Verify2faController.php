<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Symfony\Component\HttpFoundation\Response;
use Twilio\Rest\Client;

/**
 * This is to verify 2fa methods that aren't really 2fa methods.
 * 1. SMS via Twilio
 * 2. Email code via Mandrill/MailChimp
 * Authenticator app, non-intercept-able codes are still handled by Laravel/Fortify built-in.
 */
class Verify2faController extends Controller
{
    private function setupTwilio()
    {

        $this->middleware = ['auth:sanctum', 'verified'];
        $account = config('services.twilio.secret');
        $token = config('services.twilio.key');
        $this->sid = config('services.twilio.service_id');

        $this->twilio = new Client($account, $token);
        $this->phone = auth()->user()->phone;

    }

    public function setPhoneNumber()
    {
        $this->setupTwilio();

        // lookup the number on twilio
        try {
            $lookup = $this->twilio->lookups
                ->v1
                ->phoneNumbers(request()->post('phone_number'))
                ->fetch(["countryCode" => request()->post('country_code')]); //US or CA
        }
        catch (\Exception $e) {
            abort(Response::HTTP_UNPROCESSABLE_ENTITY,
                "Could not verify your phone number. Please verify it is a US or CA Carrier.");
        }
        // a new number is saved.
        $phoneNumber = pathinfo($lookup->url, PATHINFO_FILENAME);

        if (!$this->phone) {
            auth()->user()->phone()->create([
                'phone_number' => $phoneNumber,
                'last_verified_at' => null,
                'original_verified_at' => null,
            ]);
        }
        else {
            $this->phone->fill([
                'phone_number' => $phoneNumber,
                'last_verified_at' => null,
            ]);
            $this->phone->save();
        }

        // launch a twilio verif process.
        $verification = $this->twilio->verify->v2->services($this->sid)
            ->verifications
            ->create($phoneNumber, "sms");

        // wait for the user to hit verifyPhoneNumber
    }

    public function getOptions()
    {
        // todo: get options for the user to select where to send the code
        // post back the key of this array to sendCode as verify_method
        $phone = substr_replace(auth()->user()->phone->phone_number,
            str_repeat('*', strlen(auth()->user()->phone->phone_number) - 5), 1, -4);


        return [
            'verify_email' => 'd***l@bit****.com',
            'verify_sms' => $phone,
        ];

    }

    public function sendCode()
    {
        // todo: allow the user to select email or sms, needs sendgrid configuration (maybe later?)
        $this->setupTwilio();
        // make sure the user has a previously verified number, otherwise we're in a deadlock.
        //

        $method = request()->post('verify_method');
        if (!$method) {
            abort(Response::HTTP_UNPROCESSABLE_ENTITY, "Verify method must be selected.");
        }

        if ($method === 'verify_sms') {
            $verification = $this->twilio->verify->v2->services($this->sid)
                ->verifications
                ->create($this->phone->phone_number, "sms");

            logger('sending sms code');
            if ($verification->status === 'pending') {
                return ['ok' => 'Verification Pending'];
            }
            abort(Response::HTTP_UNPROCESSABLE_ENTITY,
                'Unable to send SMS message to phone number on record. Please try again.');
        }

        abort(Response::HTTP_UNPROCESSABLE_ENTITY,
            'An invalid method of verification was provided.');

    }

    public function verifyCode() {
        $this->setupTwilio();
        $code = request()->post('code', null);

        // find the method that was sent to the user
        try {
            $verification_check = $this->twilio->verify->v2->services($this->sid)
                ->verificationChecks
                ->create([
                        "to" => $this->phone->phone_number,
                        "code" => $code,
                    ]
                );
        }
        catch (\Exception $e) {
            abort(Response::HTTP_UNPROCESSABLE_ENTITY, "Code has expired. Please try again.");
        }

        if ($verification_check->status === 'approved') {
            auth()->user()->sms_2fa_verified_at = now();
            auth()->user()->save();
            $this->phone->last_verified_at = now();
            $this->phone->save();
            return ['ok' => 'success'];
        }

        abort(Response::HTTP_UNPROCESSABLE_ENTITY, "Invalid code. Please try again.");

    }

    public function verifySetupCode()
    {
        // the user will be authenticated but with limited access until the sms code is verified
        $this->setupTwilio();

        $code = request()->post('code', null);

        // find the method that was sent to the user
        try {
            $verification_check = $this->twilio->verify->v2->services($this->sid)
                ->verificationChecks
                ->create([
                        "to" => $this->phone->phone_number,
                        "code" => $code,
                    ]
                );
        }
        catch (\Exception $e) {
            $this->phone->phone_number = null;
            $this->phone->save();
            abort(Response::HTTP_UNPROCESSABLE_ENTITY, "Code has expired. Please try again.");
        }

        if ($verification_check->status === 'approved') {
            auth()->user()->sms_2fa_verified_at = now();
            $this->phone->last_verified_at = now();
            if ($this->phone->original_verified_at === null) {
                $this->phone->original_verified_at = now();
            }
            $this->phone->save();
            return ['ok' => 'success'];
        }

        abort(Response::HTTP_UNPROCESSABLE_ENTITY, "Invalid code. Please try again.");
    }
}
