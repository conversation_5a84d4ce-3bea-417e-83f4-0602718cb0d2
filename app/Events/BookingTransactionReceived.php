<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class BookingTransactionReceived implements ShouldQueue
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public int|null $bookingId = 0;
    public int $transactionId = 0;
    public string $studio = '';

    /**
     * Emit an event with the required data to be parsed on the queue.
     * @return void
     */
    public function __construct($bookingId, $transactionId, $studio)
    {
        $this->bookingId = $bookingId;
        $this->transactionId = $transactionId;
        $this->studio = $studio;
    }

}
