<?php

namespace App\Console\Commands;

use App\Actions\Dcp\SplitDcp;
use App\Actions\Dcp\VerifyDcp;
use App\Actions\Slack\Notify;
use App\Models\AsperaTransfer;
use App\Models\Booking;
use App\Models\Enum\BookingStatus;
use App\Models\Title;
use App\Models\Version;
use App\StudioHandlers\Disney\InboundProcessor as DisneyProcessor;
use App\StudioHandlers\Sony\InboundProcessor as SonyProcessor;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\ServerException;
use Illuminate\Console\Command;
use Sentry\Tracing\Transaction;

class CheckAsperaTransfers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'aspera:check';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check Aspera Transfers';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            $transfers = $this->getTransfers();
        }
        catch (ServerException | ClientException $e) {
            $this->warn($e->getMessage());
            logger()->warning($e->getMessage());
            return $e->getcode();
        }

        $this->info("Checking " . count($transfers) . " transfers.");
        foreach ($transfers as $transferInfo) {
            $this->handleTransferInfo($transferInfo);
        }
        $this->info("Completed aspera:check");
    }

    public function getTransfers()
    {
        $client = new \GuzzleHttp\Client();
        $response = $client->get(config('aspera.s3.transfer.transfer_name'), [
            'auth' => [
                config('aspera.s3.user'),
                config('aspera.s3.password'),
            ],
        ]);

        return json_decode($response->getBody()->getContents());

    }

    private function handleTransferInfo($transferInfo)
    {
        $this->info("\nAspera Transfer ID: {$transferInfo->id} - {$transferInfo->status}");

        // look for start_spec -> tags -> cinesend -> transfer_uuid
        $transferUuid = $transferInfo->start_spec?->tags?->cinesend?->transfer_uuid;

        if (!$transferUuid) {
            $this->warn("There is no `transfer_uuid` in the Aspera Trasnfer data. We cannot match this transfer.");
            return;
        }

        $this->info("Transfer uuid: {$transferUuid}");

        // find the transfer uuid in the asperaTransfers model
        $asperaTransfer = AsperaTransfer::where('transfer_uuid', $transferUuid)
            ->whereNotIn('status', [AsperaTransfer::COMPLETED, AsperaTransfer::INVALID_DCP])
            ->first();

        // don't waste time on ones already flagged completed.
        if (!$asperaTransfer) {
            $this->info("There's nothing to update on this UUID. It's already complete, or invalid, or non existent.");
            return;
        }

        // don't do anything if the data is the same.
        if ($asperaTransfer->aspera_transfer_data === $transferInfo) {
            return;
        }

        // update the status & the payload
        $asperaTransfer->status = $transferInfo->status;
        $asperaTransfer->aspera_transfer_data = $transferInfo;
        $asperaTransfer->save();

        // don't do anything if it's not done
        if ($asperaTransfer->status !== AsperaTransfer::COMPLETED) {
            return;
        }

        // let's trigger the DCP verification and finalize the version
        $this->info("This transfer completed! Verifying DCP Contents.");

        $results = VerifyDcp::execute(AsperaTransfer::ASPERA_ROOT . $asperaTransfer->transfer_uuid);
        $transferInfo->dcp_results = $results;
        $asperaTransfer->aspera_transfer_data = $transferInfo;
        $asperaTransfer->save();

        /** @var Version $version */
        $version = $asperaTransfer->version;

        $url = config('app.frontend_url') . '/titles/' . $version->title_id . '/summary';
        $vUrl = config('app.frontend_url') . '/titles/' . $version->title_id . '/content/' . $version->id;

        // if there are errors, we can save the status and leave
        if ($results['error'] === true) {
            // $this->warn("DCP Validating Failed.");
            $slack = (new Notify("A Failed DCP Validation for \"{$version->title->friendly_title}.\""))
                ->addFieldsSection([
                    ['type' => 'mrkdwn', 'text' => "*Title:* <{$url}|{$version->title->friendly_title}>"],
                    ['type' => 'mrkdwn', 'text' => "*Version:* <{$vUrl}|{$version->version_name}>"],
                ])
                ->addDivider()->send();

            $asperaTransfer->status = AsperaTransfer::INVALID_DCP;
            $asperaTransfer->aspera_transfer_data = $transferInfo;
            $asperaTransfer->save();
            return;
        }


        $this->info("Updating the version id {$version->id}");

        // if it's multi directory here, make the title the origin folder name and
        if (isset($results['multi_directory'])) {
            $name = basename($results['base_directory']);
            $uuid = '';
        }
        else {
            $uuid = str_replace('urn:uuid:', '', $results['cpl_uuid']);
            $assetUuid = str_replace('urn:uuid:', '', $results['dcp_uuid']);
            $name = $results['dcp_title'];
        }

        $version->update([
            'version_name' => $name,
            'size' => $asperaTransfer->aspera_transfer_data['bytes_written'] ?? 0,
            'creator_id' => $asperaTransfer->user_id,
            'cpl_uuid' => $uuid,
            'asset_uuid' => $assetUuid,
            'multi_cpl_uuids' => $results['cpl_multi_uuids'] ?? [],
            'is_ready' => true,
        ]);

        $version->fillS3DetailsFromTransfer($asperaTransfer);

        if (isset($results['single_root']) && $results['single_root']) {
            $version->shiftS3Paths();
        }

        $slack = (new Notify("Successful DCP Validation for \"{$version->title->friendly_title}.\""))
            ->addFieldsSection([
                ['type' => 'mrkdwn', 'text' => "*Title:* <{$url}|{$version->title->friendly_title}>"],
                ['type' => 'mrkdwn', 'text' => "*Version:* <{$vUrl}|{$version->version_name}>"],
            ])
            ->addDivider()->send();

        $asperaTransfer->save();

        // and split
        if (isset($results['multi_directory']) && $results['multi_directory']) {
            $splitResults = SplitDcp::execute($version);
            if ($splitResults['error'] === true) {
                Notify::quickMessage(":fire: There was an *error* splitting the <{$vUrl}|{$version->version_name}> --
                 {$splitResults['message']}");
            }
            else {
                Notify::quickMessage("Happily split <{$vUrl}|{$version->version_name}> into many CPLs");
            }
        }

        // and re-check any bookings that may have missing content.
        $bookings = Booking::whereNull('title_id')
            ->whereIn('overall_status', BookingStatus::getStatusesByGroup('booked'))->get();

        /** @var Booking $booking */
        foreach ($bookings as $booking) {
            /** @var Transaction $transaction */
            foreach ($booking->transactions as $transaction) {
                // these are hardcoded to produciton org IDs.
                if ($booking->organization_id  === 2) {
                    $handler = new DisneyProcessor($booking->id, $transaction->id, true);
                    $handler->processBooking();
                }
                if ($booking->organization_id  === 4) {
                    $handler = new SonyProcessor($booking->id, $transaction->id, true);
                    $handler->processBooking();
                }
            }
        }
    }
}
