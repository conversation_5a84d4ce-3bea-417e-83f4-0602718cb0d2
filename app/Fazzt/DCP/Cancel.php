<?php

namespace App\Fazzt\DCP;

use App\Fazzt\Exception;
use App\Fazzt\Json;
use App\Models\FazztTransfer;
use Illuminate\Console\Command;

class Cancel
{

    static function exec($uuid)
    {
        $params = [
            0 => $uuid,
        ];
        try {

            $response = Json::jsonCall(
                'CinemaDCPPackageCancel',
                null,
                $params,
                false);

            // this has no useful response.
            return true;
        }
        catch (\Exception $e) {
            logger($e->getMessage());
        }

        return false;

    }
}
