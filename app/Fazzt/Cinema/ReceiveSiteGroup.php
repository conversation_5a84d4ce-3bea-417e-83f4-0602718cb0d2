<?php

namespace App\Fazzt\Cinema;

use App\Fazzt\Exception;

class ReceiveSiteGroup
{
    /**
     * map p;
     * p["method"] = "CinemaReceiveSiteGroupEnumerate";
     * p["params"][0] = [];
     * p["id"] = id;
     *
     * map post;
     * // We construct map p and format it into JSON string.
     * post["Data"] = ValueToJSONString(p);
     * post["Headers"] = "Content-Type: application/json\r\n\r\n";
     * post["URL"] = "http://127.0.0.1:4039/ws/json-rpc/InvokeFazztFunction.fzt?includes=Cinema/CinemaReceiveGroupLib.fzt";
     */
}
