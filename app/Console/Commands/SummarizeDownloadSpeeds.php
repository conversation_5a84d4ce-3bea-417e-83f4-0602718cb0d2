<?php

namespace App\Console\Commands;

use App\Jobs\SummarizeDownloadRange;
use App\Models\DownloadSpeed;
use App\Models\MediaManagerSpeedSummary;
use Illuminate\Console\Command;
use DB;
use Carbon\Carbon;

class SummarizeDownloadSpeeds extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'summarize:downloads';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Take everything from the cinema site downlaods records and sum it';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // wipe it out.
        MediaManagerSpeedSummary::truncate();

        // small batch jobs due to vapor timeouts, this could take hours.
        SummarizeDownloadRange::dispatch(0);
        $this->info("Starting job fired off.");

        return Command::SUCCESS;
    }
}
