<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('bookings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('organization_id'); // org owner
            $table->unsignedBigInteger('creator_id'); // who created it
            $table->unsignedBigInteger('version_id'); // what's going
            $table->unsignedBigInteger('cinema_site_id'); // where it's going
            $table->boolean('is_electronic'); // if it's a CSX delivery
            $table->date('deliver_at'); // when it needs to be there
            $table->unsignedBigInteger('progress')->nullable(); // e-delivery progress
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('bookings');
    }
};
