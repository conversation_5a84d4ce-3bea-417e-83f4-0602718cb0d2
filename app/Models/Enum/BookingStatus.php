<?php

namespace App\Models\Enum;

use App\Models\Booking;

enum BookingStatus: string
{
    /*
     * Here's a really great explainer / coles notes for php native enums:
     *   https://stackoverflow.com/a/66208862
     *
     * The value is what is stored in the DB via `->value`, the label is for pretty display.
     * The model itself can be cast to this enum object.
     *
     * these are kinda shared between bookings and transfers
     */
    case Pending = 'pending';
    case Accepted = 'accepted';
    case Acknowledged = 'acknowledged';
    case Transmitting = 'transmitting';
    case FailedPendingRemediation = 'failed_pending_remediation';
    case FailedRemediationActioned = 'failed_remediation_actioned';
    case FailedRemediationInTransit = 'failed_remediation_in_transit';
    case Cancelled = 'cancelled';
    case CancelledAfterCompletion = 'cancelled_after_completion';
    case Incomplete = 'incomplete';
    case Completed = 'completed';
    case Delivered = 'delivered';
    case Rejected = 'rejected';
    case Error = 'error';
    case Failed = 'failed';
    case Timedout = 'timedout';
    case Retry = 'retry';
    case Verify = 'verify';

    /*
     * If you are doing a select dropdown / editor you can easily use this enum for the values and labels.
     *
     foreach (BookingStatus::cases() as $case) {
        printf('<option value="%s">%s</option>\n', $case->value, $case->label());
     }
     */
    public function label()
    {
        return match ($this) {
            BookingStatus::Pending => 'Pending',
            BookingStatus::Accepted => 'Accepted',
            BookingStatus::Acknowledged => 'Acknowledged',
            BookingStatus::Transmitting => 'Transmitting',
            BookingStatus::FailedPendingRemediation => 'Delivery Failed - Pending Remediation',
            BookingStatus::FailedRemediationActioned => 'Delivery Failed - Remediation Actioned',
            BookingStatus::FailedRemediationInTransit => 'Delivery Failed - Remediation In Transit',
            BookingStatus::Cancelled => 'Cancelled',
            BookingStatus::CancelledAfterCompletion => 'Cancelled After Completion',
            BookingStatus::Incomplete => 'Incomplete',
            BookingStatus::Delivered => 'Delivered',
            BookingStatus::Completed => 'Completed',
            BookingStatus::Rejected => 'Rejected',
            BookingStatus::Error => 'Error',
            BookingStatus::Timedout => 'Timed Out [Job not processed]',
            BookingStatus::Failed => 'Transfer Failed [CSX]',
            BookingStatus::Verify => 'Pending verification',
            BookingStatus::Retry => 'Retrying Transfers',
        };
    }

    public function groupedLabel()
    {
        return match ($this) {
            BookingStatus::Pending => 'Booked',
            BookingStatus::Accepted => 'Booked',
            BookingStatus::Acknowledged => 'Booked',
            BookingStatus::Transmitting => 'In Progress',
            BookingStatus::Verify => 'In Progress',
            BookingStatus::Cancelled => 'Completed',
            BookingStatus::CancelledAfterCompletion => 'Completed',
            BookingStatus::Delivered => 'Completed',
            BookingStatus::Completed => 'Completed',
            BookingStatus::Rejected => 'Completed',
            BookingStatus::Incomplete => 'Issues',
            BookingStatus::FailedPendingRemediation => 'Issues',
            BookingStatus::FailedRemediationActioned => 'Issues',
            BookingStatus::FailedRemediationInTransit => 'Issues',
            BookingStatus::Error => 'Issues',
            BookingStatus::Timedout => 'Issues',
            BookingStatus::Failed => 'Issues',
            BookingStatus::Retry => 'Issues'
        };
    }

    public static function contains($value): self|null
    {
        return self::tryFrom($value);
    }

    public static function getStatusesByGroup($group): array|null
    {
        return match (strtolower($group)) {
            "booked" => [BookingStatus::Pending, BookingStatus::Accepted, BookingStatus::Acknowledged],
            "active" => [BookingStatus::Pending, BookingStatus::Accepted, BookingStatus::Acknowledged, BookingStatus::Transmitting],
            "in_progress" => [BookingStatus::Transmitting, BookingStatus::Verify],
            "issues" => [
                BookingStatus::Error,
                BookingStatus::Incomplete,
                BookingStatus::Timedout,
                BookingStatus::Retry,
                BookingStatus::Failed,
                BookingStatus::FailedPendingRemediation,
                BookingStatus::FailedRemediationActioned,
                BookingStatus::FailedRemediationInTransit,
            ],
            "completed" => [
                BookingStatus::Completed,
                BookingStatus::Delivered,
                BookingStatus::Cancelled,
                BookingStatus::CancelledAfterCompletion,
                BookingStatus::Rejected,
            ],
            default => [],
        };
    }
}
