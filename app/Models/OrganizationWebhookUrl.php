<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class OrganizationWebhookUrl extends Model
{
    use SoftDeletes, HasFactory;

    protected $fillable = [
        'creator_id', 'event_model', 'event_type', 'url', 'bearer_token', 'http_headers'
    ];

    protected $casts = ['http_headers' => 'json'];

    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    public function logs() {
        return $this->hasMany(WebhookLog::class);
    }
}
