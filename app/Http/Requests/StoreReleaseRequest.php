<?php

namespace App\Http\Requests;

use App\Models\Enum\PackageTypes;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class StoreReleaseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'type' => ['required', new Enum(PackageTypes::class)],
            'package_name' => ['required', 'string'],
            'paramount_content_id' => ['sometimes', 'integer', 'nullable'],
            'attached_content' => ['sometimes', 'array'],
        ];
    }
}
