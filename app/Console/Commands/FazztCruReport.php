<?php

namespace App\Console\Commands;

use App\Fazzt\Json;
use Illuminate\Console\Command;

class FazztCruReport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fazzt:cru-report';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get a cru report';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        \App\Fazzt\Cru\Report::exec();

        return Command::SUCCESS;
    }
}
