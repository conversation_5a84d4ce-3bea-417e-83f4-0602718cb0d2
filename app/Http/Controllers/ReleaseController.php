<?php

namespace App\Http\Controllers;

use App\Models\Release;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class ReleaseController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $search = request()->get('search');
        $isPinned = filter_var(request()->get('pinned'), FILTER_VALIDATE_BOOLEAN);

        return Release::when($search, function ($q) use ($search) {
            $q->whereHas('title', function ($q) use ($search) {
                $q->where('friendly_title', 'like', "%{$search}%");
            });
            $q->orWhereHas('organization', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            });
            $q->orWhere('package_name', 'like', "%{$search}%");

        })->when($isPinned, function ($q) use ($isPinned) {
            $q->where('is_pinned', $isPinned);
        })
        ->with(['title:id,friendly_title,status,release_date', 'organization:id,name', 'content:id,size,version_name,cpl_uuid,asset_uuid'])
        ->orderBy(request()->get('sortBy', 'created_at'), request()->get('sortDirection', 'DESC'))
        ->paginate(request()->get('take', 15));

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param Release $release
     * @return Response
     */
    public function show(Release $release)
    {
        //
        $release->load(['content:id,version_name,cpl_uuid,size'])
            ->setAppends(['content_available', 'types_list', 'size']);
        return response(['data' => $release]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    public function togglePin(Release $release)
    {
        $release->is_pinned = !$release->is_pinned;
        $release->save();
        return $this->show($release);
    }


    public function getIssues(Release $release)
    {
        $search = request()->get('search');
        $sortBy = request()->get('sortBy', 'created_at');
        $sortDirection = request()->get('sortDirection', 'DESC');
        $take = request()->get('take', 15);
    
        $issues = $release->bookings()
            ->where('is_duplicate', 0)
            ->whereIn('overall_status', \App\Models\Enum\BookingStatus::getStatusesByGroup('issues'))
            ->when($search, function ($q) use ($search) {
                $q->where(function ($query) use ($search) {
                    $query->where('overall_status', 'like', "%{$search}%")
                          ->orWhereHas('organization', function ($q) use ($search) {
                              $q->where('name', 'like', "%{$search}%");
                          })
                          ->orWhereHas('cinema', function ($q) use ($search) {
                              $q->where('name', 'like', "%{$search}%");
                          });
                });
            })
            ->with([
                'organization:id,name',
                'cinema:id,name',
            ])
            ->orderBy($sortBy, $sortDirection)
            ->paginate($take);
    
        return $issues;
    }
}
