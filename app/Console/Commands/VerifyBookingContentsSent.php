<?php

namespace App\Console\Commands;

use App\Models\Booking;
use App\Models\BookingStatus;
use App\Models\Delivery;
use App\Models\Enum\BookingStatus as BS;
use App\Models\MediaManager;
use Illuminate\Console\Command;

class VerifyBookingContentsSent extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'booking:verify {bookingId}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Verify the contents of a booking exist in the destination media manager library.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $bookingId = $this->argument('bookingId');

        /** @var Booking $booking */
        $booking = Booking::findOrFail($bookingId);

        if ($booking->overall_status !== BS::Verify) {
            $this->warn("The Booking ID {$booking->id} is not in a verify status. It is likely still running or already verified.");
            return Command::FAILURE;
        }

        /** @var MediaManager $mediaManager */
        $mediaManager = $booking->cinema->primaryMediaManager;

        if (! $mediaManager) {
            $this->warn("The cinema does not have a primary media manager assigned.");
            return Command::FAILURE;
        }

        // make sure we get the most current library.
        $mediaManager->getStorage(true);
        $library = $mediaManager->media_library;

        if (! $mediaManager->isOnline()) {
            $this->warn("Media Manager is offline. Cannot verify the deliveries.");
            return Command::FAILURE;
        }

        if (count($booking->deliveries) === 0) {
            $this->warn("There were no deliveries for this booking.");
            return Command::FAILURE;
        }

        $totalTransfers = count($booking->deliveries);
        $totalVerified = 0;
        $totalMissing = 0;

        /** @var Delivery $delivery */
        foreach ($booking->deliveries as $delivery) {

            if ($delivery->version) {
                $this->info("Delivery ID {$delivery->id}");
                $this->info(" -- Looking for CPL UUID: {$delivery->version->cpl_uuid}");
                $this->info(" -- Looking for Name: {$delivery->version->version_name}");

                $found = false;

                if (count($library) > 0) {
                    foreach ($library as $libraryKey => $cplObject) {
                        if (isset($cplObject['id']) && $cplObject['id'] === $delivery->version->cpl_uuid) {
                            $found = true;
                            break;
                        }
                    }
                }
                if ($found) {
                    $this->info(" -- Content found!");
                    $totalVerified++;
                    if ($delivery->status !== BS::Completed) {
                        $delivery->status = BS::Completed;
                        $delivery->save();
                    }
                }
                else {
                    $this->warn(" -- Content does not exist in library.");
                    // retry the transfer?
//                    $delivery->status = BS::Pending;
//                    $delivery->save();
                    $totalMissing++;
                }
            }
        }

        if ($totalTransfers === $totalVerified) {
            $this->info("Delivery contents verified.");
            $booking->setOverallStatus(BS::Completed);
        }

        else {
            $this->warn("Delivery Contents Failed -- Some transfered files were not found. $totalVerified / $totalTransfers ");
            // $booking->setOverallStatus(BS::Retry);
        }
        //
        return Command::SUCCESS;
    }
}
