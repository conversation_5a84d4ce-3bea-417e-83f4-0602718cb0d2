<?php

namespace App\Http\Requests;

use App\Models\Enum\TitleStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class UpdateTitleRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'friendly_title' => ['sometimes', 'string'],
            'organization_id' => ['sometimes', 'numeric'],
            'release_date' => ['sometimes', 'date'],
            'paramount_film_id' => ['sometimes', 'string'],
            'dchub_title_id' => ['sometimes', 'string'],
            'status' => ['sometimes', new Enum(TitleStatus::class)],
        ];
    }
}
