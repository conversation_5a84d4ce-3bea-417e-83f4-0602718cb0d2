<?php

namespace App\Console\Commands;

use App\Jobs\SyncPackageToDeliveries;
use App\Models\Delivery;
use App\Models\Enum\BookingStatus;
use App\Models\Release;
use App\Models\Version;
use Illuminate\Console\Command;
use App\Models\Booking;
use App\StudioHandlers\Disney\InboundProcessor;

class SyncPackages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bookings:sync {package_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Force check a package ID to make sure it has the required transfers';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $package = Release::findOrFail($this->argument('package_id'));

        if (!$package) {
            $this->error("No package found.");
            return 999;
        }

        $this->info("dispatching job");
        SyncPackageToDeliveries::dispatch($package->id);
    }
}
