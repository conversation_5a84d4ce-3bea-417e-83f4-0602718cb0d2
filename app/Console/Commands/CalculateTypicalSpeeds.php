<?php

namespace App\Console\Commands;

use App\Models\CinemaSite;
use App\Models\DownloadSpeed;
use Illuminate\Console\Command;
use DB;

class CalculateTypicalSpeeds extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sites:update-speeds';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // grab any site that has had a recent transfer (last 48 hours?)
        $downloadRecords = DownloadSpeed::selectRaw('MAX(cinema_site_id) as cinema_site_id')
            // ->whereDate('created_at', '>', now()->subDay(2))
            ->groupBy('cinema_site_id')
            ->get();

        /** @var DownloadSpeed $downloadRecord */
        foreach ($downloadRecords as $downloadRecord) {
            $cinemaSite = $downloadRecord->cinemaSite;
            if (! $cinemaSite) { // deleted sites can be ignored.
                continue;
            }
            // $this->info($cinemaSite->name);

            // get average of all actual downloads
            $actualDownloadSpeed = DownloadSpeed::selectRaw('AVG(download_speed) as download_speed')
                ->where('is_aspera_transfer', 1)
                ->where('cinema_site_id', $cinemaSite->id)
                ->get()
                ->pluck('download_speed');

            $actualDownloadSpeed = $actualDownloadSpeed[0];

            $testSpeed = DownloadSpeed::selectRaw('AVG(download_speed) as download_speed')
                ->where('is_aspera_transfer', 0)
                ->where('cinema_site_id', $cinemaSite->id)
                ->get()
                ->pluck('download_speed');

            $testSpeed = $testSpeed[0];

            // calculate time for 150GB = 1,200 gigabits = 1,200,000 megabits.* not really, but marketing.
            $megabits = 1200000;
            $seconds = floor($megabits / $actualDownloadSpeed); // in mbps

            // store it on the cinema typical_speeds col
            $result = new \stdClass();
            $result->download_speed_in_mbps = round($actualDownloadSpeed,2);
            $result->test_download_speed_in_mbps = round($testSpeed, 2);
            $result->seconds_to_download_150GB = $seconds;

            $cinemaSite->typical_speeds = $result;
            $cinemaSite->save();

        }

        return Command::SUCCESS;
    }
}
