<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class UserRoleUpdated extends Mailable
{
    use Queueable, SerializesModels;

    public $user;
    public $newRole;

    /**
     * Create a new message instance.
     *
     * @param $user
     * @param $newRole
     */
    public function __construct($user, $newRole)
    {
        $this->user = $user;
        $this->newRole = $newRole;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $messageLine = $this->newRole === 'admin'
            ? "You've been assigned to the Admin group"
            : "Your access has been updated to read-only";

        $frontendUrl = config('app.frontend_url');
        $logoUrl = asset('images/dcdc-logo-white.png');

        return $this->subject('Your Role Has Been Updated')
            ->html(
                '<!DOCTYPE html>
                        <html lang="en">
                        <head>
                            <meta charset="UTF-8">
                            <meta name="viewport" content="width=device-width, initial-scale=1.0">
                            <title>Your Role Has Been Updated</title>
                        </head>
                        <body style="margin: 0; padding: 0; background-color: #111827; font-family: Arial, sans-serif; color: #333; text-align: center;">
                            <table width="100%" cellspacing="0" cellpadding="0" style="background-color: #111827; min-height: 100vh; width: 100%; padding: 20px 0;">
                                <tr>
                                    <td align="center">
                                        <img src="' . $logoUrl . '" alt="DCDC Logo" style="max-width: 200px; display: block;">
                                        <table width="600" cellspacing="0" cellpadding="0" style="background-color: #ffffff; border-radius: 8px; margin-top: 0;">
                                            <tr>
                                                <td align="center" style="padding: 20px;">
                                                    <p style="font-size: 16px; color: #333333;">Hello ' . $this->user->name . ',</p>
                                                    <p style="font-size: 16px; color: #333333;">' . $messageLine . '</p>
                                                    <a href="' . $frontendUrl . '" style="display: inline-block; padding: 0.5rem 1.5rem; background-color: #FFA726; color: #ffffff; border-radius: 0.5rem; text-decoration: none; font-family: sans-serif; font-size: 1rem; line-height: 1.5rem; margin-top: 20px;">Open</a>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </body>
                        </html>'
            );
    }
}
