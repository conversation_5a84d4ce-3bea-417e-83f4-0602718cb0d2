<?php

namespace Database\Seeders;

use Exception;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class PermissionSeeder extends Seeder
{
    public function run()
    {
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        $permissionMatrix = [ // within the types
            'read-write' => ['view', 'update', 'create', 'delete'], // delete will be synonymous with cancel
            'read-only' => ['view'],
        ];

        $objectMatrix = [
            'cinemas' => [
                'exhibitor' => 'read-write',
                'studio' => 'read-only',
                'vendor' => 'read-only',
            ],
            'titles' => [
                'exhibitor' => 'read-only',
                'studio' => 'read-write',
                'vendor' => 'read-only',
            ],
            'versions' => [
                'exhibitor' => 'read-only',
                'studio' => 'read-write',
                'vendor' => 'read-only',
            ],
            'bookings' => [
                'exhibitor' => 'read-only',
                'studio' => 'read-write',
                'vendor' => 'read-write',
            ],
            'reports' => [
                'exhibitor' => 'read-only',
                'studio' => 'read-only',
                'vendor' => 'read-only',
            ],
            'organizations' => [
                'exhibitor' => 'read-only',
                'studio' => 'read-only',
                'vendor' => 'read-only',
            ],
            'users' => [
                'exhibitor' => 'read-write',
                'studio' => 'read-write',
                'vendor' => 'read-write',
            ],
            'deliveries' => [
                'studio' => 'read-only',
            ],
            'imports' => [
                'studio' => 'read-write',
            ],
            'equipment' => [
                'exhibitor' => 'read-only',
                'equipment-manager' => 'read-write',
            ],
            'requests' => [
                'studio' => 'read-write',
            ],
        ];

        $actionMatrix = ['view', 'update', 'create', 'delete'];
        $ownershipMatrix = ['any', 'organization', 'own'];

        $permissionFormat = '%s-%s';
        $roleFormat = '%s-%s';
        $createdRoles = [];

        // this will effectively create all permissions for all objects
        foreach ($objectMatrix as $object => $types) {
            foreach ($actionMatrix as $action) {
                try {
                    Permission::create([
                        'name' => sprintf($permissionFormat, $action, $object),
                    ]);
                }
                catch (Exception $e) {
                }
            }
        }

        $roles = [];
        foreach ($objectMatrix as $object => $tree) {
            foreach ($tree as $rolePrefix => $permissionSet) {
                $roleName = sprintf($roleFormat, $rolePrefix, $permissionSet);
                try {
                    if (!isset($roles[$roleName])) {
                        $roles[$roleName] = Role::findOrCreate($roleName);
                    }
                    foreach ($permissionMatrix[$permissionSet] as $perm) {
                        $permissionKey = sprintf($permissionFormat, $perm, $object);
                        $roles[$roleName]->givePermissionTo($permissionKey);
                    }
                }
                catch (Exception $e) {

                }
            }
        }

        try {
            $role = Role::findOrCreate('admin');
            $role->givePermissionTo(Permission::all());

            //Permission::findOrCreate(['name' => 'restore_user']);
            $role = Role::findOrCreate('super-admin');
            $role->givePermissionTo(Permission::all());
        }
        catch (Exception $e) {
        }
    }
}
