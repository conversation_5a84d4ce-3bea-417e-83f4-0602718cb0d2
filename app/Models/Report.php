<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Str;

class Report extends Model
{
    use HasFactory;

    protected $fillable = [
        'type',
        'parameters',
        'user_id',
        'is_ready',
        'is_processing',
        'is_error',
        's3_path',
        'time_to_generate',
    ];

    protected $casts = [
        'parameters' => 'json',
        'is_ready' => 'boolean',
        'is_error' => 'boolean',
        'is_processing' => 'boolean',
        'time_to_generate' => 'integer',
    ];

    protected $appends = ['title'];

    public function getTitleAttribute() {
        $title = (Title::find($this->parameters['title_id']))->friendly_title ?? 'n/a';
        return $this->created_at->timestamp . '-' . $this->parameters['type'] . '-' . Str::slug($title) . '.csv';
    }

}
