<?php

namespace App\Console\Commands;

use App\DcHub\{Cinemas, Distributors, Requests};
use App\StudioHandlers\DcHub\OrderStatuses;
use CodeDredd\Soap\Facades\Soap;
use Illuminate\Console\Command;
use stdClass;

class DcHubSoapTest extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'dchub:test-endpoints';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'DCHub Test Things';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $requests = Requests::getRequests();
        // dump($requests);

        // Obviously this might not always work if the request disappears:
        // Requests::updateRequestStatus('03902678_00003634', OrderStatuses::COMPLETED);

        $dist = Distributors::getDistributors();
        foreach ($dist as $d) {
            if (stristr($d['name'], 'delux')) {
                dump($d);
            }
//            if ($d['distributorId'] == '1050') {
//                dump($d);
//                break;
//            }
        }
        // Cinemas::getCinemaList();

        // Cinemas::getCinemaInfo('**********');

        // Obviously this might not always work if the request disappears:
        // Requests::rerouteRequestToDeluxe('03891885');

        return 0;
    }
}

