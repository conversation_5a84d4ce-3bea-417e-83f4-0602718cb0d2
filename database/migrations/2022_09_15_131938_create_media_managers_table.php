<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('media_managers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organization_id');

            $table->string('serial_number')->unique();
            $table->string('name');
            $table->string('password')->nullable();
            $table->string('remember_token')->nullable();
            $table->string('type')->nullable();
            $table->string('kind')->nullable();

            // tunnel ports
            $table->integer('web_port')->unique()->nullable();
            $table->integer('ssh_port')->unique()->nullable();

            // json documents
            $table->json('cmms_status')->nullable();
            $table->json('media_manager_status')->nullable();
            $table->json('active_users')->nullable();
            $table->json('disk_usage')->nullable();
            $table->json('downloads_folder_tree')->nullable();
            $table->json('ftp_share_status')->nullable();
            $table->json('ip_addresses')->nullable();
            $table->json('library_jobs')->nullable();
            $table->json('media_drive_jobs')->nullable();
            $table->json('media_drives')->nullable();
            $table->json('media_library')->nullable();
            $table->json('raid_health')->nullable();
            $table->json('raid_personalities')->nullable();
            $table->json('samba_share_status')->nullable();

            // times and tings
            $table->dateTime('status_updated_at')->nullable();
            $table->dateTime('last_login_at')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('media_managers');
    }
};
