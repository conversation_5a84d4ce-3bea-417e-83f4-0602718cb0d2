<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Carbon\Carbon;
use App\Traits\HasStorage;

class MediaManager extends Authenticatable
{
    use SoftDeletes, HasFactory, HasStorage;

    protected $tunnel = 'tunnel.bitcine.com'; // todo: ovveride with data in case we have different tunnel boxes or direct connects.

    protected $fillable = [
        'organization_id',
        'serial_number',
        'name',
        'type',
        'kind',
        'media_drives',
        'media_library',
        'cmms_status',
        'media_manager_status',
        'active_users',
        'disk_usage',
        'downloads_folder_tree',
        'ftp_share_status',
        'ip_addresses',
        'network_interfaces',
        'library_jobs',
        'media_drive_jobs',
        'raid_health',
        'raid_personalities',
        'samba_share_status',
        'ssh_port',
        'web_port',
        'status_updated_at',
        'cinema_site_id',
        'public_ip',
        'is_primary',
    ];

    protected $hidden = ['password', 'remember_token'];

    protected $casts = [
        "cmms_status" => 'json',
        "media_manager_status" => 'json',
        "active_users" => 'json',
        "disk_usage" => 'json',
        "downloads_folder_tree" => 'json',
        "ftp_share_status" => 'json',
        "ip_addresses" => 'json',
        "network_interfaces" => 'json',
        "library_jobs" => 'json',
        "media_drive_jobs" => 'json',
        "media_drives" => 'json',
        "media_library" => 'json',
        "raid_health" => 'json',
        "raid_personalities" => 'json',
        "samba_share_status" => 'json',
        "web_port" => 'integer',
        "ssh_port" => 'integer',
        'status_updated_at' => 'datetime',
    ];

    protected $dates = ['status_updated_at'];

    protected $appends = ['wan_ip', 'lan_ip', 'wan_dhcp', 'lan_dhcp'];

    public function cinemaSite()
    {
        return $this->belongsTo(CinemaSite::class);
    }

    static function generatePort()
    {
        $exists = true;
        while ($exists) {
            $port = app()->environment('production') ? rand(20000, 29999) : rand(33001, 39000);
            $exists = !!(self::where(function ($q) use ($port) {
                $q->where('ssh_port', $port)->orWhere('web_port', $port);
            })->first());
        }
        return $port;
    }

    public function getDetails()
    {
        // Re-fetch data from web port
        $this->getStorage();

        $circuit = $this->cinemaSite->circuit ?? 'warehouse';
        $isWarehoused = true;
        if ($circuit !== 'warehouse') {
            $isWarehoused = false;
        }

        // Whatever the front-end needs:
        return [
            'id' => $this->id,
            'name' => $this->name,
            'type' => $this->type ?? 'CS1', // Type (CSX, CS1, Decoder)
            'serial_number' => $this->serial_number, // Serial Number
            'ssh_port' => $this->ssh_port,
            'web_port' => $this->web_port,
            'is_online' => $this->isOnline(),
            'status_updated_at' => $this->status_updated_at, //Status (online/offline/unhealthy) // Last Phoned Home
            'started_at' => $this->getUptime(),
            'media_library' => $this->media_library,
            'drives' => $this->media_drives,
            'settings' => $this->settings,
            'downloads' => $this->downloads_folder_tree,
            'cinema_site_id' => $this->cinema_site_id,
            'cinema_site' => $this->cinemaSite,
            'is_primary' => $this->is_primary, //Primary (yes/no)
            'ip_addresses' => $this->ip_addresses,
            'network_interfaces' => $this->network_interfaces,
            'is_warehoused' => $isWarehoused,
            'public_ip' => $this->public_ip, //Public IP
            'raid_status' => $this->getRaidStatus(),
            'wan_ip' => $this->getWanIp(), // any interface that is flagged as 		"isInternetConnected": true
            'wan_dhcp' => $this->getWanDhcp(), // any interface that is flagged as 		"isInternetConnected": true
            'lan_ip' => $this->getLanIp(), // any other interfaces.
            'lan_dhcp' => $this->getLanDhcp(), // any other interfaces.
            'network_interfaces' => $this->getNetworkInterfaces(),
        ];
    }

    public function getLanIp()
    {
        if (!$this->network_interfaces) {
            return [];
        }
        foreach ($this->network_interfaces as $interface => $details) {
            if ($details['isInternetConnected'] === false) {
                return $this->getIpByInterface($details['device']);
            }
        }
    }

    public function getLanDhcp()
    {
        if (!$this->network_interfaces) {
            return false;
        }
        foreach ($this->network_interfaces as $interface => $details) {
            if ($details['isInternetConnected'] === false) {
                return isset($details['method']) && $details['method'] === 'auto';
            }
        }
    }

    public function getWanDhcp()
    {
        if (!$this->network_interfaces) {
            return false;
        }
        foreach ($this->network_interfaces as $interface => $details) {
            if ($details['isInternetConnected'] === true) {
                return isset($details['method']) && $details['method'] === 'auto';
            }
        }
    }

    public function getWanIp()
    {
        if (!$this->network_interfaces) {
            return [];
        }

        foreach ($this->network_interfaces as $interface => $details) {
            if ($details['isInternetConnected'] === true) {
                return $this->getIpByInterface($details['device']);
            }
        }
        return [];
    }

    private function getIpByInterface($interface)
    {
        //
        $addresses = [];

        if (!$this->ip_addresses) {
            return [];
        }

        if (isset($this->ip_addresses[$interface])) {
            foreach ($this->ip_addresses[$interface] as $key => $value) {
                $addresses[] = $value["address"];
            }
        }
        return $addresses;
    }

    private function getUptime()
    {
        // Let's just pull the date from the "media-manager" app:
        $data = $this->media_manager_status[0] ?? "";
        $exploded = explode("active (running) since", $data);
        if (isset($exploded[1])) {
            $index = strpos($exploded[1], ";");
            $date = substr($exploded[1], 0, $index);
            return Carbon::create($date);
        }
    }

    public function activityWithIp($description, $event)
    {
        activity()
            ->withProperties([
                'ip' => request()->ip(),
                'agent' => request()->userAgent(),
            ])
            ->causedBy($this)
            ->event($event)
            ->useLog('system')
            ->log($description);
    }

    public function getJWTSecret()
    {
        return str_replace(['+', '/', '='], ['-', '_', ''], base64_encode(random_bytes(43)));
    }

    public function getFreshJWT($payload, $secret)
    {
        $encodedHeader = $this->getJWTHeader();
        $encodedPayload = $this->getJWTPayload($payload);
        $encodedSignature = hash_hmac(
            'sha256',
            $encodedHeader . "." . $encodedPayload,
            $secret,
            true
        );
        $base64UrlSignature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($encodedSignature));

        return $encodedHeader . '.' . $encodedPayload . '.' . $base64UrlSignature;
    }

    private function getJWTHeader()
    {
        $header = json_encode([
            'typ' => 'JWT',
            'alg' => 'HS256',
        ]);

        return str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
    }

    private function getJWTPayload($payload)
    {
        $payload = json_encode(array_merge([
                'iss' => 'DCDC',
                'sub' => 'MediaManagerApiAccess',
                'aud' => "CS1",
                'exp' => Carbon::now()->addSeconds(30)->timestamp,
            ],
                $payload)
        );
        return str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));
    }

    public function canReceiveJobs()
    {
        return true;
    }

    public function jobs()
    {
        return $this->hasMany(MediaManagerJob::class);
    }

    public function notes()
    {
        return $this->morphMany(Note::class, 'notable');
    }

    public function getLanIpAttribute()
    {
        return $this->getLanIp();
    }

    public function getLanDhcpAttribute()
    {
        return $this->getLanDhcp();
    }

    public function getWanIpAttribute()
    {
        return $this->getWanIp();
    }

    public function getWanDhcpAttribute()
    {
        return $this->getWanDhcp();
    }

    public function getRaidStatus()
    {
        $drives = json_decode($this->getRawOriginal('media_drives'), true) ?? [];
        $disks = [];

        $count = 0;
        foreach ($drives as $drive) {
            if (!isset($drive['partitions']) || !count($drive['partitions'])) {
                continue;
            }

            $partition = $drive['partitions'][0];
            $isRaid = ($partition['fs_type'] === 'linux_raid_member');

            $details = [
                'title' => 'Disk ' . $count++,
                'size' => $partition['size'],
                'location' => $drive['device'],
                'storage_pool' => $isRaid ? '/dev/md0' : $drive['device'],
                'estimated_lifespan' => '100%',
                'bad_record_count' => 0,
                'model' => $drive['model'],
                'serial_number' => $drive['model'],
            ];

            if ($drive = $this->drives()
                ->where('drive', $drive['device'])
                ->select(['smart_data'])
                ->first()) {

                $driveDetails = [
                    'serial_number' => $drive->smart_data['serial_number'] ?? 'N/A',
                    'firmware_version' => $drive->smart_data['firmware_version'] ?? 'N/A',
                    'temperature' => sprintf('%s ºC', ($drive->smart_data['temperature'] ?? 'N/A')),
                    'health_status' => $drive->health_status ?? 'N/A',
                ];

                $details = array_merge($details, $driveDetails);
            }


            $disks[] = $details;

        }

        $raidData = $this->raid_health;
        $raidName = "N/A";

        if (isset($raidData)) {
            $raidLevel = $raidData['raid_level'] ?? 'N/A';
            $state = $raidData['state'] ?? null;
            $healthStatus = $state === 'clean' ? 'Normal' : 'Alert';
            $raidName = $raidLevel . ($state ? " ({$state})" : null);
        }


        return [
            'health_status' => $healthStatus ?? 'Alert',
            'raid_name' => $raidName,
            'format_type' => 'ext4',
            'storage' => [
                'used' => $this->disk_usage ? $this->disk_usage['size'] - $this->disk_usage['free'] : 0,
                'total' => $this->disk_usage ? $this->disk_usage['size'] : 0,
            ],
            'disks' => $disks,
        ];
    }

    public function getNetworkInterfaces()
    {
        $interfaces = [];
        $ipAddresses = $this->ip_addresses;
        foreach ($this->ip_addresses as $interfaceName => $value) {
            if ($interfaceName === 'lo') {
                continue;
            }
            $dns = '--';
            $bytesReceived = 0;
            $bytesTransmitted = 0;
            $isInternetConnected = false;
            if ($this->network_interfaces) {
                foreach ($this->network_interfaces as $interface) {
                    if ($interface['interface-name'] === $interfaceName) {
                        $systemName = $interface['name'];
                        $method = $interface['method'];
                        $dns = $interface['dns'];
                        $gateway = $interface['gateway'];
                        $bytesReceived = $interface['receivedBytes'] ?? 0;
                        $bytesTransmitted = $interface['transmittedBytes'] ?? 0;
                        $isInternetConnected = $interface['isInternetConnected'] ?? false;
                    }
                }
            }
            $macAddress = $value[0]['mac'];
            $interfaces[] = array_merge($value[0], [
                'name' => $interfaceName,
                'label' => isset($this->interfaces)
                    ? ($this->interfaces[$macAddress]['label'] ?? null)
                    : null,
                'description' => isset($this->interfaces)
                    ? ($this->interfaces[$macAddress]['description'] ?? null)
                    : null,
                'system_name' => $systemName ?? null,
                'method' => $method ?? null,
                'dns' => $dns,
                'gateway' => $gateway ?? null,
                'bytes_received' => $bytesReceived ?? 0,
                'bytes_transmitted' => $bytesTransmitted ?? 0,
                'is_internet_connected' => $isInternetConnected ?? false,
            ]);
        }
        return $interfaces;
    }

    public function getRaidHealthAttribute($value)
    {
        $infos = [];

        if (is_array($value) && isset($value[0])) {
            $value = $value[0];
        }
        if (is_string($value)) {
            $infos = explode("\n", json_decode($value));
        }

        $raidData = [];
        foreach ($infos as $info) {

            $colonPos = strpos($info, ":");

            // This isn't a proper stat, continue.
            if ($colonPos === false) {
                continue;
            }

            $key = substr($info, 0, $colonPos);

            $value = substr($info, $colonPos + 1);
            if ($key && $value) {
                $key = str_replace(" ", "_", trim($key));
                $key = strtolower($key);
                // We won't track the update_time since it's just the present time.
                if ($key === 'update_time') {
                    continue;
                }
                $raidData[$key] = trim($value);
            }

        }

        return $raidData;
    }

    public function drives()
    {
        return $this->hasMany(MediaManagerDrive::class);
    }
}
