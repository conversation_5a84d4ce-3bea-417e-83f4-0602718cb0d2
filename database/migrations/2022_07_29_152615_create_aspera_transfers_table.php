<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('aspera_transfers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('title_id')->nullable(); // what title it belongs to
            $table->foreignId('version_id')->nullable(); // what version this is for
            $table->foreignId('user_id')->nullable(); // who uploaded
            $table->uuid('transfer_uuid')->nullable(); // to build the path for uploads, and track download transfers
            $table->string('status');
            $table->enum('direction', ['download', 'upload']);
            $table->json('transfer_spec'); // the config sent to start the transfer
            $table->json('aspera_transfer_data'); // the payload returned from aspera
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('aspera_transfers');
    }
};
