<?php

namespace App\Actions\Slack;

use <PERSON><PERSON>\SlackAlerts\Facades\SlackAlert;

/**
 * A slack notification utility via the new Blocks API so we can send prettier messages.
 * https://app.slack.com/block-kit-builder
 */
class Notify
{

    /* This supports <PERSON>lack's markdown - https://slack.com/help/articles/202288908-Format-your-messages */
    private array $blocks = [];

    public function __construct(string $title = null)
    {
        if ($title) {
            $this->addHeaderSection($title);
        }
    }

    public function addDivider()
    {
        $this->blocks[] = ['type' => 'divider'];

        return $this;
    }

    public function addHeaderSection($headerText)
    {
        $this->blocks[] = [
            'type' => 'header',
            'text' => ['type' => 'plain_text', 'text' => $headerText],
        ];

        return $this;
    }

    public function addFieldsSection(array $fields)
    {
        /** in this situation $fields must be an array of text blocks, they will be displayed
         * in columns.
         * $fields = [
         *    ["type" => 'plain_text', 'text' => 'this is text'],
         *    ["type" => 'mrkdwn', 'text' => '<example.com|this is a link>'],
         * ]
         */
        $this->blocks[] = [
            'type' => 'section',
            'fields' => $fields,
        ];
        return $this;
    }

    public function addTextSection(array $block)
    {
        /** in this situation block must be an array with eitehr mrkdn or plain_text
         * $block = ['type' => 'mrkdwn', 'text' => ' *booooo!* :ghost: ']
         * $block = ['type' => 'plain_text', 'text' => 'this is boring']
         */
        $this->blocks[] = [
            'type' => 'section',
            'text' => $block,
        ];
        return $this;
    }

    static function quickMessage($text)
    {
        SlackAlert::message($text);
    }

    public function send()
    {
        if (count($this->blocks) > 0) {
            // todo: if blocks exceeds 50, we need to split it.
            SlackAlert::blocks($this->blocks);
            $this->blocks = [];
        }
        else {
            logger()->warning("No blocks to send as a slack message.");
        }
    }

}
