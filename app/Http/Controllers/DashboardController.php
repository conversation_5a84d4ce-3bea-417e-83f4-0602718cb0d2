<?php

namespace App\Http\Controllers;

use App\Actions\Dcp\VerifyDcp;
use App\Models\AsperaTransfer;
use App\Models\Booking;
use App\Models\BookingStatus;
use App\Models\Title;
use App\Models\CinemaSite;
use App\Models\Delivery;
use App\Models\Enum\SiteStatus;

class DashboardController extends Controller
{
    function studio()
    {
        $deliveries = [];
        $bookings = [];

        if (auth()->user()->canBoth('view-bookings')) {
            $bookings = [
                'pending' => auth()->user()->organization->bookings()->whereIn('overall_status',
                    \App\Models\Enum\BookingStatus::getStatusesByGroup('booked'))
                    ->whereIsDuplicate(false)->whereDate('updated_at',
                        '>', now()->subDays(30))->count(),
                'transmitting' => auth()->user()->organization->bookings()->whereIn('overall_status',
                    \App\Models\Enum\BookingStatus::getStatusesByGroup('in_progress'))->whereIsDuplicate(false)->whereDate('updated_at',
                    '>', now()->subDays(30))->count(),
                'completed' => auth()->user()->organization->bookings()->whereIn('overall_status',
                    \App\Models\Enum\BookingStatus::getStatusesByGroup('completed'))->whereIsDuplicate(false)->whereDate('updated_at',
                    '>', now()->subDays(30))->count(),
                'error' => auth()->user()->organization->bookings()->whereIn('overall_status',
                    \App\Models\Enum\BookingStatus::getStatusesByGroup('issues'))->whereIsDuplicate(false)->whereDate('updated_at',
                    '>',
                    now()->subDays(30))->count(),
            ];
        }

        return [
            'status' => 'ok',
            'last_update' => now(),
            'bookings' => $bookings,
        ];
    }

    function dashboard()
    {
        $sites = [];
        $deliveries = [];
        $titles = [];
        $bookings = [];

        if (auth()->user()->canBoth('view-cinemas')) {
            $sites = [
                'online' => CinemaSite::where('status', SiteStatus::Online)->orWhere('status',
                    SiteStatus::Recovered)->count(),
                'pending' => CinemaSite::where('status', SiteStatus::Pending)->count(),
                'fault' => CinemaSite::where('status', SiteStatus::Fault)->count(),
            ];
        }

        if (auth()->user()->canBoth('view-deliveries')) {
            $deliveries = [
                'transmitting' => Delivery::where('status', 'transmitting')->count(),
                'queued' => Delivery::where('status', 'pending')->count(),
                'fault' => Delivery::where('status', 'error')->count(),
                'completed' => Delivery::where('status', 'completed')->count(),
                'speed' => Delivery::where('status', 'transmitting')->sum('speed_in_mbps'),
            ];
        }

        if (auth()->user()->canBoth('view-bookings')) {
            $bookings = [
                'pending' => Booking::whereIn('overall_status',
                    \App\Models\Enum\BookingStatus::getStatusesByGroup('booked'))
                    ->whereIsDuplicate(false)->whereDate('updated_at',
                        '>', now()->subDays(30))->count(),
                'transmitting' => Booking::whereIn('overall_status',
                    \App\Models\Enum\BookingStatus::getStatusesByGroup('in_progress'))->whereIsDuplicate(false)->whereDate('updated_at',
                    '>', now()->subDays(30))->count(),
                'completed' => Booking::whereIn('overall_status',
                    \App\Models\Enum\BookingStatus::getStatusesByGroup('completed'))->whereIsDuplicate(false)->whereDate('updated_at',
                    '>', now()->subDays(30))->count(),
                'error' => Booking::whereIn('overall_status',
                    \App\Models\Enum\BookingStatus::getStatusesByGroup('issues'))->whereIsDuplicate(false)->whereDate('updated_at',
                    '>',
                    now()->subDays(30))->count(),
            ];
        }

        if (auth()->user()->canBoth('view-titles')) {
            $titleData = Title::select(['id', 'friendly_title', 'release_date', 'status'])->whereDate('release_date',
                '>',
                now())->orderBy('release_date', 'asc')->get();

            $titles = [
                'headers' => [
                    ["key" => "id", "label" => "ID", "isVisible" => false],
                    ["key" => "friendly_title", "label" => "Title", "isVisible" => true],
                    ["key" => "release_date", "label" => "Release Date", "isDate" => true, "isVisible" => true],
                ],
                'values' => $titleData,
            ];
        }

        return [
            'status' => 'ok',
            'last_update' => now(),
            'sites' => $sites,
            'deliveries' => $deliveries,
            'titles' => $titles,
            'bookings' => $bookings,
        ];
    }
}
