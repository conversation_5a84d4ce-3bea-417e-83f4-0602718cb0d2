<?php

namespace App\Models;

use App\Models\Enum\TitleStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Title extends Model
{
    use SoftDeletes, HasFactory, LogsActivity;

    protected $fillable = [
        'friendly_title',
        'creator_id',
        'organization_id',
        'paramount_film_id',
        'dchub_title_id',
        'release_date',
        'status',
        'booking_counts',
    ];

    protected $dates = ['release_date'];

    protected $appends = ['status_label'];

    protected $casts = [
        'status' => TitleStatus::class,
        'booking_counts' => 'json',
    ];

    public function fillBookingCounts()
    {
        $this->booking_counts = [
            'pending' => $this->bookings()->where('is_duplicate', 0)->whereIn('overall_status',
                \App\Models\Enum\BookingStatus::getStatusesByGroup('booked'))->count(),
            'in_progress' => $this->bookings()->where('is_duplicate', 0)->whereIn('overall_status',
                \App\Models\Enum\BookingStatus::getStatusesByGroup('in_progress'))->count(),
            'completed' => $this->bookings()->where('is_duplicate', 0)->whereIn('overall_status',
                \App\Models\Enum\BookingStatus::getStatusesByGroup('completed'))->count(),
            'errors' => $this->bookings()->where('is_duplicate', 0)->whereIn('overall_status',
                \App\Models\Enum\BookingStatus::getStatusesByGroup('issues'))->count(),
        ];
        $this->saveQuietly();
    }

    public function versions()
    {
        return $this->hasMany(Version::class);
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    public function uploads()
    {
        return $this->hasMany(AsperaTransfer::class);
    }

    public function bookings()
    {
        return $this->hasMany(Booking::class);
    }

    public function links()
    {
        return $this->hasMany(TitleUploadLink::class);
    }

    public function deliveries()
    {
        return $this->hasMany(Delivery::class);
    }

    public function releases()
    {
        return $this->hasMany(Release::class);
    }

    public function delete()
    {
        // also soft delete relations.
        $this->versions()->delete();
        $this->uploads()->delete();
        $this->bookings()->delete();
        parent::delete();
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->dontSubmitEmptyLogs()
            ->logFillable()
            ->logOnlyDirty();
    }

    public function getStatusLabelAttribute()
    {
        return $this->status->label();
    }
}
