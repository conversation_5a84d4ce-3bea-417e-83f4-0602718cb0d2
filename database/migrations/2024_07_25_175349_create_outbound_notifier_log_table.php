<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('outbound_notifier_log', function (Blueprint $table) {
            $table->id();

            $table->foreignId('booking_id');
            $table->string('status');
            $table->boolean('completed');
            $table->integer('response_code');
            $table->json('response_body');

            // for replays
            $table->string('notification_class')->nullable();
            $table->json('notification_parameters')->nullable();

            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('outbound_notifier_log');
    }
};
