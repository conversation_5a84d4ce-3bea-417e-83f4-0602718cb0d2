<?php

namespace App\Http\Controllers;

use App\Events\CreateImportBookings;
use App\Events\ImportBookingsUpload;
use App\Http\Requests\StoreImportBookingRequest;
use App\Http\Requests\UpdateImportBookingRequest;
use App\Models\Booking;
use App\Models\ImportBooking;
use App\Models\ImportBookingRecord;
use App\Models\User;
use App\Policies\ImportBookingPolicy;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\HeadingRowImport;
use PhpOffice\PhpSpreadsheet\Exception;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Reader\IReader;

class ImportBookingController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
        if (auth()->user()->isAdmin()) {
            return ImportBooking::latest()->limit(10)->whereVerified(1)->paginate();
        }
        else {
            return auth()->user()->organization->imports()->latest()->limit(10)->whereVerified(1)->paginate();
        }


    }

    public function filtered(ImportBooking $importBooking, $filter) {
        $method = $filter . "Records";
        return $importBooking->$method()->paginate();
    }

    public function fireCreateBookingJob(ImportBooking $importBooking) {
        if (auth()->user()->can('create', Booking::class)) {
            event(new CreateImportBookings($importBooking->id));
            return ['status' => 'ok'];
        }
    }

    /**
     * POST Store a newly created resource in storage.
     *
     * @param \App\Http\Requests\StoreImportBookingRequest $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreImportBookingRequest $request)
    {
        //

        $importBooking = ImportBooking::create([
            'user_id' => auth()->user()->id,
            'organization_id' => auth()->user()->organization_id,
            'origin_name' => request()->post('file_list')[0]['name'],
        ]);

        // return just the basics for uppy to send the file to the appropriate endpoint.
        return [
            'upload_url' => route('import-bookings.update', $importBooking),
            'upload_details' => [
                '_id' => $importBooking->id,
            ],
        ];

    }

    /**
     * Display the specified resource.
     *
     * @param \App\Models\ImportBooking $importBooking
     * @return \Illuminate\Http\Response
     */
    public function show(ImportBooking $importBooking)
    {
        // display the import booking.
        $importBooking->load('release');

        return ['data' => $importBooking];
    }

    /**
     * PUT: Update the specified resource in storage.
     *
     * @param \App\Http\Requests\UpdateImportBookingRequest $request
     * @param \App\Models\ImportBooking $importBooking
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateImportBookingRequest $request, ImportBooking $importBooking)
    {
        if (count(request()->all()) === 0) {
            // uppy has a two pass method.
            // there were no values passed in, this must be a file attempt.
            try {
                $tempFile = storage_path(uniqid());
                $fileContents = request()->getContent(true);
                file_put_contents($tempFile, $fileContents);

                // verify the file can be opened by this library.
                $identify = IOFactory::identify($tempFile);
                logger('upload update with: ' . $identify);

                if ($identify) {
                    // arbitrarily attempt to get the header from within the first five rows
                    $importBooking->detected_type = $identify;
                    // this should cover most title/mastheads/fancy sheets etc.
                    $found = false;

                    for ($i = 1; $i < 5; $i++) {
                        $headings = (new HeadingRowImport($i))->toCollection($tempFile, null, $identify);
                        // this array is [ document [ sheets [ headers ] ]
                        $validHeadings = $headings[0][0]->filter(function ($value, $index) {
                            // if the index matches the name it means it's not named.
                            // 1 == 1 vs. 1 == "theatre_id"
                            return $value != $index;
                        });
                        // we want a minimum viable number of headings (theatre name, some ids maybe) to build
                        // a mapper.
                        if ($validHeadings->count() >= 5) {
                            $found = true;
                            $importBooking->header_row_index = $i;
                            break;
                        }
                    }
                    if (!$found) {
                        $importBooking->error_message = "Could not find suitable headers in the file. Please try again.";
                    }
                    else {
                        // send file to s3
                        $importBooking->headers = $headings[0][0]->toArray();
                        $destiny = 'i/' . uniqid(md5($importBooking->origin_name));
                        try {
                            Storage::disk('s3')->put($destiny, file_get_contents($tempFile));
                            $importBooking->s3_file = $destiny;
                        }
                        catch (\Exception $e) {
                            $importBooking->error_message = $e->getMessage();
                        }
                    }
                }
            }
            catch (Exception $e) {
                // invalid file, nothing to worry about.
                $importBooking->error_message = "Invalid file uploaded. Please try again.";
            }
            finally {
                unlink($tempFile);
            }
        }
        else {
            // this is where regular fields are updated.
            // save the mapping if it's presented.
            // then dispatch the actual job.
            $mapping = [];
            if (request()->post('mapping')) {
                foreach (request()->post('mapping') as $key => $map) {
                    $mapping[$key] = $map["value"];
                }

                $versionIds = request()->post('version_ids', []);
                if (!is_array($versionIds)) {
                    $versionIds = [$versionIds];
                }

                $importBooking->mapping = $mapping;
                $importBooking->title_id = request()->post('title_id');
                $importBooking->version_ids = $versionIds;
                $importBooking->package_id = request()->post('package_id');
                $importBooking->verified = true;
                $importBooking->release_date = request()->post('release_date');
                $importBooking->engagement_start = request()->post('engagement_start');
            }
        }

        if ($importBooking->isDirty()) {
            $importBooking->save();
            if (!$importBooking->processed && $importBooking->verified) {
                event(new ImportBookingsUpload($importBooking->id));
            }
        }

        return $this->show($importBooking);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\Models\ImportBooking $importBooking
     * @return \Illuminate\Http\Response
     */
    public function destroy(ImportBooking $importBooking)
    {
        //
    }
}
