<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('media_manager_speed_summary', function (Blueprint $table) {
            $table->id();

            $table->foreignId('media_manager_id');
            $table->foreignId('cinema_site_id');

            $table->integer('year');
            $table->integer('day_of_year');
            $table->integer('hour_of_day');

            $table->double('min_speed', 6,2)->default(0);
            $table->double('max_speed', 6,2)->default(0);
            $table->double('average_speed', 6, 2)->default(0);

            $table->integer('sample_count')->default(0);
            $table->integer('sample_sum')->default(0);

            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('media_manager_speed_summary');
    }
};
