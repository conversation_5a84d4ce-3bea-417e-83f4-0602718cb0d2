<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OutboundNotifierLog extends Model
{
    use HasFactory;

    protected $table = 'outbound_notifier_log';

    protected $fillable = [
        'booking_id',
        'status',
        'completed',
        'response_code',
        'response_body',
        'notification_class',
        'notification_parameters',
    ];

    protected $casts = [
        'status' => \App\Models\Enum\BookingStatus::class,
        'completed' => 'boolean',
        'response_body' => 'json',
        'notification_parameters' => 'json',
    ];

    public function booking()
    {
        return $this->belongsTo(Booking::class);
    }
}
