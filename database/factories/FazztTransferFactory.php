<?php

namespace Database\Factories;

use App\Models\Version;
use Illuminate\Database\Eloquent\Factories\Factory;
use Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\FazztTransfer>
 */
class FazztTransferFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            // this can't exist without FazztContent->asset_uuid
        ];
    }

    public function onContent(Version $version)
    {
        return $this->state(function (array $attributes) use ($version) {
            $duration = ($version->size / 50000000 ) * 1000; // duration is ms
            $minutes = $duration / 1000 / 60;

            return [
                'transmit_id' => Str::uuid()->toString(),
                'duration' => $duration,
                'asset_uuid' => $version->asset_uuid,
                'estimated_minutes' => $minutes,
            ];
        });
    }
    public function asInProgress() {
        return $this->state(function (array $attributes) {
            return [
                'state' => 'TRANSMITTING',
                'status'=> 'PROCESSING',
                'percent' => random_int(0,75),
            ];
        });
    }

    public function asComplete() {
        return $this->state(function (array $attributes) {
            return [
                'state' => 'COMPLETE',
                'status'=> 'SUCCESS',
                'percent' => 100,
            ];
        });
    }

}
