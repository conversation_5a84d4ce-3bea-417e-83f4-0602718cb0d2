<?php

namespace App\Console\Commands;

use App\Jobs\VerifyBooking;
use App\Models\Booking;
use App\Models\Enum\BookingStatus;
use Illuminate\Console\Command;

class ReVerifyBookings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bookings:re-verify';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Submit a job to re-verify contents for tale verify statuses';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $bookings = Booking::where('overall_status', BookingStatus::Verify)->latest()->get();
        foreach ($bookings as $booking) {
            $this->info("Dispatching {$booking->id}");
            VerifyBooking::dispatch($booking->id);
        }
        return Command::SUCCESS;
    }
}
