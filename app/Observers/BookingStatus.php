<?php

namespace App\Observers;

class BookingStatus
{

    public $afterCommit = true;

    public function created(\App\Models\BookingStatus $bookingStatus)
    {
        $booking = $bookingStatus->booking;
        if (!$booking) {
            return;
        }

        // update the parent's overall_status for counts...
        $booking->overall_status = $bookingStatus->status->value;
        $booking->save();

        if (!$booking->notification_class) {
            return;
        }

        if (method_exists($booking->notification_class, $bookingStatus->status->value)) {
            $booking->notification_class::{$bookingStatus->status->value}($booking);
        }
    }
}
