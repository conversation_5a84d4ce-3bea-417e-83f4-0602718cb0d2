<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //
        // add view cinemas to studio role
        try {
            \Illuminate\Support\Facades\DB::table('role_has_permissions')->insert([
                'permission_id' => 1,
                'role_id' => 5
            ]);
        } catch (Illuminate\Database\QueryException $e) {
            // duplicate, ignore.
        }

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
        \Illuminate\Support\Facades\DB::table('role_has_permissions')->where(['permission_id' => 1, 'role_id' => 5])->delete();
    }
};
