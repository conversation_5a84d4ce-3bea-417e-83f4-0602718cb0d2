<?php

namespace App\Listeners;

use Illuminate\Contracts\Queue\ShouldQueue;
use App\Events\DispatchWebhook;
use Illuminate\Support\Facades\Http;

class ProcessWebhook implements ShouldQueue
{

    public function handle(DispatchWebhook $event)
    {

        if (!$event->data->organization) {
            return;
        }

        $webhooks = $event->data->organization->webhooks()
            ->where('event_model', $event->name)
            ->where('event_type', $event->type)
            ->get();

        foreach ($webhooks as $webhook) {
            // emit,
            $headers = [];
            if ($webhook->bearer_token) {
                $headers['Authorization'] = "Bearer {$webhook->bearer_token}";
            }
            if ($webhook->http_headers) {
                $headers = array_merge($headers, json_decode(json_encode($webhook->http_headers), JSON_OBJECT_AS_ARRAY));
            }

            $response = Http::withHeaders($headers)->post($webhook->url, $event->data);
            // log,
            $webhook->logs()->create([
                'payload' => $event->data,
                'headers' => $headers,
                'http_status' => $response->status(),
                'http_error' => $response->reason(),
                'http_body' => $response->body()
            ]);
            // carry on.
        }
    }
}
