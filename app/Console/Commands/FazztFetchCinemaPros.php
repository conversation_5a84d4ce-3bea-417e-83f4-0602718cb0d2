<?php

namespace App\Console\Commands;

use App\Fazzt\Json;
use App\Models\CinemaProServer;
use Illuminate\Console\Command;

class FazztFetchCinemaPros extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fazzt:fetch-cinema-pros';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch all the Cinema Pros and shove them into the tables.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        \App\Fazzt\Cinema\ReceiveSites::exec();

        return Command::SUCCESS;
    }
}
