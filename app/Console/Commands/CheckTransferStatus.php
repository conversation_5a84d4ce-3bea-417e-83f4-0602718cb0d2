<?php

namespace App\Console\Commands;

use App\Models\Booking;
use App\Models\Delivery;
use App\Models\Enum\BookingStatus;
use App\Models\MediaManagerJob;
use Illuminate\Console\Command;

class CheckTransferStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:transfers';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check pending transfers and make sure they are flowing';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // flip this to bookings that are pending or transmitting

        // then make sure each booking has at least one active delivery...
        $pendingBookings = Booking::whereIn('overall_status', BookingStatus::getStatusesByGroup('active'))
            ->where('is_duplicate', 0)->oldest()->get();

        $cinemas = [];

        /** @var Booking $booking */
        foreach ($pendingBookings as $booking) {
            $this->info("Checking booking ID: {$booking->id}");

            // only deal with one pending booking per site
            if (isset($cinemas[$booking->cinema_site_id])) {
                $this->warn("-- Cinema Id {$booking->cinema_site_id} already active.");
                continue;
            }

            $package = $booking->release;
            if (!$package) {
                $this->warn("-- Release ID {$booking->package_id} for this booking does not exist.");
                //  $booking->delete();
                continue;
            }

            if ($package->content->count() === 0) {
                $this->warn("-- No package contents for Booking ID  {$booking->id}");
                continue; // nothing to send at this time (probably waiting for disney content, or a package to be built.
            }

            if ($booking->deliveries->count() === 0) {
                $this->warn("-- No deleveries created for Booking ID  {$booking->id} - package sync may not have run yet.");
                continue; // nothing to send at this time (probably waiting for disney content, or a package to be built.
            }

            // find out if this booking has any transfers
            $this->info("-- Booking has {$booking->deliveries->count()} transfers.");

            // make sure one is transmitting
            /** @var Delivery $delivery */
            foreach ($booking->deliveries as $delivery) {

                if (isset($cinemas[$booking->cinema_site_id])) {
                    continue;
                }

                if ($delivery->status === BookingStatus::Completed) {
                    continue;
                }

                $this->info("-- Delivery ID {$delivery->id} has package {$delivery->package_id} for cinema site {$delivery->cinema_site_id}");

                // if it's not a dupe, go to town.
                $jobs = $delivery->jobs()->whereIn('status', ['pending', 'transmitting', 'triggered', 'preparing'])->oldest()->get();
                if ($jobs->count() > 0) {
                    /** @var MediaManagerJob $job */
                    foreach ($jobs as $job) {
                        $this->info("-- job {$job->id} - status {$job->status}");
                        // has pending jobs, check how old they are, if they are
                        $job->checkCinesendJob();

                        if ($job->status == 'pending') {
                            $this->info("-- has been pending since {$job->created_at->toRfc822String()}");
                        }

                        if ($job->status == 'transmitting') {
                            $this->info("-- transmitting still?");
                            if ($job->updated_at < now()->subHour(12)) {
                                $this->info('-- No transmit update in 12 hours, flagging timed out. Allow for manual retries.');
                                $job->status = 'error';
                                $job->save();
                                $delivery->status = BookingStatus::Error;
                            }
                        }
                        if ($job->status == 'completed') {
                            $delivery->status = BookingStatus::Completed;
                        }
                    }
                }
                else {
                    // if it's fresh
                    $this->info("-- Creating a Job");
                    // only on non-duplicates
                    if ($booking->is_electronic) {
                        try {
                            $delivery->sendDownloadJob(); // this will detect dupe content automatically.
                        }
                        catch (\Exception $e) {
                            $this->warn("-- Cinesend Warning: " . $e->getMessage());
                        }
                    }
                    else {
                        $this->info('-- but it is not electronic.');
                    }
                }
                $cinemas[$booking->cinema_site_id] = true;
                $delivery->save();
            }
        }

        return Command::SUCCESS;
    }
}
