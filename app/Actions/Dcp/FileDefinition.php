<?php

namespace App\Actions\Dcp;

/** this is a cheap hack for a 'struct', instead of doing stdClass/assignments all the time. */
class FileDefinition
{
    public string $uuid = '';
    public string $s3key = '';
    public string $s3destination = '';
    public string $hash = '';
    public string $fileName = '';
    public int $fileSize = 0;
    public bool $verified = false;
    public bool $isPkl = false;
    public bool $isCpl = false;

    public function __construct($uuid, $s3key, $fileSize)
    {
        $this->uuid = $uuid;
        $this->s3key = $s3key;
        $this->fileName = basename($s3key);
        $this->fileSize = $fileSize;
    }
}
