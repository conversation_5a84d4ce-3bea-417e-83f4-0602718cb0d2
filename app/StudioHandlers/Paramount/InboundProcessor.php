<?php

namespace App\StudioHandlers\Paramount;

use App\Actions\Dcp\VerifyDcp;
use App\Actions\Slack\Notify;
use App\Jobs\SyncPackageToDeliveries;
use App\Models\{Booking, CinemaSite, Release, StudioTransaction, Version};
use App\Models\Enum\BookingStatus;
use Storage;
use Carbon\Carbon;

class InboundProcessor extends \App\StudioHandlers\InboundProcessor
{
    protected $site;

    protected $theatreMap;
    protected $contentMap;
    protected $messages;

    function __construct($bookingId = null, $transactionId = null, $rerun = false)
    {
        parent::__construct($bookingId, $transactionId, $rerun);
    }

    function handleInbound()
    {
        $start = now()->timestamp;

        // ideally transactionId has the parsed xml saved as transaction_data
        $data = $this->transaction->transaction_data;

        $bookings = [];
        $this->messages = [];
        $this->theatreMap = [];
        $this->contentMap = [];

        // iterate through content_xref and verify all packages are found (paramount_content_id -> packages)
        $content = $data['xrefs']['content_xref']['digital_package'] ?? $data['xrefs']['content_xref'];
        if (count($content) > 0) {
            foreach ($content as $c) {
                if (isset($c['@attributes'])) {
                    $c = $c['@attributes'];
                }
                $contentId = $c['content_id'];
                $package = Release::select(['id', 'title_id'])->where('paramount_content_id', $contentId)->first();

                if ($package) {
                    $this->contentMap[$contentId] = $package;
                }
                else {
                    $packageName = $c['title'];
                    $this->messages[] = "Release not found with paramount_content_id = $contentId ($packageName)";
                }
            }
        }
        else {
            $this->messages[] = "No content found in content_xref.";
        }

        // iterate through theatre_xref and verify all theatres are found (paramount_theatre_id -> cinema_sites)
        $theatres = $data['xrefs']['theatre_xref']['theatre'] ?? $data['xrefs']['theatre_xref'];
        if (count($theatres) > 0) {
            foreach ($theatres as $theatre) {
                // make sure theatre_id is found in cinema_sites->paramount_theatre_id
                if (isset($theatre['@attributes'])) {
                    $theatre = $theatre['@attributes'];
                }
                $theatreId = $theatre['theatre_id'];
                $theatreFound = CinemaSite::where('paramount_theatre_id', $theatreId)->pluck('id')->first();

                if ($theatreFound) {
                    $this->theatreMap[$theatreId] = $theatreFound;
                }
                else {
                    $theatreName = $theatre['name'] . ' - ' . $theatre['city'] . ', ' . $theatre['state'];
                    $this->messages[] = "Site not found for paramount_theatre_id = $theatreId ($theatreName)";
                }
            }
        }
        else {
            $this->messages[] = "No theatres found in theatre_xref.";
        }

        // iterate through theatre_orders build a list of orders for theatres and pacakges
        $theatreOrders = $data['theatre_orders']['theatre_order'] ?? $data['theatre_orders'];

        $bookings = [];

        // single theatre
        if (isset($theatreOrders['@attributes'])) {
            $theatreId = $theatreOrders['@attributes']['theatre_id'];
            $mediaOrders = $theatreOrders['media_orders'];
            $bookings = array_merge($bookings, $this->processMediaOrders($mediaOrders, $theatreId));

        }
        // multi theatre
        else {
            foreach ($theatreOrders as $theatreOrder) {
                $theatreId = $theatreOrder['@attributes']['theatre_id'];
                $mediaOrders = $theatreOrder['media_orders'];
                $bookings = array_merge($bookings, $this->processMediaOrders($mediaOrders, $theatreId));
            }
        }


        $created = [];
        $updated = [];

        if (count($this->messages) === 0) {
            // only create bookings when everything is good. (allow for someone to manually put in IDs to associate the paramount assets to DCDC assets.)
            foreach ($bookings as $booking) {
                try {
                    // find an existing one with the external_order_id and update it
                    $findFields = ['external_order_id' => $booking['external_order_id']];
                    // otherwise create a new one.
                    $newBooking = Booking::updateOrCreate($findFields, $booking);
                    if ($newBooking->wasRecentlyCreated) {
                        $newBooking->setOverallStatus(BookingStatus::Pending, $this->transaction->user_id);
                        // track creates
                        $created[] = $newBooking;
                    }
                    else {
                        // track updates
                        $updated[] = $newBooking;
                    }
                }
                catch (\Exception $e) {
                    $this->messages[] = "Error creating Booking: " . $e->getMessage() . ":" . $e->getLine();
                }
            }
        }

        // trigger package syncs to bookings for all packages referenced.
        foreach ($this->contentMap as $cm) {
            SyncPackageToDeliveries::dispatch($cm->id);
        }

        // and that should be it?
        $data['messages'] = $this->messages;
        $data['bookings'] = $bookings;

        // when there are unmatched theatres or content, keep it unprocessed.
        $this->transaction->processed = (count($this->messages) === 0);
        $this->transaction->transaction_data = $data;

        $this->transaction->save();

        $end = now()->timestamp;
        $seconds = $end - $start;

        // send this to Slack with a breakdown of messages (if there are any)
        logger("[Paramount] Procsessed transaction {$this->transaction->id} in $seconds seconds with " . count($this->messages) . " issues.");

        $messageBlocks = [];
        foreach ($this->messages as $message) {
            $messageBlocks[] = ['type' => 'mrkdwn', 'text' => $message];
        };

        try {
            $slack = (new Notify("[Paramount] Processed Transaction ID {$this->transaction->id} in $seconds seconds with " . count($this->messages) . " issues."));
            foreach ($messageBlocks as $message) {
                $slack->addTextSection($message);
            }

            /** @var Booking $updatedBooking */
            foreach ($updated as $updatedBooking) {

                $sUrl = config('app.frontend_url') . '/sites/' . $updatedBooking->cinema_site_id . '/general';
                $bUrl = config('app.frontend_url') . '/bookings/' . $updatedBooking->id . '/details';
                $tUrl = config('app.frontend_url') . '/titles/' . $updatedBooking->title_id . '/summary';
                $pUrl = config('app.frontend_url') . '/titles/' . $updatedBooking->title_id . '/packages/' . $updatedBooking->package_id;

                $slack->addTextSection(['type' => 'mrkdwn', 'text' => '*Updated Booking*']);
                $slack->addFieldsSection([
                    ['type' => 'mrkdwn', 'text' => "*Site:* <{$sUrl}|{$updatedBooking->cinema->name}>"],
                    ['type' => 'mrkdwn', 'text' => "*Booking:* <{$bUrl}|#{$updatedBooking->id}>"],
                    ['type' => 'mrkdwn', 'text' => "*Title:* <{$tUrl}|{$updatedBooking->title->friendly_title}>"],
                    ['type' => 'mrkdwn', 'text' => "*Release:* <{$pUrl}|{$updatedBooking->package->package_name}>"],
                ]);
                $slack->addDivider();
            }

            /** @var Booking $createdBooking */
            foreach ($created as $createdBooking) {

                $sUrl = config('app.frontend_url') . '/sites/' . $createdBooking->cinema_site_id . '/general';
                $bUrl = config('app.frontend_url') . '/bookings/' . $createdBooking->id . '/details';
                $tUrl = config('app.frontend_url') . '/titles/' . $createdBooking->title_id . '/summary';
                $pUrl = config('app.frontend_url') . '/titles/' . $createdBooking->title_id . '/packages/' . $createdBooking->package_id;


                $slack->addTextSection(['type' => 'mrkdwn', 'text' => '*Created Booking*']);

                $slack->addFieldsSection([
                    ['type' => 'mrkdwn', 'text' => "*Site:* <{$sUrl}|{$createdBooking->cinema->name}>"],
                    ['type' => 'mrkdwn', 'text' => "*Booking:* <{$bUrl}|#{$createdBooking->id}>"],
                    ['type' => 'mrkdwn', 'text' => "*Title:* <{$tUrl}|{$createdBooking->title->friendly_title}>"],
                    ['type' => 'mrkdwn', 'text' => "*Release:* <{$pUrl}|{$createdBooking->package->package_name}>"],
                ]);
                $slack->addDivider();

            }
            $slack->addDivider()->send();

        }
        catch (\Exception $e) {
            // slack failed, usually rate limited.
            logger("Slack Error: {$e->getMessage()}");
        }

    }

    private function processMediaOrders($mediaOrders, $theatreId)
    {
        $bookings = [];

        if (count($mediaOrders) > 0) {

            if (!isset($this->theatreMap[$theatreId])) {
                return [];
            }

            $cinemaId = $this->theatreMap[$theatreId];
            if (!isset($mediaOrders['@attributes'])) {
                // shift it on multi orders.
                $mediaOrders = $mediaOrders['media_order'];
            }

            foreach ($mediaOrders as $mediaOrder) {
                if (isset($mediaOrder['@attributes'])) {
                    $mediaOrder = $mediaOrder['@attributes'];
                }

                $mediaOrderId = $mediaOrder['media_order_id'];

                if (!isset($this->contentMap[$mediaOrder['content_id']])) {
                    $this->messages[] = "Content ID {$mediaOrder['content_id']} not mapped. Cannot create booking for media Order ID $mediaOrderId.";
                    continue;
                }
                $packageId = $this->contentMap[$mediaOrder['content_id']];

                $bookingData = [
                    'cinema_site_id' => $cinemaId,
                    'title_id' => $this->contentMap[$mediaOrder['content_id']]->title_id,
                    'package_id' => $this->contentMap[$mediaOrder['content_id']]->id,
                    'creator_id' => $this->transaction->user_id,
                    'organization_id' => $this->transaction->organization_id,
                    'is_electronic' => true,
                    'deliver_at' => (new Carbon($mediaOrder['playdate_begin']))->subDay(2),
                    'release_date' => new Carbon($mediaOrder['playdate_begin']),
                    'overall_status' => BookingStatus::Pending,
                    'external_order_id' => $mediaOrderId,
                ];

                $bookings[] = $bookingData;
            }
        }
        else {
            $this->messages[] = "There are no orders in this transaction.";
        }
        return $bookings;
    }
}
