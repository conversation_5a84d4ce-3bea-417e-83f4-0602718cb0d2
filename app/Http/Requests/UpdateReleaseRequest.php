<?php

namespace App\Http\Requests;

use App\Models\Enum\PackageTypes;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class UpdateReleaseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'package_name' => ['sometimes', 'string'],
            'type' => ['sometimes', new Enum(PackageTypes::class)],
            'organization_id' => ['sometimes', 'integer'],
            'paramount_content_id' => ['sometimes', 'integer', 'nullable'],
            'lionsgate_content_id' => ['sometimes', 'integer', 'nullable'],
            'dchub_version_id' => ['sometimes', 'string', 'nullable'],
            'add_content_id' => ['sometimes', 'integer'],
            'remove_content_id' => ['sometimes', 'integer'],
            'attached_content' => ['sometimes', 'array'],
        ];
    }
}
