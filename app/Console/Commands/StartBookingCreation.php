<?php

namespace App\Console\Commands;

use App\Events\CreateImportBookings;
use App\Events\ImportBookingsUpload;
use Illuminate\Console\Command;

class StartBookingCreation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bookings:create {import_booking_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Trigger the event/job that processes the actual creation of bookings';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // verify the ID exists
        $id = $this->argument('import_booking_id');

        // fire the event
        event(new CreateImportBookings($id));

    }
}
