<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Version;
use App\Models\Versions;
use Illuminate\Auth\Access\HandlesAuthorization;

class VersionPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param \App\Models\User $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user)
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param \App\Models\User $user
     * @param \App\Models\Version $version
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, Version $version)
    {
        return $user->isAdmin()
            || $version->creator_id === $user->id
            || $user->organization->users->contains($version->creator_id);
    }

    /**
     * Determine whether the user can create models.
     *
     * @param \App\Models\User $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user)
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param \App\Models\User $user
     * @param \App\Models\Versions $versions
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, Version $version)
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param \App\Models\User $user
     * @param \App\Models\Version $version
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, Version $version)
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @param \App\Models\User $user
     * @param \App\Models\Version $version
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function restore(User $user, Version $version)
    {
        return $user->isAdmin();
    }

}
