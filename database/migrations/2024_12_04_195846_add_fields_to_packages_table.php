<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('packages', function (Blueprint $table) {
            //
            $table->boolean('is_pinned')->after('type')->default(false);
            $table->json('distribution_summary')->after('booking_counts')->nullable();
            $table->dateTime('first_published_at')->after('updated_at')->nullable();
            $table->dateTime('last_published_at')->after('first_published_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('packages', function (Blueprint $table) {
            //
            $table->dropColumn('is_pinned');
            $table->dropColumn('distribution_summary');
            $table->dropColumn('first_published_at');
            $table->dropColumn('last_published_at');
        });
    }
};
