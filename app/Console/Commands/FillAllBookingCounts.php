<?php

namespace App\Console\Commands;

use App\Jobs\UpdateBookingCounts;
use Illuminate\Console\Command;
use App\Models\Booking;

class FillAllBookingCounts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fill-all-booking-counts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Queue up a fill-booking-counts for everything!';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        foreach (Booking::get() as $booking) {
            UpdateBookingCounts::dispatch($booking);
        }

        return Command::SUCCESS;
    }
}
