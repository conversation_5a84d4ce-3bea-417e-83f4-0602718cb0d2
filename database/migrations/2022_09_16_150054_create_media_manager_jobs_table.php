<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('media_manager_jobs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable();
            $table->foreignId('cinema_site_id')->nullable();

            $table->foreignId('booking_id')->nullable();
            $table->string('status')->default('pending');
            $table->string('name');
            $table->string('type');
            $table->string('code');
            $table->string('update_url')->nullable();
            $table->string('source_cpl_uuid')->nullable();
            $table->string('file_name')->nullable();
            $table->string('key')->nullable();
            $table->string('download_key')->nullable();
            $table->string('download_secret')->nullable();
            $table->string('cpl')->nullable();
            $table->string('drive')->nullable();
            $table->string('source_drive')->nullable();
            $table->string('destination_drive')->nullable();
            $table->string('partition')->nullable();
            $table->string('source_partition')->nullable();
            $table->string('destination_partition')->nullable();
            $table->string('directory')->nullable();
            $table->string('source_directory')->nullable();
            $table->string('destination_directory')->nullable();
            $table->string('path')->nullable();
            $table->string('media_manager_name')->nullable();
            $table->string('media_manager_job_id')->nullable();

            $table->unsignedBigInteger('size')->nullable(); // somewhere around 18 exabytes maximum
            $table->unsignedTinyInteger('progress')->default(0);
            $table->unsignedInteger('target_rate_in_kbps')->nullable();

            $table->json('errors')->nullable();
            $table->string('latest_message')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('media_manager_jobs');
    }
};
