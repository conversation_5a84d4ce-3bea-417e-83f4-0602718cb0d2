<?php

namespace App\Observers;

use App\Events\DispatchWebhook;
use App\Jobs\UpdateBookingCounts;
use App\Jobs\UpdateTransferCounts;
use App\Jobs\UpdateDistributionSummary;
use App\Jobs\VerifyBooking;
use App\Models\Enum\BookingStatus;

class Booking
{

    public $afterCommit = true;

    public function updated(\App\Models\Booking $booking)
    {
        // If the is_electronic field has changed, we swap all deliveries as well.
        if ($booking->isDirty('is_electronic')) {
            $booking->deliveries()->update(['is_electronic' => $booking->is_electronic]);
            if ($booking->release) {
                UpdateDistributionSummary::dispatch($booking->release);
            }
        }

        if ($booking->isDirty('overall_status')) {
            // mimic the status on deliveries when rejected or cancelled or completed
            switch ($booking->overall_status) {
                case \App\Models\Enum\BookingStatus::Rejected:
                case \App\Models\Enum\BookingStatus::Cancelled:
                case \App\Models\Enum\BookingStatus::Completed:
                    $booking->deliveries()->update(['status' => $booking->overall_status]);
                    break;
                case \App\Models\Enum\BookingStatus::Verify:
                    VerifyBooking::dispatch($booking->id)->delay(30);
                    break;
            }

            switch ($booking->overall_status) {
                case \App\Models\Enum\BookingStatus::Completed:
                    if ($booking->release) {
                        $booking->release->setFirstOrLastPublished();
                    }
                    break;
            }

            UpdateTransferCounts::dispatch($booking);
            UpdateBookingCounts::dispatch($booking);
        }
        event(new DispatchWebhook('booking', 'update', $booking));

    }

    public function created(\App\Models\Booking $booking)
    {
        UpdateBookingCounts::dispatch($booking);
        UpdateTransferCounts::dispatch($booking);
        if ($booking->release) {
            UpdateDistributionSummary::dispatch($booking->release);
        }
    }

    public function deleted(\App\Models\Booking $booking)
    {
        // delete this booking's deliveries long-form to trigger observers.
        $booking->deliveries->each(fn($delivery) => $delivery->delete());
    }
}
