<?php

namespace App\Models;

use App\Models\Enum\PackageTypes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Storage;

class Release extends Model
{
    protected $table = 'packages';

    use SoftDeletes, HasFactory;

    protected $fillable = [
        'package_name',
        'title_id',
        'organization_id',
        'studio_data',
        'type',
        'paramount_content_id',
        'lionsgate_content_id',
        'dchub_version_id',
        'booking_counts',
    ];
    protected $casts = [
        'booking_counts' => 'json',
        'distribution_summary' => 'json',
        'studio_data' => 'json',
        'is_pinned' => 'boolean',
        'type' => PackageTypes::class,
    ];

    protected $appends = ['ingest_letter_url', 'types_list', 'size', 'fazzt_info'];

    public function content()
    {
        return $this->belongsToMany(Version::class, 'package_version', 'package_id', 'version_id');
    }

    public function bookings()
    {
        return $this->hasMany(Booking::class, 'package_id');
    }

    public function title()
    {
        return $this->belongsTo(Title::class);
    }

    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    public function fillDistributionSummary()
    {
        $this->distribution_summary = [
            'physical' => $this->bookings()->where('is_electronic', 0)->count(),
            'electronic' => $this->bookings()->where('is_electronic', 1)->count(),
            'satellite' => $this->bookings()->where('is_electronic', -1)->count(),
        ];
        logger($this->distribution_summary);
        $this->saveQuietly();
    }

    public function fillBookingCounts()
    {

        $this->booking_counts = [
            'pending' => $this->bookings()->where('is_duplicate', 0)->whereIn('overall_status',
                \App\Models\Enum\BookingStatus::getStatusesByGroup('booked'))->count(),
            'in_progress' => $this->bookings()->where('is_duplicate', 0)->whereIn('overall_status',
                \App\Models\Enum\BookingStatus::getStatusesByGroup('in_progress'))->count(),
            'completed' => $this->bookings()->where('is_duplicate', 0)->whereIn('overall_status',
                \App\Models\Enum\BookingStatus::getStatusesByGroup('completed'))->count(),
            'errors' => $this->bookings()->where('is_duplicate', 0)->whereIn('overall_status',
                \App\Models\Enum\BookingStatus::getStatusesByGroup('issues'))->count(),
        ];

        $this->saveQuietly();
    }

    public function getContentAvailableAttribute()
    {
        return $this->title->versions()
            ->select(['id', 'version_name', 'cpl_uuid', 'size'])
            ->where('cpl_uuid', '<>', '')
            ->whereNotIn('id', $this->content->pluck('id'))
            ->get();
    }

    public function getIngestLetterUrlAttribute()
    {
        $path = 'igl/' . md5($this->id) . '.pdf';
        try {
            if (Storage::disk('s3')->exists($path)) {
                return Storage::disk('s3')->temporaryUrl($path, now()->addDays(7));
            }
        } catch (\Exception $e) {
            // nothing exciting here, move along.
        }
        return null;
    }

    public function getTypesListAttribute()
    {
        $types = [];
        foreach (PackageTypes::cases() as $case) {
            $types[] = ["value" => $case->value, "label" => $case->label()];
        }
        return $types;
    }

    public function getSizeAttribute()
    {
        $size = 0;

        if ($this->content) {
            foreach ($this->content as $content) {
                $size += $content->size;
            }
        }

        return $size;
    }

    public function setFirstOrLastPublished()
    {
        // Whenever a publish command is sent, hit this.
        // When first_published_at is null, save it there.
        // in all cases, save last_published_at too.
        if ($this->first_published_at === null) {
            $this->first_published_at = now();
        }

        $this->last_published_at = now();
        $this->saveQuietly();

    }

    public function getFazztInfoAttribute()
    {
        $return = [
            'ready_to_transmit' => false,
            'transmitting' => false,
            'percent' => 0,
            'delivered' => false,
            'first_published' => false,
            'last_published' => false,
        ];

        foreach ($this->content as $content) {
            if ($c = $content->fazztContent()->first()) {
                if ($c->status === 'SUCCESS') {
                    $return['ready_to_transmit'] = $c->updated_at;
                } else {
                    // nothing else matters if we can't transmit it.
                    return $return;
                }
            }
            if ($c = $content->fazztTransfer()->first()) {
                $return['transfer_date'] = $c->actual_start_at; // this will change when in the queue
                $return['percent'] = $c->percent;
                if ($c->percent === 100) {
                    $return['delivered'] = $c->updated_at;
                }
            }
            $return['first_published'] = $this->first_published_at;
            $return['last_published'] = $this->last_published_at;
        }

        return $return;
    }
}
