<?php

namespace App\Console\Commands;

use App\Actions\Dcp\SplitDcp;
use App\Actions\Dcp\VerifyDcp;
use App\Actions\Slack\Notify;
use App\Models\AsperaTransfer;
use App\Models\Booking;
use App\Models\Enum\BookingStatus;
use App\Models\Title;
use App\Models\Version;

use App\StudioHandlers\Disney\InboundProcessor;
use Illuminate\Console\Command;
use Sentry\Tracing\Transaction;

class CheckDCP extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'dcp:check {version_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check the status of a DCP via version id';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $versionId = $this->argument('version_id');

        /** @var Version $version */
        $version = Version::findOrFail($versionId);
        $title = $version->title;

        $this->info("Checking VersionId {$versionId}");

        // pull the root from the original upload and force-fill the s3 details.
        $asperaTransfer = AsperaTransfer::where('version_id', $versionId)->first();

        if (!$asperaTransfer) {
            $this->error("No transfer found, has this version been assigned to an aspera transfer record?");
            return Command::INVALID;
        }

        $this->info("{$title->friendly_title}: Version ID: {$version->id} On bucket: {$asperaTransfer->bucket} ");

        // there are two forms of uploads
        // 1. regular, the root and path can be pulled from aspera data, the root and paths are in there.
        // 2. deluxe directly to S3, the transfer_spec[] will contain the root, bucket.
        $version->fillS3DetailsFromTransfer($asperaTransfer);

        $results = VerifyDcp::execute($asperaTransfer->origin_root, $asperaTransfer->bucket);

        $url = config('app.frontend_url') . '/titles/' . $version->title_id . '/summary';
        $vUrl = config('app.frontend_url') . '/titles/' . $version->title_id . '/content/' . $version->id;

        if ($results['error'] === true) {
            $slack = (new Notify("A Failed DCP Validation for \"{$version->title->friendly_title}.\""))
                ->addFieldsSection([
                    ['type' => 'mrkdwn', 'text' => "*Title:* <{$url}|{$version->title->friendly_title}>"],
                    ['type' => 'mrkdwn', 'text' => "*Version:* <{$vUrl}|{$version->version_name}>"],
                ])
                ->addDivider()->send();
            $this->error("DCP Failure");
            $this->error(json_encode($results));
            return Command::FAILURE;
        }

        $this->info("Validated DCP.");
        $this->info(json_encode($results));

        if (isset($results['multi_directory']) && $results['multi_directory']) {
            $name = basename($results['base_directory']);
            $uuid = '';
            $assetUuid = '';
        }
        else {
            $uuid = str_replace('urn:uuid:', '', $results['cpl_uuid']);
            $assetUuid = str_replace('urn:uuid:', '', $results['dcp_uuid']);
            $name = $results['dcp_title'];
        }

        // we have a version, update it.
        $version->update([
            'version_name' => $name,
            'cpl_uuid' => $uuid,
            'asset_uuid' => $assetUuid,
            'multi_cpl_uuids' => $results['cpl_multi_uuids'] ?? [],
            'is_ready' => true,
        ]);

        $url = config('app.frontend_url') . '/titles/' . $version->title_id . '/summary';
        $vUrl = config('app.frontend_url') . '/titles/' . $version->title_id . '/content/' . $version->id;

        $slack = (new Notify("Successful DCP Validation for \"{$version->title->friendly_title}.\""))
            ->addFieldsSection([
                ['type' => 'mrkdwn', 'text' => "*Title:* <{$url}|{$version->title->friendly_title}>"],
                ['type' => 'mrkdwn', 'text' => "*Version:* <{$vUrl}|{$version->version_name}>"],
            ])
            ->addDivider()->send();

        // if it's a single root (one dcp in a sub folder), re-align the paths
        if (isset($results['single_root']) && $results['single_root']) {
            $version->shiftS3Paths();
        }

        // and split x
        if (isset($results['multi_directory']) && $results['multi_directory']) {
            $this->info("Splitting.");
            try {
                $splitResults = SplitDcp::execute($version); // this fails on frozen assets.
            }
            catch (Exception $e) {
                $splitResults['error'] = true;
                $splitResults['message'] = $e->getMessage();
            }

            if ($splitResults['error'] === true) {
                Notify::quickMessage(":fire: There was an *error* splitting the <{$vUrl}|{$version->version_name}> --
                 {$splitResults['message']}");
            }
            else {
                $count = count($splitResults['versions']);
                Notify::quickMessage("Happily split <{$vUrl}|{$version->version_name}> into {$count} CPLs");
            }
        }

        // if disney owns the title, trigger the re-check of bookings that may be orphaned.
        $bookings = Booking::whereNull('title_id')->whereIn('overall_status', BookingStatus::getStatusesByGroup('booked'))->get();
        /** @var Booking $booking */
        foreach ($bookings as $booking) {
            /** @var Transaction $transaction */
            foreach ($booking->transactions as $transaction) {
                $handler = new InboundProcessor($booking->id, $transaction->id, true);
                $handler->processBooking();
            }
        }

        $this->info("Completed.");
        return Command::SUCCESS;

    }
}
