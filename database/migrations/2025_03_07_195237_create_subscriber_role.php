<?php

use Illuminate\Database\Migrations\Migration;
use App\Models\{Role, Permission};

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $role = Role::findOrCreate('subscriber');
        $validPermissions = ['view-bookings', 'view-titles', 'view-reports', 'view-users'];
        $permissions = Permission::whereIn('name', $validPermissions)->get();
        $role->givePermissionTo($permissions);
    }   

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        $role = Role::where('name', 'subscriber')->first();
        if ($role) {
            $role->delete();
        }
    }
};
