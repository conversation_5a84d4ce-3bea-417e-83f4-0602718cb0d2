APP_NAME=DCDC
APP_ENV=local
APP_KEY=base64:37XZQqZcnxJJv9I69tX/35z/wAQxsOs3mWFzQ0U5ZwU=
APP_DEBUG=true
APP_URL=https://dcdc-api.test
FRONTEND_URL=https://frontend.dcdc-api.test

LOG_CHANNEL=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=sqlite
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=database/database.sqlite
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=144000
SESSION_DOMAIN=.dcdc-api.test

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# brew install mailhog && mailhog
# if you want to trap locally sent mail:
MAIL_MAILER=smtp
MAIL_HOST=localhost
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# should be injected by github actions
#AWS_ACCESS_KEY_ID=
#AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=cinesend-dev
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

DISNEY_NOTIFY_URL=http://mockbin.org/bin/33c5a3ea-b18c-4492-9214-3ffa1104f3bc
