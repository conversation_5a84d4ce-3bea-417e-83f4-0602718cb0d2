<?php

namespace App\Fazzt\Cinema;

use App\Fazzt\Exception;
use App\Fazzt\Json;
use App\Models\CinemaProServer;

class ReceiveSites
{

    /**
     * map p;
     * p["method"] = "CinemaReceiveSiteGroupEnumerate";
     * p["params"][0] = [];
     * p["id"] = id;
     *
     * map post;
     * // We construct map p and format it into JSON string.
     * post["Data"] = ValueToJSONString(p);
     * post["Headers"] = "Content-Type: application/json\r\n\r\n";
     * post["URL"] = "http://127.0.0.1:4039/ws/json-rpc/InvokeFazztFunction.fzt?includes=Cinema/CinemaReceiveGroupLib.fzt";
     */

    static function exec()
    {
        try {
            $response = Json::jsonCall(
                'CinemaReceiveSiteEnumerate',
                'Cinema/CinemaReceiveSiteLib',
                [['limit' => -1]]);

            $result = $response['result'];
            foreach ($result as $site) {
                /** @var CinemaProServer $site */
                $site = CinemaProServer::fillFromFazzt($site);
            }
            return true;
        }
        catch (\Exception $e) {
            logger($e->getMessage());
        }

        return false;

    }
}
