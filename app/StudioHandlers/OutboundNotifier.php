<?php

namespace App\StudioHandlers;

abstract class OutboundNotifier
{
    abstract function sendNotification($status, $bookingId);

    static abstract function pending($parameters);
    static abstract function error($parameters);
    static abstract function accepted($parameters);
    static abstract function transmitting($parameters);
    static abstract function failed_pending_remediation($parameters);
    static abstract function failed_remediation_actioned($parameters);
    static abstract function failed_remediation_in_transit($parameters);
    static abstract function incomplete($parameters);
    static abstract function rejected($parameters);
    static abstract function cancelled($parameters);
    static abstract function cancelled_after_completion($parameters);
    static abstract function delivered($parameters);
    static abstract function completed($parameters);

    public function logToSentry($message) {
        logger()->warning($message);
        \Sentry\captureMessage($message);
    }

}
