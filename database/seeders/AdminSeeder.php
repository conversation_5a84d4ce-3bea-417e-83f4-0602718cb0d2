<?php

namespace Database\Seeders;

use App\Models\Organization;
use Exception;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Psy\Exception\DeprecatedException;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        try {
            $user1 = \App\Models\User::factory()->create([
                'name' => 'Demo Admin',
                'email' => '<EMAIL>',
            ]);
            $user1->assignRole('admin');

            $user2 = \App\Models\User::factory()->create([
                'name' => 'Demo Super Admin',
                'email' => '<EMAIL>',
            ]);
            $user2->assignRole('super-admin');

            $org = Organization::factory()->withOwner($user2)->create(['name' => 'DCDC', 'type' => 'admin']);
            $org->users()->saveMany([$user1, $user2]);

        }
        catch (Exception $e) {
        }
    }
}
