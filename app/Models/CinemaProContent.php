<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CinemaProContent extends Model
{
    protected $table = 'cinema_pro_content';

    use HasFactory, SoftDeletes;

    protected $fillable = [
        'site_id',
        'asset_uuid',
        'file_count',
        'library_status',
        'cru_status',
    ];

    public function version()
    {
        return $this->belongsTo(Version::class, 'asset_uuid', 'asset_uuid');
    }

    public function cinemaPro()
    {
        $this->belongsTo(CinemaProServer::class, 'site_id', 'site_id');
    }
}

