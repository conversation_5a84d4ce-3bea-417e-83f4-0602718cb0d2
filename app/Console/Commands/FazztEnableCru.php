<?php

namespace App\Console\Commands;

use App\Fazzt\Json;
use Illuminate\Console\Command;

class FazztEnableCru extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fazzt:enable-cru';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Enable the CRU drive syncing';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        \App\Fazzt\Cru\SetMode::exec('manual');

        return Command::SUCCESS;
    }
}
