<?php

namespace App\Console\Commands;

use App\Jobs\VerifyBooking;
use App\Models\Booking;
use App\Models\Enum\BookingStatus;
use Illuminate\Console\Command;

class CheckForPendingVerifications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'booking:verify';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Run verify against any bookings that seem stuck.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $bookings = Booking::where('overall_status', BookingStatus::Verify)
            ->where('updated_at', '<', now()->subHour())->get();

        $this->info("Verifying {$bookings->count()} bookings.");

        foreach ($bookings as $booking) {
            VerifyBooking::dispatch($booking->id);
        }

        return Command::SUCCESS;
    }
}
