<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreImportBookingRecordRequest;
use App\Http\Requests\UpdateImportBookingRecordRequest;
use App\Models\ImportBookingRecord;

class ImportBookingRecordController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Http\Requests\StoreImportBookingRecordRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreImportBookingRecordRequest $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\ImportBookingRecord  $importBookingRecord
     * @return \Illuminate\Http\Response
     */
    public function show(ImportBookingRecord $importBookingRecord)
    {
        //
        return ['data' => $importBookingRecord];
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \App\Http\Requests\UpdateImportBookingRecordRequest  $request
     * @param  \App\Models\ImportBookingRecord  $importBookingRecord
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateImportBookingRecordRequest $request, ImportBookingRecord $importBookingRecord)
    {
        //
        $importBookingRecord->cinema_site_id = $request->validated('cinema_site_id');
        $importBookingRecord->matched = 1;
        $importBookingRecord->save();

        return $this->show($importBookingRecord);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\ImportBookingRecord  $importBookingRecord
     * @return \Illuminate\Http\Response
     */
    public function destroy(ImportBookingRecord $importBookingRecord)
    {
        //
    }
}
