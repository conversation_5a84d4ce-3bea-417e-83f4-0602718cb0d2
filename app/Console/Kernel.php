<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     *
     * @param \Illuminate\Console\Scheduling\Schedule $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {

        $schedule->command('aspera:check')->everyMinute();
        $schedule->command('equipment:check')->everyFiveMinutes()->withoutOverlapping();
        $schedule->command('sites:update-speeds')->daily();

        $schedule->command('check:transfers')->everyFiveMinutes();
        $schedule->command('check:pending-jobs')->hourly();

        // XML processors:
        $schedule->command('paramount:inbound')->hourly();
        $schedule->command('lionsgate:inbound')->everyFifteenMinutes();

        // Check for Deluxe direct uploads
        $schedule->command('deluxe:check')->everyFifteenMinutes();

        $schedule->command('bookings:re-verify')->everyFifteenMinutes();

        // $schedule->command('dchub:poll-requests')->hourly();

        // Fazzt Things
        // $schedule->command('fazzt:fetch-cinema-pros')->hourly();
        $schedule->command('fazzt:check-dcp-ingest')->everyFiveMinutes(); // this takes 15-20 minutes to complete on proper size DCPs.
        $schedule->command('fazzt:dcp-transmit')->everyMinute();
        $schedule->command('fazzt:dcp-transmit-status')->everyMinute();

    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
