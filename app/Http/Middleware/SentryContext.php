<?php

namespace App\Http\Middleware;

use Closure;
use Sentry;
use Sentry\State\Scope;

class SentryContext
{
    /**
    * Handle an incoming request.
    *
    * @param  \Illuminate\Http\Request $request
    * @param  \Closure                 $next
    *
    * @return mixed
    */
    public function handle($request, Closure $next)
    {
        $data = [];
        if ($user = auth()->user()) {
            $data = [
                'id' => $user->id,
                'email' => $user->email,
                'username' => $user->full_name
            ];
        }
        else if ($user = auth()->guard('sanctum')->user()) {
            $data = [
                'id' => $user->id,
                'email' => $user->email,
                'username' => $user->name
            ];
        }

        Sentry\configureScope(function (Scope $scope) use ($data): void {
            $scope->setUser($data);
        });

        return $next($request);
    }
}
