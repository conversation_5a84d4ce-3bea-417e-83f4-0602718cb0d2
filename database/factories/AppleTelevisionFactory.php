<?php

namespace Database\Factories;

use App\Models\CinemaSite;
use App\Models\Organization;
use App\Models\Title;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Booking>
 */
class AppleTelevisionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'serial_number' => uniqid('id'),
        ];
    }

    public function withCinema(CinemaSite $cinema)
    {
        return $this->state(function (array $attributes) use ($cinema) {
            return [
                'cinema_site_id' => $cinema->id,
            ];
        });
    }

    public function withOrganization(Organization $org)
    {
        return $this->state(function (array $attributes) use ($org) {
            return [
                'organization_id' => $org->id,
            ];
        });
    }


}
