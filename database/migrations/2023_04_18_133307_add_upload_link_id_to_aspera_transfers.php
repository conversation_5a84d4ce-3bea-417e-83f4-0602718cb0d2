<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('aspera_transfers', function (Blueprint $table) {
            //
            $table->foreignId('title_upload_link_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('aspera_transfers', function (Blueprint $table) {
            //
            $table->dropColumn('title_upload_link_id');
        });
    }
};
