<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\URL;

class TitleUploadLink extends Model
{
    use SoftDeletes, HasFactory;

    protected $fillable = [
        'title_id',
        'user_id',
        'version_id',
        'aspera_transfer_id',
        'email',
        'reference',
        'is_expired',
        'expires_at',
    ];
    protected $casts = ['is_expired' => 'boolean', 'expires_at' => 'datetime'];

    protected $appends = ['url', 'upload_count', 'origin_name'];

    public function title()
    {
        return $this->belongsTo(Title::class);
    }

    public function version()
    {
        return $this->hasOne(Version::class);
    }

    public function transfers()
    {
        return $this->hasMany(AsperaTransfer::class, 'title_upload_link_id', 'id')->where('direction', 'upload');
    }

    public function owner()
    {
        return $this->hasOne(User::class);
    }

    public function getUploadCountAttribute()
    {
        return $this->transfers()->where('direction', 'upload')->count();
    }

    public function getoriginNameAttribute(){
        return "pants";
    }

    public function getUrlAttribute()
    {
        $url = URL::temporarySignedRoute(
            'verify-upload-link',
            $this->expires_at,
            ['link' => $this]
        );
        return config('app.frontend_url') . '/upload?signedUrl=' . base64_encode($url);
    }
}
