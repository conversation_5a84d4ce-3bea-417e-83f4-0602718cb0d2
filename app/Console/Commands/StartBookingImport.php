<?php

namespace App\Console\Commands;

use App\Events\ImportBookingsUpload;
use Illuminate\Console\Command;

class StartBookingImport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bookings:import {import_booking_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Trigger the booking import worker (file to db records)';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        // verify the ID exists
        $id = $this->argument('import_booking_id');

        // fire the event
        event(new ImportBookingsUpload($id));

    }
}
