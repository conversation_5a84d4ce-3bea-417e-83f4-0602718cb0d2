<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('versions', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('title_id');
            $table->unsignedBigInteger('creator_id'); // ownership

            $table->string('nickname'); // short DCP name convention, may be an enum later
            $table->string('version_name'); // the long DCP name convention
            $table->uuid('cpl_uuid')->nullable(); // hopefully just uuid from the dcp?
            $table->softDeletes();
            $table->timestamps();

            $table->unsignedBigInteger('size')->nullable(); // size in bytes
            $table->string('path')->nullable(); // path in S3

            $table->unique('cpl_uuid');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('versions');
    }
};
