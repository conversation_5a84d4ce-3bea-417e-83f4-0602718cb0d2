<?php

namespace App\Listeners;

use App\Events\ImportBookingsUpload;
use App\Imports\BookingImport;
use App\Models\ImportBooking;
use Illuminate\Contracts\Queue\ShouldQueue;
use Maatwebsite\Excel\Facades\Excel;

class ImportBookingUpload implements ShouldQueue
{
    public function handle(ImportBookingsUpload $event)
    {
        logger("Event Processor for ID: " . $event->importBookingId);
        $importRecord = ImportBooking::where('id', $event->importBookingId)->first();

        Excel::import(new BookingImport($importRecord), $importRecord->s3_file, 's3', $importRecord->detected_type);

    }
}
