<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use App\Models\{Delivery, Enum\BookingStatus, Release, Booking, Version};
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\Middleware\WithoutOverlapping;

class SyncPackageToDeliveries implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $packageId;
    public $lastBookingId;
    public $uniqueFor = 3600; // 5 minutes and then let it re-try with the same ID.

    public function middleware(): array
    {
        return [new WithoutOverlapping($this->packageId)];
    }

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($packageId, $lastBookingId = null)
    {
        //
        $this->packageId = $packageId;
        $this->lastBookingId = $lastBookingId;
    }

    public function uniqueId(): string
    {
        return sprintf("%d.%d", $this->packageId, $this->lastBookingId);
    }
    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $package = Release::findOrFail($this->packageId);
        $limit = 50;

        $bookings = $package->bookings()->oldest()->when($this->lastBookingId, function ($q) {
            $q->where('id', '>', $this->lastBookingId);
        })->limit($limit)->get();

        /** @var Booking $booking */
        foreach ($bookings as $booking) {
            $versionIds = [];
            /** @var Version $version */
            foreach ($package->content as $version) {
                $versionIds[] = $version->id;

                // create one if it doesn't exist.
                /** @var Delivery $delivery */
                $delivery = $booking->deliveries()->withTrashed()->firstOrCreate([
                    'version_id' => $version->id,
                    'package_id' => $package->id,
                    'title_id' => $package->title_id,
                    'organization_id' => $booking->organization_id,
                    'cinema_site_id' => $booking->cinema_site_id,
                ], ['cpl_uuid' => $version->cpl_uuid, 'is_electronic' => true]);

                // restore it if it was deleted and pending
                if ($delivery->trashed()) {
                    $delivery->restore();
                }
            }

            // delete any that weren't matched.
            $booking->deliveries()->whereNotIn('version_id', $versionIds)->delete();

            // adjust the booking's overall status
            $completeCount = 0;
            foreach ($booking->deliveries()->get() as $delivery) {
                if ($delivery->status === BookingStatus::Completed) {
                    $completeCount++;
                }
            }

            if ($completeCount === count($booking->deliveries)) {
                if (in_array($booking->overall_status, BookingStatus::getStatusesByGroup('in_progress'))) {
                    $booking->setOverallStatus(BookingStatus::Verify);
                }
            }
            else {
                // flip back to pending if it was previously completed, but there are new transfers.
                if ($booking->overall_status === BookingStatus::Completed) {
                    $booking->setOverallStatus(BookingStatus::Pending);
                }
            }
        }

        // dispatch this job again with current booking ID pointer
        if ($bookings->count() === $limit) {
            logger("dispatching next batch from {$booking->id}");
            SyncPackageToDeliveries::dispatch($package->id, $booking->id);
        }
        else {
            logger("no more to process");
        }
    }
}
