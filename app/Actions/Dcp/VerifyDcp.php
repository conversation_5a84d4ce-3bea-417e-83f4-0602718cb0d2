<?php

namespace App\Actions\Dcp;

use Aws\S3\Exception\S3Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Storage;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Diff\Chunk;

class VerifyDcp
{
    static Collection $files;
    static array $directories = [];
    static array $results = [];
    static string $disk = 's3';

    static function execute($s3root, $disk = 's3')
    {
        self::$disk = $disk;
        $s3 = Storage::disk(self::$disk);
        self::reset();

        self::$results['info'] = sprintf("Checking %s on disk %s", $s3root, $disk);

        // should have one directory (the name of the dcp)
        // grab a list of all the files
        try {
            self::$directories = $s3->directories($s3root);
        } catch (S3Exception $e) {
            self::$results['error'] = true;
            self::$results['s3_access_denied'] = $e->getMessage();
            return self::$results;
        }

        $files = $s3->files($s3root);

        if (count($files) == 0) {
            switch (count(self::$directories)) {
                case 0:
                    self::$results['error'] = true;
                    self::$results['no_directories_found'] = true;
                    return self::$results;
                case 1:
                    self::$results['single_root'] = true;
                    $files = $s3->allFiles(self::$directories[0]);
                    $subdirectories = $s3->directories(self::$directories[0]);
                    if (count($subdirectories) > 0) {
                        // it's possible that a dcp has content in it's own folders, so this may not be accurate.
                        // we can still run validator, but this will allow it to be
                        // split via the other tool.
                        self::$results['base_directory'] = self::$directories[0];
                        foreach ($subdirectories as $subdir) {
                            // how about if an asset map exists in any subdirectory, then we'll try and split it.
                            if ($s3->exists($subdir . '/ASSETMAP.xml') || $s3->exists($subdir . '/ASSETMAP')) {
                                self::$results['multi_directory'] = true;
                                return self::$results;
                            }
                        }
                    }
                    break;
                default:
                    // this case may will be after a split where the DCP is this s3root.
                    self::$results['single_root'] = false;
                    self::$results['base_directory'] = $s3root;
                    self::$results['multi_directory'] = true;
                    return self::$results;
            }
        } else {
            self::$results['root_is_dcp'] = true;
            $files = $s3->allFiles($s3root);
        }

        foreach ($files as $file) {
            self::$files->add(new FileDefinition('', $file, $s3->size($file)));
        }

        $assetMapFileCount = 2; // start with VOLINDEX and ASSETMAP

        // should have an volindex and asset map inside that directory
        self::$results['has_volindex'] = self::hasVolIndex();

        if (self::$results['has_asset_map'] = self::hasAssetMap()) {
            // keep on processing.
            $assetMap = self::getAssetMapAsObject(); // this returns (bool)true on a frozen/glacier file. The next few lines will error, which is caught later.
            self::$results['dcp_uuid'] = (string)$assetMap->Id; // UUID guaranteed unique
            self::$results['dcp_title'] = (string)$assetMap->AnnotationText; // not reliable, but we'll keep it.

            // each chunklist/chunk is an individual file inside the folder
            foreach ($assetMap->AssetList->Asset as $asset) {
                foreach ($asset->ChunkList->Chunk as $chunk) {
                    // find each chunk file in the s3 list and mark it as verified
                    if (pathinfo((string)$chunk->Path, PATHINFO_EXTENSION) === 'xml') {
                        if (isset($asset->PackingList) && (bool)$asset->PackingList === true) {
                            self::updateFile($chunk->Path, 'isPkl', true);
                        } else {
                            // cpl detection is a little harder, xml file maybe with CPL_ or cpl- or -cpl, CompositionPlayList
                            if (stristr(strtolower((string)$chunk->Path), 'cpl') || stristr(strtolower((string)$chunk->Path), 'compositionplaylist')) {
                                self::updateFile($chunk->Path, 'isCpl', true);
                            }
                        }
                    }
                    $assetMapFileCount++;

                    if (!self::updateFile($chunk->Path, 'verified', true)) {
                        self::$results['assets_not_found'][] = (string)$chunk->Path;
                        self::$results['error'] = true;
                    }

                    self::updateFile($chunk->Path, 'uuid', (string)$asset->Id);
                }
            }
            if ($assetMapFileCount <> count(self::$files)) { // this is only critical if asset map references a file that doesn't exist.
                self::$results['file_count_asset_map'] = $assetMapFileCount;
                self::$results['file_count_s3'] = count(self::$files);
                self::$results['file_count_mismatched'] = true;
            }
        } else {
            self::$results['error'] = true;
            return self::$results;
        }

        // verify that asset map and all files in it are uploaded

        // verify the Pkls (at least one)
        if (self::$results['has_pkl'] = self::hasPkl()) {
            // process the pkl(s)! - these reference the uuid for the asset
            /** @var FileDefinition $pkl */
            foreach (self::getPkls() as $pkl) {
                // load into xml object
                $pkl = simplexml_load_string($s3->get($pkl->s3key));
                // check assetlist->asset for hash (set to to comp cpl's hash)
                foreach ($pkl->AssetList->Asset as $asset) {
                    $file = self::getFileByUuid((string)$asset->Id);
                    if (!$file) {
                        self::$results['pkl_assets_not_found'][] = (string)$asset->Id;
                        //self::$results['error'] = true;
                    } else {
                        $file->hash = (string)$asset->Hash;

                        if ($file->fileSize !== (int)$asset->Size) {
                            self::$results['pkl_size_mismatches'][] = (string)$asset->Id;
                            // self::$results['error'] = true;
                        }
                    }
                }
            }
        } else {
            self::$results['error'] = true;
        }

        // verify any Cpls (at least one)
        if (self::$results['has_cpl'] = self::hasCpl()) {
            $cplUUIDs = [];
            /** @var FileDefinition $cplDefinition */
            foreach (self::getCpls() as $cplDefinition) {
                // check reel -> [insert item here] for uuid in asset list
                $cpl = simplexml_load_string($s3->get($cplDefinition->s3key));
                // cpl UUID is here
                self::$results['cpl_uuid'] = (string)$cpl->Id;
                $cplUUIDs[] = (string)$cpl->Id; // track all cpl uuids
                foreach ($cpl->ReelList->Reel->AssetList as $asset) {
                    $file = self::getFileByUuid((string)$asset->Id);
                    if (!$file) {
                        self::$results['cpl_assets_not_found'][] = (string)$asset->Id;
                        self::$results['error'] = true;
                    }
                    // check hash equals hash found in pkl, not much else to do here.
                    if ($file->hash !== (string)$asset->Hash) {
                        self::$results['cpl_asset_hash_mismatch'][] = (string)$asset->Id;
                        self::$results['error'] = true;
                    }
                }
            }
            self::$results['cpl_multi_uuids'] = $cplUUIDs;
        } else {
            self::$results['error'] = true;
        }

        return self::$results;
    }

    static function getAssetMapAsObject()
    {
        $map = self::$files->filter(function (FileDefinition $file) {
            // sometimes they're missing dot xml.
            return strtolower($file->fileName) === strtolower('assetmap.xml') || strtolower($file->fileName) === strtolower('assetmap');
        });

        return simplexml_load_string(Storage::disk(self::$disk)->get($map->first()->s3key));

    }

    static function updateFile($fileName, $key, $value)
    {
        $fileName = basename($fileName);
        $foundOne = false;
        self::$files->each(function (FileDefinition $file) use ($fileName, $key, $value, &$foundOne) {
            if ($file->fileName === (string)$fileName) {
                $file->{$key} = $value;
                $foundOne = true;
            } // exact match name.
        });
        return $foundOne;
    }

    static function hasMatchedFile($fileName)
    {
        return self::$files->contains(function (FileDefinition $file) use ($fileName) {
            return $file->fileName === $fileName; // exact match name.
        });
    }

    static function hasVolIndex()
    {
        return self::$files->contains(function (FileDefinition $file) {
            return strtolower($file->fileName) == strtolower('volindex.xml') || strtolower($file->fileName) == strtolower('volindex');
        });
    }

    static function hasAssetMap()
    {
        return self::$files->contains(function (FileDefinition $file) {
            // sometimes they're missing dot xml.
            return strtolower($file->fileName) == strtolower('assetmap.xml') || strtolower($file->fileName) == strtolower('assetmap');
        });
    }

    static function hasPkl()
    {
        return self::$files->contains(function (FileDefinition $file) {
            return $file->isPkl === true;
        });
    }

    static function getPkls()
    {
        return self::$files->filter(function (FileDefinition $file) {
            return $file->isPkl;
        });
    }

    static function hasCpl()
    {
        return self::$files->contains(function (FileDefinition $file) {
            return $file->isCpl === true;
        });
    }

    static function getCpls()
    {
        return self::$files->filter(function (FileDefinition $file) {
            return $file->isCpl;
        });
    }

    static function getFileByUuid($uuid)
    {
        return self::$files->filter(function (FileDefinition $file) use ($uuid) {
            return $file->uuid === $uuid;
        })->first() ?? false;
    }

    static function reset()
    {
        self::$files = collect([]);
        self::$directories = [];
        self::$results = ['error' => false];
    }
}
