<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreOrganizationRequest;
use App\Http\Requests\StoreWebhookRequest;
use App\Http\Requests\UpdateOrganizationRequest;
use App\Http\Requests\UpdateWebhookRequest;
use App\Models\Organization;
use App\Models\OrganizationWebhookUrl;
use Illuminate\Http\Response;

class OrganizationController extends Controller
{
    public function __construct()
    {
        $this->authorizeResource(Organization::class);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
        if (auth()->user()->isAdmin() || auth()->user()->can('view-all-organizations')) {
            return Organization::paginate(request()->take);
        }

        // temporary
        return Organization::whereIn('id', [auth()->user()->organization_id])->paginate(request()->take);
    }

    // public function bookings(Organization $organization)
    // {
    //     if (auth()->user()->cant('view', $organization)) {
    //         abort(Response::HTTP_FORBIDDEN);
    //     }

    //     return $organization->bookings()
    //         ->orderBy(request()->sortBy ?? 'name', request()->sortDirection ?? 'ASC')
    //         ->paginate(request()->take);
    // }

    /**
     * Store a newly created resource in storage.
     *
     * @param \App\Http\Requests\StoreOrganizationRequest $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreOrganizationRequest $request)
    {
        // this will validate and reject failed posts.
        $values = $request->validated();

        // if we're here, we're good...
        $newOrg = Organization::create($values);

        return response(['data' => $newOrg]);
    }

    /**
     * Display the specified resource.
     *
     * @param \App\Models\Organization $organization
     * @return \Illuminate\Http\Response
     */
    public function show(Organization $organization)
    {
        return response(['data' => $organization]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \App\Http\Requests\UpdateOrganizationRequest $request
     * @param \App\Models\Organization $organization
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateOrganizationRequest $request, Organization $organization)
    {
        // this will validate and reject failed posts.
        $values = $request->validated();

        // if we're here, we're good...
        $organization->update($values);

        return response(['data' => $organization]);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\Models\Organization $organization
     * @return \Illuminate\Http\Response
     */
    public function destroy(Organization $organization)
    {
        if (auth()->user()->id === $organization->owner_id) {
            abort(Response::HTTP_UNPROCESSABLE_ENTITY, 'No self deleting.');
        }

        return response($organization->delete());
    }

    public function restore($id)
    {
        // this one needs to be manually checked because the model is unknown until here.
        $organization = Organization::findOrFail($id)->withTrashed();
        if (auth()->user()->cant('restore_organizations', $organization)) {
            abort(Response::HTTP_FORBIDDEN);
        }

        $organization->restore();
        return response(['data' => $organization]);
    }

    public function webhooks(Organization $organization)
    {
        if (auth()->user()->cant('view', $organization)) {
            abort(Response::HTTP_FORBIDDEN);
        }

        return ['data' => $organization->webhooks()->paginate(100)];

    }

    public function updateWebhook(UpdateWebhookRequest $request, Organization $organization, OrganizationWebhookUrl $organizationWebhookUrl)
    {

        if (auth()->user()->cant('update', $organization)) {
            abort(Response::HTTP_FORBIDDEN);
        }

        $data = $request->validated();
        $organizationWebhookUrl->update();

        return ['data' => $organizationWebhookUrl];

    }

    public function storeWebook(StoreWebhookRequest $request, Organization $organization)
    {
        if (auth()->user()->cant('update', $organization)) {
            abort(Response::HTTP_FORBIDDEN);
        }

        $data = $request->validated();
        $data['creator_id'] = auth()->user()->id;

        $organizationWebhookUrl = $organization->webhooks()->create($data);

        return ['data' => $organizationWebhookUrl];
    }

    public function deleteWebhook(Organization $organization, OrganizationWebhookUrl $organizationWebhookUrl)
    {
        if (auth()->user()->cant('update', $organization)) {
            abort(Response::HTTP_FORBIDDEN);
        }

        $organizationWebhookUrl->delete();
        return ['data' => $organizationWebhookUrl];

    }

    public function transactions(Organization $organization) {
        return $organization->transactions()->latest()->with(['user'])->paginate(request()->take);
    }
}
