<?php

namespace Database\Factories;

use App\Models\Version;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\FazztContent>
 */
class FazztContentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            // this can't exist without a package->version asset UUID

            'status' => 'PROCESED', // this is an actual typo that comes from the fazzt server status. :sigh:
            'package_file' => "C:\windows\FakeFile" . uniqid(),
            'message' => 'Successfully package files.',
        ];
    }

    public function onContent(Version $version)
    {
        return $this->state(function (array $attributes) use ($version) {
            return [
                'asset_uuid' => $version->asset_uuid,
                'package_size' => $version->size,
            ];
        });
    }

    public function asError() {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'ERROR',
                'error' => '{message: "Failure"}'
            ];
        });
    }

}
