<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CinemaProServer extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'site_id',
        'cinema_site_id',
        'receive_site_name',
        'serial_number',
        'pub_key_name',
        'raid_type',
        'raid_state',
        'raid_percent_used',
        'raid_size',
        'ip_address',
    ];

    protected $appends = ['status_updated_at'];

    public function getStatusUpdatedAtAttribute()
    {
        return $this->updated_at;
    }

    public function cinemaSite()
    {
        return $this->belongsTo(CinemaSite::class);
    }

    public function content()
    {
        return $this->hasMany(CinemaProContent::class, 'site_id', 'site_id');
    }

    static function fillFromFazzt($data)
    {
        // convert fazzt data to our structure, then find or create based on unique id.
        $map = [
            "RAIDState" => 'raid_state',
            "IPAddress" => 'ip_address',
            "RAIDPercentUsed" => 'raid_percent_used',
            "RAIDSize" => 'raid_size',
            "RAIDType" => 'raid_type',
            "ReceiveSiteName" => 'receive_site_name',
            "SiteID" => 'site_id',
            "SerialNumber" => 'serial_number',
            "PubKeyName" => 'pub_key_name',
        ];

        $payload = [];

        foreach ($map as $key => $destinationKey) {
            $payload[$destinationKey] = $data[$key];
        }

        /** @var CinemaProServer $cps */
        $cps = self::where(['site_id' => $data['SiteID']])->withTrashed()->first()
            ?: self::create($payload);

        // maintain the disabled state from Fazzt as trashed/not trashed.
        if ($data['Disabled'] === "F" && $cps->trashed()) {
            $cps->restore();
        }

        if ($data['Disabled'] === "T" && !$cps->trashed()) {
            $cps->delete();
        }

        $cps->fill($payload);
        $cps->touch();

        return $cps;
    }
}
