<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('cinema_pro_content', function (Blueprint $table) {
            $table->id();

            $table->integer('site_id');
            $table->uuid('asset_uuid');
            $table->integer('file_count')->default(0); // num of files
            $table->text('library_status')->nullable(); // publish/unpublished/??
            $table->text('cru_status')->nullable(); // publish/unpublished/??

            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('cinema_pro_content');
    }
};
