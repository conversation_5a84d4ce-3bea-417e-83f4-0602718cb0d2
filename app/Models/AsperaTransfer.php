<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Symfony\Component\HttpFoundation\Response;

class AsperaTransfer extends Model
{
    use SoftDeletes, HasFactory, LogsActivity;

    // s3 bucket root for organizational purposes
    const ASPERA_ROOT = 'aspera-uploads/';

    protected $fillable = [
        'title_id',
        'version_id',
        'user_id',
        'transfer_uuid',
        'direction',
        'transfer_spec',
        'aspera_transfer_data',
        'title_upload_link_id',
        'status',
    ];

    protected $appends = ['aspera_statistics', 'origin_name', 'file_list', 'bucket', 'origin_root'];

    protected $hidden = ['aspera_transfer_data', 'file_list'];

    protected $casts = [
        'transfer_spec' => 'json',
        'aspera_transfer_data' => 'json',
    ];

    public function version()
    {
        return $this->belongsTo(Version::class);
    }

    // these match aspera's statuses
    const CANCELLED = "cancelled";    //The user stopped the transfer.
    const COMPLETED = "completed";    //The transfer finished successfully.
    const FAILED = "failed";    //The transfer had an error.
    const INITIATING = "initiating";    //The transfer reqeust was accepted. Now starting transfer.
    const QUEUED = "queued";    //The transfer is waiting for other transfers to finish. The queue is
    const REMOVED = "removed";    //The user deleted the transfer.
    const RUNNING = "running";    //Transfer in progress.
    const WILLRETRY = "willretry";    //Transfer waiting to retry after a recoverable error.
    const INVALID_DCP = "invalid"; // dcp failed.

    static function createUpload($payload = null)
    {
        $payload = $payload ?? request()->post('payload');
        return self::create([
            'title_id' => $payload['title_id'],
            'version_id' => $payload['version_id'],
            'user_id' => auth()->user()->id ?? $payload['user_id'],
            'transfer_uuid' => Str::uuid()->toString(),
            'direction' => 'upload',
            'transfer_spec' => [],
            'aspera_transfer_data' => $payload['aspera_data'],
            'title_upload_link_id' => $payload['title_upload_link_id'] ?? null,
            'status' => self::INITIATING,
        ])->createUploadTransferSpec();
    }

    static function createDownload()
    {

        $payload = request()->post('payload');

        $version = Version::findOrFail($payload['version_id']);

        if (!$version->s3_details) {
            // nothing was uploaded, call it a 404.
            abort(Response::HTTP_NOT_FOUND);
        }

        $root = $version->s3_details['root'];
        $pathName = $version->s3_details['path'];

        return self::create([
            'title_id' => $payload['title_id'],
            'version_id' => $payload['version_id'],
            'user_id' => auth()->user()->id,
            'transfer_uuid' => Str::uuid()->toString(),
            'direction' => 'download',
            'transfer_spec' => [],
            'aspera_transfer_data' => [],
            'status' => self::INITIATING,
        ])->createDownloadTransferSpec($root, $pathName, $version->s3_details['bucket'] ?? 's3');

    }

    private function createDownloadTransferSpec($root, $pathName, $bucket = 's3')
    {
        $spec = [
            'transfer_requests' => [
                [
                    'transfer_request' => [
                        'authentication' => 'token',
                        'tags' => [
                            'cinesend' => [
                                'name' => str_replace('/', '', $pathName),
                                'transfer_uuid' => $this->transfer_uuid,
                            ],
                        ],
                        'source_root' => $root ?? '/',
                        'paths' => [
                            ['source' => $pathName],
                        ],
                    ],
                ],
            ],
        ];

        $result = $this->getAsperaCommandResult('download_setup', $spec, $bucket);

        $this->transfer_spec = $result;
        $this->save();

        return $result;

    }

    private function createUploadTransferSpec()
    {
        $spec = [
            'transfer_requests' => [
                [
                    'transfer_request' => [
                        'destination_root' => self::ASPERA_ROOT . $this->transfer_uuid, //  uuid path
                    ],
                ],
            ],
        ];

        $result = $this->getAsperaCommandResult('upload_setup', $spec);

        $name = $this->aspera_transfer_data['files'][0]['name'];
        $opts = [
            'tags' => [
                'cinesend' => [
                    'transfer_uuid' => $this->transfer_uuid,
                ],
                'aspera' => [
                    'cloud-metadata' => [
                        [
                            'Content-Disposition' => 'attachment; filename="' . basename($name) . '"',
                        ],
                    ],
                ],
            ],
            'create_dir' => true,
            'destination_root' => self::ASPERA_ROOT . $this->transfer_uuid,
            'authentication' => "token",
            'paths' => [
                ['source' => $name],
            ],
        ];

        if ($result === null) {
            abort(Response::HTTP_UNPROCESSABLE_ENTITY, "There was a problem contacting Aspera.");
        }
        $spec = array_merge($result['transfer_specs'][0]['transfer_spec'], $opts);

        $this->transfer_spec = $spec;
        $this->save();

        return $this;
    }

    public function getAsperaStatisticsAttribute()
    {
        $stats = new \stdClass();
        if (isset($this->aspera_transfer_data['start_time_usec'])) {
            $startTime = Carbon::createFromTimestamp($this->aspera_transfer_data['start_time_usec'] / 1000000);
            $endTime = Carbon::createFromTimestamp($this->aspera_transfer_data['end_time_usec'] / 1000000);
        }
        else if (isset($this->aspera_transfer_data['start_time'])) {
            $startTime = Carbon::create($this->aspera_transfer_data['start_time']);
            $endTime = Carbon::create($this->aspera_transfer_data['end_time']);

        }

        $stats->started_at = $startTime ?? null;
        $stats->end_at = $endTime ?? null;
        $stats->bytes_expected = $this->aspera_transfer_data['precalc']['bytes_expected'] ?? 0;
        $stats->bytes_written = $this->aspera_transfer_data['bytes_written'] ?? 0;
        $stats->calculated_rate_kbps = $this->aspera_transfer_data['avg_rate_kbps'] ?? $this->aspera_transfer_data['calculated_rate_kbps'] ?? 0;
        return $stats;
    }

    public function getOriginNameAttribute()
    {
        if (isset($this->aspera_transfer_data['start_spec'])) {
            return basename($this->aspera_transfer_data['start_spec']['source_paths'][0]) ?? 'N/A';
        }

        if (isset($this->transfer_spec['path'])) {
            return $this->transfer_spec['path'] ?? 'N/A';
        }

        return 'TBD';
    }

    public function getFileListAttribute()
    {
        // this relies on the pulled full completed data object from the aspera transfers endpoint, NOT the front end.

        $files = [];

        if ($this->direction === 'download' || $this->status !== self::COMPLETED) {
            return $files;
        }

        if (!$this->aspera_transfer_data['files']) {
            return $files;
        }

        $remove = '/' . self::ASPERA_ROOT . $this->transfer_uuid;
        foreach ($this->aspera_transfer_data['files'] as $file) {
            $cleanedFile = new \stdClass();
            $cleanedFile->s3_path = str_replace($remove, '', $file['path'] ?? '');
            $cleanedFile->file_size = $file['bytes_written'] ?? 0;
            $files[] = $cleanedFile;
        }

        usort($files, function ($a, $b) {
            return strcmp($a->s3_path, $b->s3_path);
        });
        return $files;
    }

    private function getAsperaCommandResult($command, $spec, $bucket = 's3')
    {
        $host = config("aspera.s3.transfer.host_name");
        $user = config("aspera.s3.user");
        $password = config("aspera.s3.password");

        if ($bucket === 's3-deluxe') {
            $user = config("aspera.deluxe.key");
            $password = config("aspera.deluxe.secret");
        }

        $ch = curl_init($host . "/files/" . $command);
        $options = [
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_USERPWD => $user . ":" . $password,
            CURLOPT_HTTPHEADER => ["Content-type: application/json"],
            CURLOPT_POSTFIELDS => json_encode($spec),
        ];
        curl_setopt_array($ch, $options);
        $result = curl_exec($ch);
        return json_decode($result, true);
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->dontSubmitEmptyLogs()
            ->logOnly(['status'])
            ->logOnlyDirty();
    }

    public function getOriginRootAttribute()
    {
        // determine where this origin is, based on deluxe upload or regular upload.

        if (isset($this->aspera_transfer_data['destination_root'])) {
            return $this->aspera_transfer_data['destination_root'];
        }

        if (isset($this->transfer_spec['root'])) {
            return $this->transfer_spec['root'] . $this->transfer_spec['path'];
        }

        return self::ASPERA_ROOT . $this->transfer_uuid;

    }

    public function getBucketAttribute()
    {
        // determine the bucket based on deluxe upload or regular upload
        if (isset($this->transfer_spec['bucket'])) {
            return $this->transfer_spec['bucket'];
        }

        return 's3';
    }

    private function realBasename($path)
    {
        // unfortunately 'basename' only works for paths on the OS PHP is running on...
        // so these paths that come from windows uploads don't match properly.. so here we go...
        if (preg_match('@^.*[\\\\/]([^\\\\/]+)$@s', $path, $matches)) {
            return $matches[1];
        }
        else if (preg_match('@^([^\\\\/]+)$@s', $path, $matches)) {
            return $matches[1];
        }
        return '';

    }

}
