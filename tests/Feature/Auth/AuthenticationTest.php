<?php

namespace Tests\Feature\Auth;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AuthenticationTest extends TestCase
{

    public function test_non_json_request_is_redirected() {
        $response = $this->get('/api/user');
        $response->assertRedirect();
    }

    public function test_anonymous_cannot_access_user() {
        $response = $this->getJson('/api/user');
        $response->assertUnauthorized();
    }

    public function test_users_can_authenticate_using_the_login_screen()
    {
        $user = User::factory()->create();

        $response = $this->postJson('/login', [
            'email' => $user->email,
            'password' => 'testing123',
        ]);

        $this->assertAuthenticated();
        $response->assertSuccessful();
    }

    public function test_users_can_not_authenticate_with_invalid_password()
    {
        $user = User::factory()->create();

        $this->postJson('/login', [
            'email' => $user->email,
            'password' => 'wrong-password',
        ]);

        $this->assertGuest();
    }
}
