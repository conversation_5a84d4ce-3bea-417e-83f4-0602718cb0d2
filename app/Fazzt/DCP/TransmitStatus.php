<?php

namespace App\Fazzt\DCP;

use App\Fazzt\Exception;
use App\Fazzt\Json;
use App\Models\FazztTransfer;
use Illuminate\Console\Command;

class TransmitStatus
{

    static function exec($uuid)
    {
        $params = [
            0 => $uuid,
        ];
        try {

            $response = Json::jsonCall(
                'CinemaDCPPackageTransmitStatus',
                null,
                $params,
                false);

            dump($response['result']);
            $response = $response['result'];

            $dataset = [
                'transmit_id' => $uuid,
                'start_time' => new \Carbon\Carbon($response['StartTime'], 'America/New_York'),
                'duration' => $response['Duration'],
                'status' => $response['Status'],
                'percent' => $response['Percent'],
                'state' => $response['State'],
                'priority' => 0, // this is being handled by fazzt at this point, no more changes allowed from frontend.
                'estimated_minutes' => ($response['Duration'] / 1000 / 60) // Duration is MS, I want minutes for dayjs frontend things.
            ];

            if ($response['State'] === 'COMPLETE') {
                $dataset['actual_completed_at'] = now();
            }

            $ft = FazztTransfer::firstOrCreate(['transmit_id' => $uuid], $dataset);

            if (!$ft->wasRecentlyCreated) {
                $ft->update($dataset);
            }

            /**
             * "StartTime" => "2024-08-15 11:15:53"
             * "Duration" => 17140
             * "Message" => "Successfully sent DCP DCP_63775ee3-382a-4f86-aff1-f7c0b8b47a60.fpk"
             * "Percent" => 100
             * "Status" => "SUCCESS"
             * "State" => "COMPLETE"
             */
            return true;
        }
        catch (\Exception $e) {
            logger($e->getMessage());
        }

        return false;

    }
}
