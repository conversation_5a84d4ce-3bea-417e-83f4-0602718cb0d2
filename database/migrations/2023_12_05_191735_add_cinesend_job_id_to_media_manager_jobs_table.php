<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('media_manager_jobs', function (Blueprint $table) {
            //
            $table->string('cinesend_job_id', 32)->after('status')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('media_manager_jobs', function (Blueprint $table) {
            //
            $table->dropColumn('cinesend_job_id');
        });
    }
};
