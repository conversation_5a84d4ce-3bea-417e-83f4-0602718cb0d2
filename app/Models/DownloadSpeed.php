<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DownloadSpeed extends Model
{
    use HasFactory;

    protected $table = 'cinema_site_download_speeds';

    protected $guarded = [];

    protected $casts = [
        'download_speed' => 'float',
        'is_aspera_transfer' => 'boolean'
    ];

    protected $attributes = [
        'is_aspera_transfer' => false
    ];

    public function cinemaSite()
    {
        return $this->belongsTo(CinemaSite::class);
    }
}