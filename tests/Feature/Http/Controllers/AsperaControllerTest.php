<?php

namespace Tests\Feature\Http\Controllers;

use App\Models\CinemaSite;
use App\Models\Booking;
use App\Models\Enum\BookingStatus;
use App\Models\Organization;
use App\Models\Title;
use App\Models\User;
use App\Models\Version;
use Tests\TestCase;

class AsperaControllerTest extends TestCase
{
    public function test_it_does_not_create_an_upload_without_aspera_credentials()
    {
        $admin = User::factory()->create()->assignRole('admin');
        $organization = Organization::factory()->withOwner($admin)->create();
        $organization->users()->save($admin);

        $response = $this->actingAs($admin)->postJson(route('aspera-upload'),
            [
                'payload' => [
                    'title_id' => 1,
                    'version_id' => 1,
                    'aspera_data' => [
                        'files' => [['name' => 'filename']],
                    ],
                    'transfer_specs' => [['transfer_spec' => []]],
                ],
            ]);
        $response->assertUnprocessable();

    }

    public function test_it_does_not_create_a_download_for_invalid_version()
    {

        $admin = User::factory()->create()->assignRole('admin');
        $organization = Organization::factory()->withOwner($admin)->create();
        $organization->users()->save($admin);

        $response = $this->actingAs($admin)->postJson(route('aspera-download'),
            ['payload' => ['version_id' => 2, 'title_id' => 1]]);
        $response->assertNotFound();

    }
}
