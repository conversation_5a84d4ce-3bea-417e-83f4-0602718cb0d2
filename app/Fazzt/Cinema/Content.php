<?php

namespace App\Fazzt\Cinema;

use App\Fazzt\Json;

class Content
{
    static function exec($siteId)
    {
        $callbackParams = [
            0 => 'contentquery',
            1 => '',
            2 => [
                'OnClientCallback' => config('fazzt.callback_url') . 'content',
                'SiteID' => $siteId,
                'QueueName' => 'Channel 1 Preprocess', // todo: proper channel.
                'TransmissionModeName' => 'With Reports Command',
            ],
        ];
        try {
            $response = Json::jsonCall(
                'CinemaCmdSubmit',
                null,
                $callbackParams, true);

            return true;
        }
        catch (\Exception $e) {
            logger($e->getMessage());
        }

        return false;

    }
}


