<?php

namespace App\Console\Commands;

use App\Models\AsperaTransfer;
use App\Models\Version;
use App\Actions\Dcp\SplitDcp;
use Illuminate\Console\Command;

class SplitDcpCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'dcp:split {versionId?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Splits a DCP from a mastercopy package to individual versions';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $versionId = $this->argument('versionId');
        $version = Version::find($versionId);

        if (! $version) {
            $this->error("VersionId {$versionId} could not be found.");
            return 404;
        }

        $this->info("Processing VersionId {$versionId} - {$version->version_name} from s3://{$version->s3_details['root']}");

        $results = SplitDcp::execute($version);

        return 0;
    }
}
