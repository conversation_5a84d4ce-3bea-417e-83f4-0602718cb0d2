{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.2", "ext-soap": "*", "ext-curl": "*", "ext-simplexml": "*", "doctrine/dbal": "^3.4", "guzzlehttp/guzzle": "^7.2", "laravel/fortify": "^1.13", "laravel/framework": "^9.11", "laravel/sanctum": "^2.14.1", "laravel/telescope": "^4.9", "laravel/vapor-cli": "^1.41", "laravel/vapor-core": "^2.22", "league/flysystem-read-only": "^3.15", "maatwebsite/excel": "^3.1", "nesbot/carbon": "^2.66", "pda/pheanstalk": "^4.0", "phpoffice/phpspreadsheet": "^1.29.8", "sentry/sentry-laravel": "^2.13", "spatie/laravel-activitylog": "^4.5", "spatie/laravel-permission": "^5.5", "spatie/laravel-slack-alerts": "^1.2", "twilio/sdk": "^6.42"}, "require-dev": {"brianium/paratest": "^6.4", "fakerphp/faker": "^1.9.1", "kitloong/laravel-migrations-generator": "^6.10", "laravel/breeze": "^1.9", "laravel/sail": "^1.0.1", "laravel/tinker": "^2.7", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ignition": "^1.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true}