<?php

namespace App\Http\Middleware;

use Closure;

class MediaManagerMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // Todo: Remove this in prod when cmms updates are in place (re-using auth cookies properly)
        if (app()->environment('production')) {
            return $next($request);
        }

        if (!$request->route('serialNumber') || $request->route('serialNumber') === 'undefined') {
            return response('Invalid device serial number.', 401);
        }

        if (!auth()->guard('media-manager')->check()) {
            return response('Device not logged in.', 401);
        }

        if (auth()->guard('media-manager')->user()->serial_number !== $request->route('serialNumber')) {
            return response('Device mismatch, you may not access this resource.', 401);
        }

        return $next($request);
    }
}
