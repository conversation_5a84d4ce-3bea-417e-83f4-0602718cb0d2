<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateWebhookRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'event_model' => ['sometimes', 'in:booking'],
            'event_type' => ['sometimes', 'in:update'],
            'url' => ['sometimes', 'url'],
            'bearer_token' => ['sometimes', 'string', 'nullable'],
            'http_headers' => ['sometimes', 'array'],
        ];
    }
}
