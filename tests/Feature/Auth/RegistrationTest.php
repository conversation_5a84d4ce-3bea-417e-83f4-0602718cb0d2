<?php

namespace Tests\Feature\Auth;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class RegistrationTest extends TestCase
{

    public function test_new_users_cannot_register()
    {
        $user = User::factory()->make();
        $response = $this->postJson('/register', [
            'name' =>  $user->name,
            'email' =>  $user->email,
            'password' => 'Password$!@',
            'password_confirmation' => 'Password$!@',
        ]);

        $response->assertNotFound();
    }
}
