<?php

namespace Database\Factories;

use App\Models\CinemaSite;
use App\Models\Organization;
use App\Models\Release;
use App\Models\Title;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Booking>
 */
class BookingFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'deliver_at' => $this->faker->dateTimeBetween('now', 'last day of this month 23:59:59'),
            'overall_status' => 'pending',
            'is_electronic' => false
        ];
    }

    public function withCreator(User $user)
    {
        return $this->state(function (array $attributes) use ($user) {
            return [
                'creator_id' => $user->id,
            ];
        });
    }

    public function withTitle(Title $title)
    {
        return $this->state(function (array $attributes) use ($title) {
            return [
                'title_id' => $title->id,
            ];
        });
    }

    public function withCinema(CinemaSite $cinema)
    {
        return $this->state(function (array $attributes) use ($cinema) {
            return [
                'cinema_site_id' => $cinema->id,
            ];
        });
    }

    public function withOrganization(Organization $org)
    {
        return $this->state(function (array $attributes) use ($org) {
            return [
                'organization_id' => $org->id,
            ];
        });
    }

    public function withRelease(Release $release)
    {
        return $this->state(function (array $attributes) use ($release) {
            return [
                'package_id' => $release->id,
            ];
        });
    }


}
