<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('import_bookings', function (Blueprint $table) {
            $table->id();

            $table->foreignId('user_id');
            $table->foreignId('title_id')->nullable();
            $table->json('version_ids')->nullable(); // an array of Ids.
            $table->date('release_date')->nullable();

            $table->string('s3_file')->nullable(); // short filename to be opened via storage system
            $table->string('detected_type')->nullable();
            $table->string('origin_name')->nullable();

            $table->integer('header_row_index')->nullable();
            $table->json('headers')->nullable(); // headers from the original file
            $table->json('mapping')->nullable();
            // headers to local property lookup (for cinema sites, verified by uploader.
            // ie: TheatreName (in xls) => 'site_name' in model.
            // or Technicolor ID to TCN, etc.

            $table->boolean('verified')->nullable(); // user has verified header mapping and will be processed.
            $table->boolean('processed')->nullable(); // processing has completed.

            $table->string('error_message')->nullable();

            $table->integer('num_records')->nullable(); // total in file
            $table->integer('num_records_processed')->nullable(); // number of rows from file
            $table->integer('num_records_imported')->nullable(); // number of rows actually imported
            $table->integer('num_warnings')->nullable(); // attempts to import that have issues
            $table->integer('num_errors')->nullable(); // attempts ot import that failed

            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('import_bookings');
    }
};
