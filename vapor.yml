id: 33556
name: dcdc-portal-api
environments:
  production:
    memory: 1024
    cli-memory: 512
    timeout: 31
    runtime: 'php-8.2:al2'
    database: dcdc-db-production
    storage: dcdc-production-uploads
    domain: api.dcdcdistribution.com
    build:
      - 'COMPOSER_MIRROR_PATH_REPOS=1 composer install --no-dev'
      - 'php artisan event:cache'
    deploy:
      - 'php artisan migrate --force'
  staging:
    memory: 1024
    cli-memory: 512
    runtime: 'php-8.2:al2'
    database: dcdc-db-staging
    storage: dcdc-staging-uploads
    domain: staging-api.dcdcdistribution.com
    build:
      - 'COMPOSER_MIRROR_PATH_REPOS=1 composer install'
      - 'php artisan event:cache'
    deploy:
      - 'php artisan migrate --force'
