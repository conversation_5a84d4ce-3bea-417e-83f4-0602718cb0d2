<?php

namespace App\DcHub;

use Carbon\Carbon;
use SoapFault;

class Cinemas
{
    /*
    Soap Calls:
    getCinemaList(credentials $credentials, string $codeCountryIsoA2, dateTime $fromDate, string $apiVersion)
    getCinemaInfo(credentials $credentials, string $cinemaMbId, string $apiVersion)
     */
    static function getCinemaList(array $filter = [])
    {
        $params = [
            '$codeCountryIsoA2' => 'US',
            'fromDate'=> Carbon::create('1950-01-01')->toDateTimeLocalString()
        ];

        if ($filter) {
            $params = array_merge($params, $filter);
        }

        $response = Soap::soapCall('getCinemaList', $params);

        $cinemas = simplexml_load_string($response->xml);

        logger("identified " . count($cinemas) . " cinema(s)");

        foreach ($cinemas as $cinema) {
            // maybe we create cinemas on our end or lookup the maccsID to find them.

            dump($cinema);
        }

    }

    static function getCinemaInfo($cinemaId)
    {

        $response = Soap::soapCall('getCinemaInfo', ['cinemaMbId' => $cinemaId]);

        $cinema = simplexml_load_string($response->xml);

        dump($cinema);

    }
}
