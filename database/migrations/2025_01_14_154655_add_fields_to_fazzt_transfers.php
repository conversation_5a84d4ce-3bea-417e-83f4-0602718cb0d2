<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('fazzt_transfers', function (Blueprint $table) {
            //
            $table->uuid('transmit_id')->nullable()->change();
            $table->uuid('asset_uuid')->nullable()->change();
            $table->uuid('start_time')->nullable()->change();

            $table->integer('priority')->nullable()->after('state');
            $table->datetime('scheduled_start_at')->nullable()->after('priority');
            $table->integer('estimated_minutes')->nullable()->after('scheduled_start_at');
            $table->datetime('actual_start_at')->nullable()->after('scheduled_start_at');
            $table->datetime('actual_completed_at')->nullable()->after('actual_start_at');

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('fazzt_transfers', function (Blueprint $table) {
            //
            $table->dropColumn('priority');
            $table->dropColumn('scheduled_start_at');
            $table->dropColumn('actual_start_at');
            $table->dropColumn('estimated_minutes');
            $table->dropColumn('actual_completed_at');
        });
    }
};
