<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Organization;
use App\Models\User;
use Laravel\Sanctum\PersonalAccessToken;
use Symfony\Component\HttpFoundation\Response;

class UserTokenController extends Controller
{
    public function index(User $user)
    {
        $this->authorize('viewAny', PersonalAccessToken::class);
        if (auth()->user()->isAdmin()) {
            $tokens = $user->tokens()->select(['id', 'name', 'last_used_at', 'created_at'])->get();
        }
        else {
            $tokens = auth()->user()->tokens()->select(['id', 'name', 'last_used_at', 'created_at'])->get();
        }
        return ['tokens' => $tokens];
    }

    public function store(Request $request, User $user)
    {
        $this->authorize('create', PersonalAccessToken::class);
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
        ]);

        if (auth()->user()->isAdmin()) {
            $token = $user->createToken($request->name);
        }
        else {
            $token = auth()->user()->createToken($request->name);
        }

        return [
            'token' => $token->plainTextToken,
            'message' => 'Save this token securely. It will not be shown again.',
        ];

    }

    public function destroy(User $user, PersonalAccessToken $token)
    {
        //$this->authorizeForUser($user, 'delete', [$user, $token]);
        $this->authorize('delete', [PersonalAccessToken::class, $user, $token]);
        if (! auth()->user()->isAdmin()) {
            $user = auth()->user();
        }
        $token->delete();
        return ['status' => 'ok'];
    }
}
