<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ImportBooking extends Model
{
    use HasFactory, SoftDeletes;

    protected $appends = ['lookup', 'pending_count', 'matched_count', 'booked_count'];
    protected $with = ['title'];

    protected $fillable = [
        'user_id',
        'title_id',
        'version_ids',
        'package_id',
        'release_date',
        'engagement_start',
        's3_file',
        'detected_type',
        'origin_name',
        'headers',
        'mapping',
        'verified',
        'processed',
        'num_records',
        'num_records_to_import',
        'numb_records_imported',
        'num_warnings',
        'num_errors',
    ];

    protected $dates = ['release_date', 'engagement_start'];

    protected $casts = [
        'headers' => 'json',
        'mapping' => 'json',
        'version_ids' => 'json',
        'verified' => 'boolean',
        'processed' => 'boolean',
        'num_records' => 'int',
        'num_records_to_import' => 'int',
        'numb_records_imported' => 'int',
        'num_warnings' => 'int',
        'num_errors' => 'int',
    ];

    public function release()
    {
        return $this->belongsTo(Release::class, 'package_id');
    }

    public function records()
    {
        return $this->hasMany(ImportBookingRecord::class);
    }

    public function pendingRecords()
    {
        return $this->records()->whereNull('cinema_site_id');
    }

    public function matchedRecords()
    {
        return $this->records()->whereNotNull('cinema_site_id')->whereNull('booking_id');
    }

    public function bookedRecords()
    {
        return $this->records()->whereNotNull('booking_id');
    }

    public function title()
    {
        return $this->belongsTo(Title::class);
    }

    public function getMatchedCountAttribute()
    {
        return $this->matchedRecords()->count();
    }

    public function getPendingCountAttribute()
    {
        return $this->pendingRecords()->count();
    }

    public function getBookedCountAttribute()
    {
        return $this->bookedRecords()->count();
    }

    public function getLookupAttribute()
    {
        return [
            ['key' => 'address', 'label' => 'Cinema Address'],
            ['key' => 'name', 'label' => 'Cinema Name',],
            ['key' => 'tcn', 'label' => 'TCN #',],
            ['key' => 'secondary_tcn', 'label' => 'Secondary TCN #'],
            ['key' => 'rentrack', 'label' => 'Rentrack #'],
            ['key' => 'sage_customer_number', 'label' => 'DCDC / Sage #'],
            ['key' => 'universal_site_id', 'label' => 'Universal Site ID'],
            ['key' => 'sony_site_id', 'label' => 'Sony Site ID'],
            ['key' => 'paramount_theatre_id', 'label' => 'Paramount Theatre ID'],
            ['key' => 'lionsgate_theatre_id', 'label' => 'Lionsgate Theatre ID'],
            ['key' => 'dchub_cinema_id', 'label' => 'DCHub Cinema ID'],
        ];
    }
}
