<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('packages', function (Blueprint $table) {
            $table->id();

            $table->string('package_name');
            $table->foreignId('title_id');
            $table->foreignId('organization_id');

            $table->string('ingest_letter_url')->nullable(); // s3 url for where this thing is, to email.
            $table->json('studio_data')->nullable(); // aka {packageId: xxx} for auto created lookups

            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('packages');
    }
};
