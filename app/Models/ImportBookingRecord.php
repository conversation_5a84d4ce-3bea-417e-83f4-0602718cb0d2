<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ImportBookingRecord extends Model
{
    use HasFactory, SoftDeletes;

    protected $with = ['cinema'];

    protected $fillable = [
        'import_booking_id',
        'original_record',
        'original_index',
        'matched',
        'booking_id',
        'cinema_site_id',
    ];

    protected $casts = [
        'original_record' => 'json',
        'matched' => 'boolean',
    ];

    public function importBooking()
    {
        return $this->belongsTo(ImportBooking::class);
    }

    public function cinema()
    {
        return $this->belongsTo(CinemaSite::class, 'cinema_site_id', 'id');
    }
}
