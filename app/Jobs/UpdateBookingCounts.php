<?php

namespace App\Jobs;

use App\Models\Booking;
use App\Models\Title;
use App\Models\Release;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class UpdateBookingCounts implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $title_id;
    public $package_id;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Booking $booking)
    {
        $this->package_id = $booking->package_id;
        $this->title_id = $booking->title_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        if ($this->package_id) {
            /** @var Release $package */
            $package = Release::find($this->package_id);
            if ($package) {
                $package->fillBookingCounts();
            }

        }

        if ($this->title_id) {
            /** @var Title $title */
            $title = Title::find($this->title_id);
            if ($title) {
                $title->fillBookingCounts();
            }
        }
    }
}
