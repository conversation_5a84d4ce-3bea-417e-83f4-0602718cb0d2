<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('organization_webhook_urls', function (Blueprint $table) {
            $table->id();

            $table->foreignId('organization_id');
            $table->foreignId('creator_id');

            $table->string('event_model'); // 'bookings', 'users'
            $table->string('event_type'); // 'create', 'update', ...

            $table->text('url');
            $table->string('bearer_token')->nullable();
            $table->json('http_headers')->nullable();

            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('organization_webhook_urls');
    }
};
