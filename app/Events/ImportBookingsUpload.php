<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ImportBookingsUpload implements ShouldQueue
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public int $importBookingId = 0;

    /**
     * Emit an event with the required data to be parsed on the queue.
     * @return void
     */
    public function __construct($importBookingId)
    {
        logger("event setup for ID: ". $importBookingId);
        $this->importBookingId = $importBookingId;
    }

}
