<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreNoteRequest;
use App\Http\Requests\UpdateNoteRequest;
use App\Models\CinemaSite;
use App\Models\MediaManager;
use App\Models\MediaManagerJob;
use App\Models\Note;
use App\Models\User;

class NoteController extends Controller
{
    public function __construct()
    {
        $this->authorizeResource(Note::class);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
        return false;
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \App\Http\Requests\StoreNoteRequest $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreNoteRequest $request)
    {
        //
        return false;
    }

    /**
     * Display the specified resource.
     *
     * @param \App\Models\Note $note
     * @return \Illuminate\Http\Response
     */
    public function show(Note $note)
    {
        //
        return ['note' => $note];
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \App\Http\Requests\UpdateNoteRequest $request
     * @param \App\Models\Note $note
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateNoteRequest $request, Note $note)
    {
        // figure out what the notable() is off of this note and if the user has permissions
        //
        $note->fill($request->validated())->save();
        return $this->show($note);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\Models\Note $note
     * @return \Illuminate\Http\Response
     */
    public function destroy(Note $note)
    {
        // figure out what the notable() is off of this note and if the user has permissions
        //
        $note->delete();
        return $this->show($note);
    }

    public function saveEquipmentNote(StoreNoteRequest $request, MediaManager $mediaManager)
    {
        // this requires the user to be able to view the media manager.
        auth()->user()->can('view', $mediaManager);

        $validated = $request->validated();
        $validated['user_id'] = auth()->user()->id;

        $note = $mediaManager->notes()->create($validated);

        return ['note' => $note];

    }

    public function getEquipmentNotes(MediaManager $mediaManager)
    {
        return $mediaManager->notes()->select(['id', 'note', 'user_id','updated_at'])
            ->with('user:id,name')->latest('updated_at')->paginate(20);
    }

    public function saveSiteNote(StoreNoteRequest $request, CinemaSite $cinemaSite)
    {
        // this requires the user to be able to view the cinemasite.
        auth()->user()->can('view', $cinemaSite);

        $validated = $request->validated();
        $validated['user_id'] = auth()->user()->id;

        $note = $cinemaSite->notes()->create($validated);

        return ['note' => $note];
    }

    public function getSiteNotes(CinemaSite $cinemaSite)
    {
        return $cinemaSite->notes()->select(['id', 'note', 'user_id','updated_at'])
            ->with('user:id,name')->latest('updated_at')->paginate(20);
    }
}
