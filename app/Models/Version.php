<?php

namespace App\Models;

use App\Fazzt\DCP\Ingest;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Storage;

class Version extends Model
{
    use SoftDeletes, HasFactory, LogsActivity;

    protected $fillable = [
        'parent_version_id',
        'nickname',
        'version_name',
        'creator_id',
        'cpl_uuid',
        'asset_uuid',
        'multi_cpl_uuids',
        'dchub_version_id',
        'size',
        'title_id',
        's3_details',
        'is_ready',
    ];

    protected $touches = ['title'];

    protected $casts = ['s3_details' => 'json', 'is_ready' => 'boolean', 'multi_cpl_uuids' => 'json'];

    protected $hidden = ['s3_details', 'transfer_spec', 'aspera_transfer_data'];

    protected $attributes = [
        'nickname' => '',
    ];

    public function title()
    {
        return $this->belongsTo(Title::class);
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    public function asperaTransfer()
    {
        return $this->hasOne(AsperaTransfer::class);
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->dontSubmitEmptyLogs()
            ->logFillable()
            ->logOnlyDirty();
    }

    public function releases()
    {
        return $this->belongsToMany(Release::class, 'package_version', 'version_id', 'package_id');
    }

    // use this to puplate the s3_details after the aspera transfer is complete.
    // if it's a multi dcp upload, the splitter will re-populate as necssary.
    public function fillS3DetailsFromTransfer(AsperaTransfer $asperaTransfer)
    {
        $s3details = new \stdClass();

        if (!$asperaTransfer) {
            $s3details->origin_transfer_id = null;
            $s3details->status = 'no_transfer';
            $s3details->root = '';
        } else {
            $s3details->origin_transfer_id = $asperaTransfer->id;
            // grab the s3 path from the aspera transfer object instead destination_root or root

            $s3details->path = ltrim($asperaTransfer->origin_root, '/');
            $s3details->root = '/';
            $s3details->bucket = $asperaTransfer->bucket;

            $s3 = Storage::disk($asperaTransfer->bucket);

            try {
                $files = $s3->allFiles($asperaTransfer->origin_root);
            } catch (\Aws\S3\Exception\S3Exception $e) {
                $files = [];
                $s3details->status = 'no_permissions';
                $s3details->root = '';
            }

            // grab the file list
            $bucketFiles = [];
            $totalSize = 0;

            foreach ($files as $file) {
                $size = $s3->size($file);
                $fileObject = new \stdClass();
                $fileObject->size = $size;
                $fileObject->bytes_written = $size;
                $fileObject->path = $file;
                $bucketFiles[] = $fileObject;
                $totalSize += $size;
            }
            $s3details->files = $bucketFiles;
        }
        $this->multi_cpl_uuids = isset($asperaTransfer->aspera_transfer_data['dcp_results']['cpl_multi_uuids'])
            ? $asperaTransfer->aspera_transfer_data['dcp_results']['cpl_multi_uuids'] : [];
        $this->size = $totalSize;
        $this->s3_details = $s3details;
        $this->save();
    }

    public function shiftS3Paths()
    {
        // simply move the first part of the path into the root on s3 details, so the download transfers the single dcp folder.
        $s3details = $this->s3_details;

        $oldRoot = $s3details['root'];
        $oldPath = $s3details['path'];
        $firstFile = $s3details['files'][0];
        $firstPath = $firstFile['path'];

        $fullPath = trim($oldRoot . $firstPath, '/');

        $newPath = str_replace($oldPath, '', dirname($fullPath));

        $s3details['root'] = $oldPath . '/';
        $s3details['path'] = ltrim($newPath, '/');

        $this->s3_details = $s3details;
        $this->save();

    }

    public function fazztContent()
    {
        return $this->hasOne(FazztContent::class, 'asset_uuid', 'asset_uuid');
    }

    public function fazztTransfer()
    {
        return $this->hasOne(FazztTransfer::class, 'asset_uuid', 'asset_uuid');
    }

    public function getBucketAttribute()
    {
        $s3details = $this->s3_details;
        return $s3details['bucket'] ?? 's3';
    }

    public function ingestIntoFazzt() {
        // tell fazzt to inget this version as an FPK.
        // todo: determine if this DCP has ASSETMAP or ASSETMAP.XML (it's important.)
        $path = sprintf("Z:\\%s\\ASSETMAP.xml", $this->s3_details['path']);
        Ingest::exec($path);
    }
}
