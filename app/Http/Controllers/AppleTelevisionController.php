<?php

namespace App\Http\Controllers;

use App\Models\AppleTelevision;
use App\Models\CinemaSite;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Schema;
use App\Http\Requests\UpdateAppleTelevisionRequest;
use Carbon\Carbon;

class AppleTelevisionController extends Controller
{
    public function ping()
    {
        // todo: find or create based on serial number passed into the call
        $appleTelevision = null;

        return $this->show($appleTelevision);
    }

    public function index()
    {
        $query = AppleTelevision::query()->select([
            'id',
            'serial_number',
            'remote_ip_address',
            'hexnode_device_id',
            'cinema_site_id',
            'updated_at',
            'status_updated_at',
        ]);

        $search = request()->get('search');

        return $query
            ->with('cinemaSite')
            ->when($search, function ($q, $search) {
                $search = "%{$search}%";
                $q->where('serial_number', 'like', $search);
            })
            ->orderBy(request()->get('sortBy', 'serial_number'), request()->get('sortDirection', 'ASC'))
            ->paginate(request()->get('take', 15));

    }

    public function update(UpdateAppleTelevisionRequest $request, AppleTelevision $appleTelevision)
    {
        $request->validated();
        $appleTelevision->update($request->all());
        return $this->show($appleTelevision);
    }

    public function show(AppleTelevision $appleTelevision)
    {
        return ['appleTelevision' => $appleTelevision];
    }
}
