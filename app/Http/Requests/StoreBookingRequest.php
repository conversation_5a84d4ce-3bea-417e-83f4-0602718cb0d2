<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreBookingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'cinema_site_id' => ['required', 'numeric'],
            'package_id' => ['required', 'numeric'],
            'deliver_at' => ['required', 'date'],
            'release_date' => ['sometimes', 'date'],
            'is_electronic' => ['required', 'boolean']
        ];
    }
}
