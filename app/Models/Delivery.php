<?php

namespace App\Models;

use App\Jobs\VerifyBooking;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use GuzzleHttp\Client;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use App\Models\Enum\BookingStatus;

class Delivery extends Model
{
    use SoftDeletes, HasFactory;

    protected $fillable = [
        'organization_id',
        'cinema_site_id',
        'version_id',
        'title_id',
        'package_id',
        'is_electronic',
        'progress',
        'booking_id',
        'cpl_uuid',
        'external_id',
        'status',
        'speed_in_mbps',
        'details',
    ];

    protected $casts = [
        'progress' => 'integer',
        'speed_in_mbps' => 'integer',
        'status' => BookingStatus::class,
    ];

    protected $attributes = [
        'progress' => 0,
        'speed_in_mbps' => 0,
        'status' => BookingStatus::Pending,
        'is_electronic' => true,
    ];

    protected $appends = ['transfer_speed'];

    public function getTransferSpeedAttribute()
    {
        $statuses = [];
        if ($this->status === BookingStatus::Completed) {
            return '';
        }

        if ($this->progress && $this->progress !== 100) {
            $statuses[] = $this->progress . "%";
            if ($this->speed_in_mbps) {
                $statuses[] = $this->speed_in_mbps . " mbps";
            }
        }
        return implode(" • ", $statuses);
    }

    public function release()
    {
        return $this->belongsTo(Release::class, 'package_id');
    }

    public function jobs()
    {
        return $this->hasMany(MediaManagerJob::class);
    }

    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    public function version()
    {
        return $this->belongsTo(Version::class)->withTrashed();
    }

    public function title()
    {
        return $this->belongsTo(Title::class)->withTrashed();
    }

    public function booking()
    {
        return $this->belongsTo(Booking::class)->withTrashed();
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'creator_id')->withTrashed();
    }

    public function cinema()
    {
        return $this->belongsTo(CinemaSite::class, 'cinema_site_id')->withTrashed();
    }

    public function asperaAccessKey()
    {
        return $this->hasOne(AsperaAccessKey::class);
    }

    public function updateData($values)
    {
        if (isset($values['status'])) {
            $this->updateStatus($values['status']);
        }

        $this->fill($values);
        $this->save();
    }

    public function updateStatus($status)
    {

        if ($status === "completed") {
            $this->completeDelivery();
        }

        // We should loop through all the deliveries in the Booking here
        // And potentially update the Booking status if all are complete, etc.
        $booking = $this->booking;
        if ($booking) {
            $completeCount = 0;
            foreach ($booking->deliveries as $delivery) {
                if ($delivery->status === BookingStatus::Completed) {
                    $completeCount++;
                }
            }

            if ($completeCount === count($booking->deliveries)) {
                // we think the booking's deliveries are all completed, so we'll fire off a verify here.
                if (in_array($booking->overall_status, BookingStatus::getStatusesByGroup('in_progress'))) {
                    $booking->setOverallStatus(BookingStatus::Verify);
                }
            } else {
                // Otherwise we can store a Transmitting status...
                $booking->setOverallStatus(BookingStatus::Transmitting);
            }
        }
    }

    public function sendDownloadJob()
    {
        if (!isset($this->version) || !isset($this->version->s3_details)) {
            abort(422, "No storage exists!");
        }

        $key = $this->version->s3_details['root'];

        if (!isset($key)) {
            abort(422, "No storage exists!");
        }

        // We save it locally so when the e-booking comes back round trip, we have
        // a record to update and track. This can also be used in the future
        // to send download jobs directly to the CSX/CMMS directly if needed.

        if (!$this->cinema->primaryMediaManager) {
            abort(422, "No primary media manager set for {$this->cinema->name} ");
        }

        // make sure this version's UUID doesn't already exist in the destination's library
        if (isset($this->version->multi_cpl_uuids) && is_array($this->version->multi_cpl_uuids) && count($this->version->multi_cpl_uuids) === 1) {
            $library = $this->cinema->primaryMediaManager->media_library;
            if (count($library) > 0) {
                foreach ($library as $libraryKey => $cplObject) {
                    if (isset($cplObject['id']) && $cplObject['id'] === $this->version->cpl_uuid) {
                        // mark this as completed
                        logger("Duplicate content detected on MM {$this->cinema->primaryMediaManager->id} for uuid {$this->version->cpl_uuid}, auto-completing.");
                        $this->updateStatus(BookingStatus::Completed->value);
                        return;
                    }
                }
            }
        }

        // we'll allow new jobs to be created on any other status...
        $existingJob = MediaManagerJob::whereIn('status',
            ['pending', 'transmitting', 'retrying', 'triggered', 'running'])
            ->where('delivery_id', $this->id)
            ->where('cinema_site_id', $this->cinema_site_id)
            ->where('media_manager_id', $this->cinema->primaryMediaManager->id)
            ->first();
        if ($existingJob) {
            abort(422, "An existing job is {$existingJob->status} for this delivery.");
        }

        $jobData = [
            'cinema_site_id' => $this->cinema_site_id,
            'media_manager_id' => $this->cinema->primaryMediaManager->id,
            'delivery_id' => $this->id,
            'update_url' => config('app.url') . '/api/e-deliveries/' . $this->id,
            'name' => 'Download ' . $this->version->version_name . ' to Downloads Directory',
            'type' => 'aspera_download',
            'code' => 'external_download',
            'source_cpl_uuid' => $this->version->cpl_uuid,
            'file_name' => $this->version->version_name, // folder name
            'title' => $this->version->version_name,
            'size' => $this->version->size,
            'key' => $key,
            // when is_fazzt is true, it will not be ingested. It will sit in downloads for Fazzt to process in as FPK.
            'is_fazzt' => $this->release->is_fazzt ?? false,
            // 'download_key' => $credentials->id,
            // 'download_secret' => $credentials->secret,
            'progress' => 0,
        ];

        // get the url from the media manager table/device+port
        /** @var MediaManagerJob $job */
        $job = MediaManagerJob::create($jobData);

        return $job->sendJob();
    }

    public function createDownloadCredentials($path)
    {
        $accessKey = $this->asperaAccessKey()->create(['path' => $path]);
        return $accessKey->createKeyInAspera($this->version->bucket);
    }

    public function completeDelivery()
    {
        // Set some sane defaults for complete deliveries...
        $this->status = BookingStatus::Completed;
        $this->speed_in_mbps = 0;
        $this->progress = 100;
        $this->details = null;
        $this->save();

        if (!$this->is_electronic) {
            return;
        }

        if (isset($this->release->is_fazzt) && $this->release->is_fazzt) {
            // trigger fazzt's own ingest job on the version.
            $this->version->ingestIntoFazzt();
        }

        // Delete the Aspera key if this is an e-delivery.
        if ($asperaAccessKey = $this->asperaAccessKey) {
            $asperaAccessKey->deleteKeyFromAspera();
        }
    }

}
