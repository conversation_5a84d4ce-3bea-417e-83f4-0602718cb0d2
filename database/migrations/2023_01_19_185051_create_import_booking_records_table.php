<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('import_booking_records', function (Blueprint $table) {
            $table->id();

            $table->foreignId('import_booking_id');
            // original csv row as JSON format for visual/validation purposes
            $table->json('original_record');
            $table->integer('original_index');

            $table->boolean('matched')->default(false);
            $table->foreignId('booking_id')->nullable();
            $table->foreignId('cinema_site_id')->nullable();

            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('import_booking_records');
    }
};
