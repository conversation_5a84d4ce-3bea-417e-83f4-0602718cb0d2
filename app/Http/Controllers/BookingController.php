<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreBookingRequest;
use App\Http\Requests\{UpdateBookingRequest, UpdateElectronicBookingRequest};
use App\Models\{Booking, CinemaSite, Delivery, Title, Version, Release};
use App\Models\Enum\BookingStatus;
use Illuminate\Http\Response;
use Carbon\Carbon;
use App\Models\FazztTransfer;
use Exception;

class BookingController extends Controller
{
    public function __construct()
    {
        $this->authorizeResource(Booking::class);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if (auth()->user()->isAdmin() || auth()->user()->can('view-all-bookings')) {
            return $this->executeQuery(Booking::query());
        }
        return $this->executeQuery(auth()->user()->organization->bookings());
    }

    public function indexByOrganization()
    {
        return $this->executeQuery(auth()->user()->organization->bookings());
    }

    public function indexByTitle(Title $title)
    {
        return $this->executeQuery($title->bookings());
    }

    public function indexByCinemaSite(CinemaSite $cinemaSite)
    {
        return $this->executeQuery($cinemaSite->bookings());
    }

    private function executeQuery($query)
    {


        $search = request()->get('search');
        $status = request()->get('status');
        $studio = request()->get('studio');
        $circuit = request()->get('circuit');
        $packageId = request()->get('package_id');

        $sortField = request()->get('sortBy', 'created_at');

        return $query->select([
            'bookings.id',
            'title_id',
            'package_id',
            'bookings.organization_id',
            'creator_id',
            'cinema_site_id',
            'is_electronic',
            'deliver_at',
            'release_date',
            'overall_status',
            'transfer_counts',
            'bookings.created_at',
        ])
            ->with([
                'title:id,friendly_title,release_date,status',
                'cinema:id,name,disney_site_id,tcn,circuit,address',
                'creator:id,name',
                'organization:id,name',
                'transactions:id,booking_id,transaction_data',
                'release:id,package_name',
                //'deliveries:id,booking_id,version_id,status,progress,speed_in_mbps',
            ])
            ->when($status, function ($q, $status) {
                $filterStatuses = explode(',', $status);
                $statuses = [];
                if (is_array($filterStatuses)) {
                    foreach ($filterStatuses as $fs) {
                        $statuses = array_merge($statuses, BookingStatus::getStatusesByGroup($fs));
                    }
                }
                if (count($statuses) > 0) {
                    $q->whereIn('overall_status', $statuses);
                }
            })->when($studio, function ($q, $studio) {
                $q->whereIn('organization_id', explode(',', $studio));
            })->when($circuit, function ($q, $circuit) {
                $q->whereHas('cinema', function ($q) use ($circuit) {
                    $q->whereIn('circuit', explode(',', $circuit));
                });
            })->when($packageId, function ($q, $packageId) {
                $q->where('package_id', $packageId);
            })->when($search, function ($q) use ($search) {
                $q->where(function ($q) use ($search) {
                    $q->whereHas('title', function ($q) use ($search) {
                        $q->where('friendly_title', 'like', "%{$search}%");
                    });
                    $q->orWhereHas('release', function ($q) use ($search) {
                        $q->where('package_name', 'like', "%{$search}%");
                    });
                    $q->orWhereHas('cinema', function ($q) use ($search) {
                        $q->where('name', 'like', "%{$search}%");
                    });
                    $q->orWhereHas('cinema', function ($q) use ($search) {
                        $q->where('tcn', "{$search}");
                    });
                });
            })
            ->when(request()->get('statusPriority') === 'on', function ($q) {
                $q->orderBy('status_priority', 'ASC');
            })
            ->where('is_duplicate', false)
            ->when((in_array($sortField, ['site', 'circuit'])), function ($q) use ($sortField) {
                $q->leftJoin('cinema_sites', 'bookings.cinema_site_id', '=', 'cinema_sites.id');
                $sortField = ($sortField === 'site' ? 'cinema_sites.name' : 'cinema_sites.circuit');
                $q->orderBy($sortField, request()->get('sortDirection', 'DESC'));
            })
            ->when((!in_array($sortField, ['site', 'circuit'])), function ($q) use ($sortField) {
                $q->orderBy($sortField, request()->get('sortDirection', 'DESC'));
            })
            ->paginate(request()->get('take', 15));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Http\Requests\StoreBookingRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreBookingRequest $request)
    {
        $values = $request->validated();
        $package = Release::find($values['package_id']);
        $values['creator_id'] = auth()->user()->id;
        $values['organization_id'] = $package->title->organization_id; // auth()->user()->organization_id; // pull from title
        $values['title_id'] = $package->title_id;
        $values['package_id'] = $package->id;
        /** @var Booking $newBooking */
        $newBooking = Booking::create($values);

        $newBooking->setOverallStatus(
            BookingStatus::Pending,
            auth()->user()->id
        );

        // create a delivery for each package contents
        /** @var Version $version */
        foreach ($package->content as $version) {
            $newBooking->deliveries()->create([
                'version_id' => $version->id,
                'organization_id' => $package->title->organization_id, //auth()->user()->organization_id,
                'cinema_site_id' => $values['cinema_site_id'],
                'package_id' => $package->id,
                'title_id' => $package->title_id,
                'is_electronic' => request()->is_electronic,
            ]);
        }

        return $this->show($newBooking);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Booking  $booking
     * @return \Illuminate\Http\Response
     */
    public function show(Booking $booking)
    {
        $booking->load([
            'title:id,friendly_title,status',
            'cinema:id,name,circuit',
            'release:id,package_name',
            'organization:id,name',
            'transactions:id,booking_id,transaction_data,created_at',
            'statuses:id,status,booking_id,created_at',
        ])->makeVisible(['transactions', 'statuses']);
        return response(['data' => $booking]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \App\Http\Requests\UpdateBookingRequest  $request
     * @param  \App\Models\Booking  $booking
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateBookingRequest $request, Booking $booking)
    {
        $values = $request->validated();
        if (isset($values['is_electronic']) && $values['is_electronic']) {
            $this->calculateDeliveryTime($booking, $values['is_electronic'] === -1);
        }

        $isElectronic = $booking->is_electronic;
        $booking->update($values);

        if (isset($values['status'])) {
            $booking->setOverallStatus(
                BookingStatus::tryFrom($values['status']),
                auth()->user()->id ?? null
            );
        }

        // if is_electronic was just changed to e-delivery, trigger e-deliveries. Ignore satellite (-1)
        if ($booking->wasChanged('is_electronic') && $booking->is_electronic === 1) {

            if ($booking->deliveries()->count() > 0) {
                // fire it up.
                $booking->deliveries()->update(['is_electronic' => 1]);
                try {
                    $this->sendDeliveries($booking);
                } catch (\Exception $e) {
                    $booking->is_electronic = 0; // failed to send e-delivery, revert to hard drive.
                    $booking->save();
                    throw $e;
                }
            }
        }

        return $this->show($booking);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Booking  $booking
     * @return \Illuminate\Http\Response
     */
    public function destroy(Booking $booking)
    {
        $booking->delete();
        return response(['data' => $booking]);
    }

    /**
     * Bulk update one or many Bookings with a new status. This will use the same update validation with a
     * check for booking_ids as array, we'll still have to do a check to make sure the user can update each ID
     * passed in though.
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\Routing\ResponseFactory|Response
     */
    public function bulkUpdateStatuses(UpdateBookingRequest $request)
    {
        // a simple validation for array & a valid status.
        $values = $request->validated();

        $ids = $values['booking_ids'];
        $bookings = Booking::whereIn('id', $ids)->get();

        // this should be safe via validation. if the value is actually invalid it will throw an exception.
        $status = BookingStatus::from($values['status']);

        $updatedIds = [];
        $rejectedIds = [];
        /** @var Booking $booking */
        foreach ($bookings as $booking) {
            if (auth()->user()->can('update', $booking)) {
                $booking->setOverallStatus(
                    $status,
                    auth()->user()->id,
                );
                $updatedIds[] = $booking->id;
            }
        }

        $rejectedIds = array_diff($ids, $updatedIds);

        return response([
            'updated_ids' => $updatedIds,
            'rejected_ids' => $rejectedIds,
        ]);
    }

    public function startDeliveries(Booking $booking)
    {
        $this->sendDeliveries($booking);
        return response()->json(['success' => true]);
    }

    private function sendDeliveries(Booking $booking)
    {
        /** @var Delivery $delivery */
        if ($booking->is_electronic === 1) {
            foreach ($booking->deliveries as $delivery) {
                if ($delivery->status == BookingStatus::Pending) {
                    $delivery->sendDownloadJob();
                }
            }
        }
    }

    private function calculateDeliveryTime(Booking $booking, $isSatellite)
    {
        $deliverAt = Carbon::parse($booking->deliver_at);
        $sizeInMegabits = ($booking->release->size * 8) / 1000000;

        if ($isSatellite) {
            $lastTransfer = FazztTransfer::where('state', 'PENDING')
                ->orderByDesc('priority')
                ->first();

            $lastTransferEndTime = Carbon::parse($lastTransfer->start_at ?? now())
                ->addMinutes($lastTransfer->estimated_minutes ?? 0);

            $transferTime = round($sizeInMegabits / 40); // Assume 40 Mbps

            if ($lastTransferEndTime->addSeconds($transferTime)->greaterThan($deliverAt)) {
                abort(422, 'Satellite transfer time exceeds delivery deadline.');
            }
        } else {
            $speed = isset($booking->cinema->typical_speed->download_speed_in_mbps)
                ? $booking->cinema->typical_speed->download_speed_in_mbps
                : 40;
            $transferTime = round($sizeInMegabits / $speed);
            $timeRemaining = $deliverAt->diffInSeconds(Carbon::now());
            if ($transferTime > $timeRemaining) {
                abort(422, 'E-Delivery transfer time exceeds delivery deadline.');
            }
        }
    }
}
