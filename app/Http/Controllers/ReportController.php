<?php

namespace App\Http\Controllers;

use App\Models\AppleTelevision;
use App\Models\Report;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Schema;
use Carbon\Carbon;
use App\Jobs\ReportGenerator;
use Storage;


class ReportController extends Controller {

    public function reportsList() {
        // only going to show last 50 in the UI for now.
        return ['data' => Report::latest()->paginate(100)];
    }

    public function generateReport() {
        // verify the generator has the required data
        // submit the job
        // wait it out.
        $report = Report::create([
            'user_id' => auth()->user()->id,
            'type' => request()->post('type'),
            'parameters' => request()->post()
        ]);

        ReportGenerator::dispatch($report->id);
        return ['data' => $report];
    }

    public function downloadReport(Report $report) {
        // return s3 signed URL
        if (Storage::disk('s3')->exists($report->s3_path)) {
            return ['url' => Storage::disk('s3')->temporaryUrl($report->s3_path, now()->addHour())];
        }
        return false;
    }

    public function deleteReport(Report $report) {
        if (Storage::disk('s3')->exists($report->s3_path)) {
            Storage::disk('s3')->delete($report->s3_path);
        }
        $report->delete();
    }
}
