<?php

namespace App\Models;

use Aws\S3\Transfer;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FazztContent extends Model
{
    protected $table = 'fazzt_content';

    protected $casts = [
        'error' => 'json'
    ];

    protected $fillable = [
        'asset_uuid', 'package_file', 'package_size', 'status', 'message', 'error'
    ];
    use HasFactory;

    public function version()
    {
        // a 'version' (content) dcp that gets uploaded to FAZZT server will have this relation.
        // this content will point to the ingested FPK file(s) for transmission.
        return $this->belongsTo(Version::class, 'asset_uuid', 'asset_uuid');
    }

    public function transfer() {
        return $this->belongsTo(Transfer::class, 'asset_uuid', 'asset_uuid');
    }
}
