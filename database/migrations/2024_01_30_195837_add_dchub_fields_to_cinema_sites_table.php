<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('cinema_sites', function (Blueprint $table) {
            $table->string('dchub_cinema_id')->after('paramount_theatre_id')->nullable();
        });
        Schema::table('titles', function (Blueprint $table) {
            $table->string('dchub_title_id')->after('paramount_film_id')->nullable();
        });
        Schema::table('versions', function (Blueprint $table) {
            $table->string('dchub_version_id')->after('cpl_uuid')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('cinema_sites', function (Blueprint $table) {
            $table->dropColumn('dchub_cinema_id');
        });
        Schema::table('titles', function (Blueprint $table) {
            $table->dropColumn('dchub_title_id');
        });
        Schema::table('versions', function (Blueprint $table) {
            $table->dropColumn('dchub_version_id');
        });
    }
};
