<?php

namespace Tests\Feature\Http\Controllers;

use App\Models\CinemaSite;
use App\Models\Organization;
use App\Models\User;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class DeviceControllerTest extends TestCase
{
    use RefreshDatabase;

    protected const SERIAL_NUMBER = '123456789';

    protected const CPLS_STRING = '{
        "6e1bb85b-9767-4238-83be-312cb48534ec":{
            "bitrate-avg":null,
            "last-modified":1545227233,
            "bitrate-max":null,
            "size-si":"283.6 kB",
            "kind":"Test",
            "id":"6e1bb85b-9767-4238-83be-312cb48534ec",
            "is-corrupt":0,
            "error-str":"",
            "is-stereo":0,
            "is-smpte":0,
            "is-encrypted":0,
            "duration":1,
            "asset-ids":[
                "e6f60beb-4144-4c35-ae85-ace530aa190a",
                "6e1bb85b-9767-4238-83be-312cb48534ec"
            ],
            "size-bytes":283566,
            "is-complete":1,
            "keyids":[

            ],
            "title":"2.39-FIELD-CHART_TST_S_MOS_2K_FKI_20100608_FKI",
            "edit-rate":"24/1"
        },
        "bd9393dc-2bcc-4a36-bbc3-d3d581b84621":{
            "bitrate-avg":null,
            "last-modified":1545227233,
            "bitrate-max":null,
            "size-si":"56.2 MB",
            "kind":"Rating",
            "id":"bd9393dc-2bcc-4a36-bbc3-d3d581b84621",
            "is-corrupt":0,
            "error-str":"",
            "is-stereo":0,
            "is-smpte":0,
            "is-encrypted":0,
            "duration":146,
            "asset-ids":[
                "00ca9238-25a4-4e5f-a6e1-fd97eeac6b50",
                "bd9393dc-2bcc-4a36-bbc3-d3d581b84621",
                "ac5111ce-a42e-4a12-af1e-f7834bd33c07"
            ],
            "size-bytes":56234910,
            "is-complete":1,
            "keyids":[

            ],
            "title":"TULLYfocus_RTG_F_EN-XX_US-R_51_2K_FF_20170928_FKI",
            "edit-rate":"24/1"
        },
        "85a8f6c4-0f08-4fab-b192-100f2c65b101":{
            "bitrate-avg":null,
            "last-modified":1545180112,
            "bitrate-max":null,
            "size-si":"9.1 MB",
            "kind":"Test",
            "id":"85a8f6c4-0f08-4fab-b192-100f2c65b101",
            "is-corrupt":0,
            "error-str":"",
            "is-stereo":1,
            "is-smpte":0,
            "is-encrypted":0,
            "duration":240,
            "asset-ids":[
                "7aa0c89d-75d4-4d4b-8ff4-75d928f694fd",
                "85a8f6c4-0f08-4fab-b192-100f2c65b101",
                "e729c6eb-514e-4edc-8424-889ac11f3c4a"
            ],
            "size-bytes":9144073,
            "is-complete":1,
            "keyids":[

            ],
            "title":"FS Peak White 3D",
            "edit-rate":"24/1"
        },
        "3fe8bde6-8fbb-416d-9b4a-5b40b6126f7d":{
            "bitrate-avg":null,
            "last-modified":1544831030,
            "bitrate-max":null,
            "size-si":"2.4 GB",
            "kind":"Trailer",
            "id":"3fe8bde6-8fbb-416d-9b4a-5b40b6126f7d",
            "is-corrupt":0,
            "error-str":"",
            "is-stereo":0,
            "is-smpte":1,
            "is-encrypted":0,
            "duration":2205,
            "asset-ids":[
                "3fe8bde6-8fbb-416d-9b4a-5b40b6126f7d",
                "eb3b31b1-97b2-417b-8ea6-d23ea591257b",
                "67a487ab-c28f-415a-86fc-aeb7fed15d9e",
                "16ba7f70-058d-403e-92f7-6e1aa9af8e08"
            ],
            "size-bytes":2361179999,
            "is-complete":1,
            "keyids":[

            ],
            "title":"GRAVITY_TLR-1A-2D_F_EN-XX_US-GB_51-ATMOS_2K_WR_20130529_DLB",
            "edit-rate":"24/1"
        }
    }';

    protected const DRIVES_STRING = '[
        {
           "location_index":0,
           "location_name":"Left Bay",
           "model":"Hitachi_HUA722010CLA330",
           "vendor":"ATA",
           "udev_path":"/devices/pci0000:00/0000:00:17.0/ata2/host0/target1:0:0/1:0:0:0/block/sdb",
           "device":"/dev/sda",
           "devfile":"sda",
           "size":234872834723847,
           "partition_table_type":"dos",
           "formatting":0,
           "transfer_locked":0,
           "ftp_export_in_progress":0,
           "partitions":[
              {
                 "device":"/dev/sda1",
                 "is_root":0,
                 "size":"3434323234234",
                 "fs_type":"ext3",
                 "fs_id":"some_id",
                 "fs_inode_size":128,
                 "unreadable_file_count":0,
                 "non_dcp_files":{
                    "count":0,
                    "size":0
                 },
                 "mount_point":{
                    "ro":1,
                    "free":992112665280,
                    "dir":"/mnt/drive-sda1sda1",
                    "size":"992188112896"
                 },
                 "dcps":[
                    {
                       "size_b":1068563870,
                       "dci":0,
                       "valid_sizes":1,
                       "directory":"/mnt/drive-sda1sda1/Movie_TLR_FR_20091415",
                       "issue_date":"2018-11-13T10:20:12-08:00",
                       "text":"The Annotation Text of this DCP",
                       "url":"*****************************/drive-f39536ff",
                       "id":"65a21f86-3d18-4ae0-b9d0-d152f417b1a5",
                       "validating":0,
                       "validation_error":null,
                       "valid":null,
                       "cpls":[
                          {
                             "uuid":"a2a2a2a2-ffff-489f-9fae-60cca0beeec3",
                             "title_text":"Movie_TLR_FR_20091415",
                             "issue_date":"2009-05-07T17:05:31-07:00",
                             "kind":"trailer",
                             "dci":0,
                             "creator":"Bob",
                             "issuer":"SixSmith Technologies.",
                             "duration":1293,
                             "width":2048,
                             "height":1080,
                             "size_b":1068563870,
                             "reel_count":6,
                             "key_count":10,
                             "edit_rate":"24/1",
                             "frame_rate":"24/1",
                             "is_encrypted":1,
                             "is_partial":0,
                             "is_3d":0,
                             "is_signed":0,
                             "closed_caption_languages":[

                             ],
                             "closed_subtitle_languages":[

                             ],
                             "caption_language":null,
                             "sound_language":null,
                             "subtitle_language":null,
                             "aspect_ratio":185
                          }
                       ]
                    },
                    {
                       "size_b":1068563870,
                       "dci":0,
                       "valid_sizes":1,
                       "directory":"/mnt/drive-sda1sda1/Movie_TLR_EN_20091415",
                       "issue_date":"2018-11-13T10:20:12-08:00",
                       "text":"The Annotation Text of this DCP",
                       "url":"*****************************/drive-f39536f3",
                       "id":"65a21f86-3d18-4ae0-b9d0-d152f417b1a5",
                       "validating":0,
                       "validation_error":null,
                       "valid":null,
                       "cpls":[
                          {
                             "uuid":"a1a1a1a1-ea4d-489f-9fae-60cca0beeec3",
                             "title_text":"Movie_TLR_EN_20091415",
                             "issue_date":"2009-05-07T17:05:31-07:00",
                             "kind":"trailer",
                             "dci":0,
                             "creator":"Bob",
                             "issuer":"SixSmith Technologies.",
                             "duration":1293,
                             "width":2048,
                             "height":1080,
                             "size_b":1068563870,
                             "reel_count":6,
                             "key_count":10,
                             "edit_rate":"24/1",
                             "frame_rate":"24/1",
                             "is_encrypted":1,
                             "is_partial":0,
                             "is_3d":0,
                             "is_signed":0,
                             "closed_caption_languages":[

                             ],
                             "closed_subtitle_languages":[

                             ],
                             "caption_language":null,
                             "sound_language":null,
                             "subtitle_language":null,
                             "aspect_ratio":185
                          },
                          {
                             "uuid":"a3a3a3a3-ffff-489f-9fae-60cca0beeec3",
                             "title_text":"Movie_TLR_FR_20091415",
                             "issue_date":"2009-05-07T17:05:31-07:00",
                             "kind":"trailer",
                             "dci":0,
                             "creator":"Bob",
                             "issuer":"SixSmith Technologies.",
                             "duration":1293,
                             "width":2048,
                             "height":1080,
                             "size_b":1068563870,
                             "reel_count":6,
                             "key_count":10,
                             "edit_rate":"24/1",
                             "frame_rate":"24/1",
                             "is_encrypted":1,
                             "is_partial":0,
                             "is_3d":0,
                             "is_signed":0,
                             "closed_caption_languages":[

                             ],
                             "closed_subtitle_languages":[

                             ],
                             "caption_language":null,
                             "sound_language":null,
                             "subtitle_language":null,
                             "aspect_ratio":185
                          }
                       ]
                    }
                 ]
              }
           ]
        },
        {
           "location_index":1,
           "location_name":"Right Bay",
           "model":"Hitachi_HUA722010CLA330",
           "vendor":"ATA",
           "udev_path":"/devices/pci0000:00/0000:00:17.0/ata2/host0/target1:0:0/1:0:0:0/block/sdb",
           "device":"/dev/sdb",
           "devfile":"sdb",
           "size":234872834723847,
           "partition_table_type":"dos",
           "formatting":0,
           "transfer_locked":0,
           "ftp_export_in_progress":0,
           "partitions":[
              {
                 "device":"/dev/sdb1",
                 "is_root":0,
                 "size":"3434323234234",
                 "fs_type":"ext3",
                 "fs_id":"some_id",
                 "fs_inode_size":128,
                 "unreadable_file_count":0,
                 "non_dcp_files":{
                    "count":0,
                    "size":0
                 },
                 "mount_point":{
                    "ro":1,
                    "free":992112665280,
                    "dir":"/mnt/drive-sdb1sdb1",
                    "size":"992188112896"
                 },
                 "dcps":[
                    {
                       "size_b":1068563870,
                       "dci":0,
                       "valid_sizes":1,
                       "directory":"/mnt/drive-sdb1sdb1/Movie_ITL_FR_20091415",
                       "issue_date":"2018-11-13T10:20:12-08:00",
                       "text":"The Annotation Text of this DCP",
                       "url":"*****************************/drive-f39536f3",
                       "id":"65a21f86-3d18-4ae0-b9d0-d152f417b1a5",
                       "validating":0,
                       "validation_error":null,
                       "valid":null,
                       "cpls":[
                          {
                             "uuid":"b1b1b1b1-ea4d-489f-9fae-60cca0beeec3",
                             "title_text":"Movie_ITL_FR_20091415",
                             "issue_date":"2009-05-07T17:05:31-07:00",
                             "kind":"trailer",
                             "dci":0,
                             "creator":"Bob",
                             "issuer":"SixSmith Technologies.",
                             "duration":1293,
                             "width":2048,
                             "height":1080,
                             "size_b":1068563870,
                             "reel_count":6,
                             "key_count":10,
                             "edit_rate":"24/1",
                             "frame_rate":"24/1",
                             "is_encrypted":1,
                             "is_partial":0,
                             "is_3d":0,
                             "is_signed":0,
                             "closed_caption_languages":[

                             ],
                             "closed_subtitle_languages":[

                             ],
                             "caption_language":null,
                             "sound_language":null,
                             "subtitle_language":null,
                             "aspect_ratio":185
                          }
                       ]
                    }
                 ]
              }
           ]
        }
    ]';

    public function test_site_formats_media_library()
    {
        $serial = uniqid(self::SERIAL_NUMBER);
        $admin = User::factory()->create()->assignRole('admin');
        $newCinema = CinemaSite::factory()
            ->withOrganization(Organization::factory()->withOwner($admin)->create())
            ->create([
                'csx_serial_number' => $serial,
            ]);

        $newCinema->primaryMediaManager()->create([
            'name' => 'name',
            'cinema_site_id' => $newCinema->id,
            'is_primary' => 1,
            'organization_id' => $newCinema->organization_id,
            'serial_number' => $serial,
            'media_library' => json_decode(self::CPLS_STRING),
        ]);

        $firstCplName = array_values(json_decode(self::CPLS_STRING, true))[0]['title'];

        $newCinema->load(['primaryMediaManager']);

        $array = $newCinema->primaryMediaManager->media_library;
        $first = array_shift($array);
        $this->assertSame(
            $firstCplName,
            $first['title']
        );
    }

    public function test_site_formats_drives()
    {
        $serial = uniqid(self::SERIAL_NUMBER);
        $admin = User::factory()->create()->assignRole('admin');
        $newCinema = CinemaSite::factory()
            ->withOrganization(Organization::factory()->withOwner($admin)->create())
            ->create([
                'csx_serial_number' => $serial,
            ]);
        $newCinema->primaryMediaManager()->create([
            'name' => 'name',
            'cinema_site_id' => $newCinema->id,
            'is_primary' => 1,
            'organization_id' => $newCinema->organization_id,
            'serial_number' => $serial,
            'media_drives' => json_decode(self::DRIVES_STRING)
        ]);

        //                ,
        $drives = json_decode(self::DRIVES_STRING);
        $newCinema->load(['primaryMediaManager']);

        $array = $newCinema->primaryMediaManager->media_drives;
        $firstDrive = array_shift($array);

        $this->assertSame(
            $drives[0]->location_name,
            $firstDrive['location_name']
        );

        $firstCpl = array_values($array[0]['partitions'][0]['dcps'][0]['cpls'])[0];
        $this->assertSame(1, $firstCpl['is_encrypted']);
    }
}
