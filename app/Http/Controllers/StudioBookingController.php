<?php

namespace App\Http\Controllers;

use App\Events\BookingTransactionReceived;
use App\Models\Booking;
use App\Models\StudioTransaction;
use Illuminate\Http\Request;
use Str;

class StudioBookingController extends Controller
{

    public function createTransaction(Request $request, $studio)
    {
        // we don't really need $studio, but we want it in the URL for logging
        $valid = ['studio', 'admin'];
        if (!in_array(auth()->user()->organization->type, $valid)) {
            return response(['error - invalid org type']);
        }

        $createBooking = true;
        $transactionData = $request->except(['password', 'username']);

        switch ($studio) {
            case 'disney':
                $externalOrderId = $request->contentOrderId;
                $startDate = $request->startDate;
                $deliveryDate = $request->deliveryDate;
                $isElectronic = true;
                break;
            case 'paramount':
                $createBooking = false;
                $transactionData = $this->parseXmlFromBody();
                break;
            case 'sony':
                $transactionData = $rawTransactionData['BookingRequest']['BookingPayload'] ?? null;
//                if (! $transactionData) {
//                    abort(422, "Payload is missing BookingRequest->BookingPayload");
//                }
                $externalOrderId = $transactionData['booking_id'];
                $deliveryDate = $transactionData['ship_date'];
                $startDate = $transactionData['play_start_date'];
                $isElectronic = true;
                $createBooking = $transactionData['shipment_indicator'] === 'Y';
                break;
            default:
                $externalOrderId = $request->external_order_id ?? Str::uuid();
                $deliveryDate = now()->addDay();
                $startDate = now();
                $isElectronic = true;
        }

        if ($createBooking) {
            $booking = Booking::firstOrCreate([
                'organization_id' => auth()->user()->organization_id,
                'creator_id' => auth()->user()->id,
                'external_order_id' => $externalOrderId,
            ], [
                'release_date' => $startDate,
                'deliver_at' => $deliveryDate,
                'is_electronic' => $isElectronic,
            ]);
        }

        $transaction = StudioTransaction::create([
            'user_id' => auth()->user()->id,
            'booking_id' => $booking->id ?? null,
            'organization_id' => auth()->user()->organization_id,
            'transaction_data' => $transactionData,
        ]);


        event(new BookingTransactionReceived($booking->id ?? null, $transaction->id, $studio));

        return response(['status' => 'ok', 'transaction' => $transaction, 'booking' => $booking ?? null]);
    }

    public function retryTransaction($studio, StudioTransaction $transaction) {

        if ($transaction->processed) {
            abort(422, "Transaction has already been processed.");
        }

        event(new BookingTransactionReceived(null, $transaction->id, $studio));
        return response(['status' => 'ok']);

    }

    private function parseXmlFromBody()
    {
        // eat up the XML posted from the body and go.
        if (request()->getContentTypeFormat() === 'xml') {
            try {
                return simplexml_load_string(request()->getContent());
            }
            catch (\Exception $e) {
                return ['error' => true, 'message' => $e->getMessage()];
            }
        }
        else {
            return [
                'error' => true,
                'message' => 'XML content type not provided. Please use `Content-Type: application/xml` and post the XML file in the body.',
            ];
        }

    }
}
