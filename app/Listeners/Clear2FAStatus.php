<?php

namespace App\Listeners;

use Illuminate\Auth\Events\Logout;
use Illuminate\Contracts\Queue\ShouldQueue;

class Clear2FAStatus implements ShouldQueue
{
    /**
     * Handle the event.
     *
     * @param \Illuminate\Auth\Events\Logout $event
     * @return void
     */
    public function handle(Logout $event)
    {
        logger($event->user);
        $event->user->sms_2fa_verified_at = null;
        $event->user->save();

        activity()->causedBy($event->user)->log('Signed Out');
    }
}
