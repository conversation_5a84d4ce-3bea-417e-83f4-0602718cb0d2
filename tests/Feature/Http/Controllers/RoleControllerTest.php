<?php

namespace Tests\Feature\Http\Controllers;

use App\Models\Role;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class RoleControllerTest extends TestCase
{
    public function test_super_admin_can_see_single_role() {
        $admin = User::factory()->create()->assignRole('super-admin');
        $role = Role::create(['name' => uniqid('test-role')]);
        $response = $this->actingAs($admin)->getJson(route('roles.show', $role));
        $response->assertSuccessful();
    }

    public function test_admin_cannot_see_single_role() {
        $admin = User::factory()->create()->assignRole('admin');
        $role = Role::create(['name' => uniqid('test-role')]);
        $response = $this->actingAs($admin)->getJson(route('roles.show', $role));
        $response->assertForbidden();
    }

    public function test_admin_can_see_roles()
    {
        $admin = User::factory()->create()->assignRole('admin');
        $response = $this->actingAs($admin)->getJson(route('roles.index'));
        $response->assertSuccessful();
    }

    public function test_super_admin_cannot_create_role()
    {
        $admin = User::factory()->create()->assignRole('admin');
        $response = $this->actingAs($admin)->postJson(route('roles.store'), ['name' => uniqid('role')]);
        $response->assertForbidden();
    }

    public function test_super_admin_can_create_role()
    {
        $admin = User::factory()->create()->assignRole('super-admin');
        $response = $this->actingAs($admin)->postJson(route('roles.store'), ['name' => uniqid('role')]);
        $response->assertSuccessful();
    }

    public function test_admin_cannot_edit_role()
    {
        $admin = User::factory()->create()->assignRole('admin');
        $role = Role::create(['name' => uniqid('test-role')]);
        $response = $this->actingAs($admin)->putJson(route('roles.update', $role), ['name' => uniqid('role')]);
        $response->assertForbidden();
    }

    public function test_super_admin_can_edit_role()
    {
        $admin = User::factory()->create()->assignRole('super-admin');
        $role = Role::create(['name' => uniqid('test-role')]);
        $response = $this->actingAs($admin)->putJson(route('roles.update', $role), ['name' => uniqid('role')]);
        $response->assertSuccessful();
    }

    public function test_user_cannot_see_roles()
    {
        $user = User::factory()->create();
        $response = $this->actingAs($user)->getJson(route('roles.index'));
        $response->assertForbidden();
    }

    public function test_user_cannot_create_role()
    {
        $user = User::factory()->create()->assignRole('admin');
        $response = $this->actingAs($user)->postJson(route('roles.store'), ['name' => uniqid('role')]);
        $response->assertForbidden();
    }

    public function test_user_cannot_edit_role()
    {
        $user = User::factory()->create();
        $role = Role::create(['name' => uniqid('test-role')]);
        $response = $this->actingAs($user)->putJson(route('roles.update', $role), ['name' => uniqid('role')]);
        $response->assertForbidden();
    }
}
