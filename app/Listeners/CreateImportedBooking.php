<?php

namespace App\Listeners;

use App\Events\CreateImportBookings;
use App\Jobs\SyncPackageToDeliveries;
use App\Models\Booking;
use App\Models\Enum\BookingStatus;
use App\Models\ImportBooking;
use App\Models\ImportBookingRecord;
use App\Models\User;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;

class CreateImportedBooking implements ShouldQueue
{
    /**
     * @throws Exception
     */
    public function handle(CreateImportBookings $event)
    {

        // find the import booking record from event->importBookingId
        logger("handling the imported rows");
        $importBooking = ImportBooking::find($event->importBookingId);

        logger("processing " . $importBooking->origin_name);

        $user = User::where('id', $importBooking->user_id)->first();
        $organizationId = $importBooking->title->organization_id;

        // pull up each import_booking_record that doesn't have a booking attached
        $importBookingRecords = $importBooking->records()->whereNull('booking_id')->whereMatched(1)->get();

        // create a delivery for each version Ids. (todo: convert version picker to multi... )
        $versionIds = $importBooking->version_ids;

        $packageId = $importBooking->package_id;

        if (!is_array($versionIds)) {
            throw new Exception("Version IDs is not an array on Import Record {$importBooking->id}");
        }

        foreach ($importBookingRecords as $importBookingRecord) {
            logger("creating a booking site: " . $importBookingRecord->cinema_site_id);
            // create the booking
            $originalRow = $importBookingRecord->original_data ?? [];
            // create the actual booking
            $booking = Booking::create([
                'creator_id' => $importBooking->user_id,
                'organization_id' => $organizationId,
                'title_id' => $importBooking->title_id,
                'release_date' => $originalRow['engagement_start'] ?? $importBooking->engagement_start,
                'deliver_at' => $importBooking->release_date ?? now(),
                'package_id' => $importBooking->package_id,
                'cinema_site_id' => $importBookingRecord->cinema_site_id,
                'is_electronic' => true,
            ]);

            $booking->setOverallStatus(BookingStatus::Pending, $importBooking->user_id);
            $importBookingRecord->booking_id = $booking->id;
            $importBookingRecord->save();


            if (!$importBooking->package_id) {
                foreach ($versionIds as $versionId) {
                    logger("Created delivery for booking version: {$versionId['value']}");
                    $booking->deliveries()->create([
                        'organization_id' => $organizationId,
                        'version_id' => $versionId['value'],
                        'cinema_site_id' => $importBookingRecord->cinema_site_id,
                        'title_id' => $booking->title_id,
                        'is_electronic' => true,
                    ]);
                }
                // start the transfer immediately.
                if ($booking->cinema->primaryMediaManager) {
                    try {
                        $booking->deliveries->each->sendDownloadJob();
                    }
                    catch (Exception $e) {
                        logger()->warning("Failed to send Download Job during batch creations - {$e->getMessage()}");
                    }
                }
            }
        }
        if ($packageId) {
            SyncPackageToDeliveries::dispatch($packageId);
        }
    }
}
