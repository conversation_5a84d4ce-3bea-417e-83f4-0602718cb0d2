<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateAppleTelevisionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'hexnode_device_id' => ['sometimes', 'integer'],
            'organization_id' => ['sometimes', 'integer'],
            'cinema_site_id' => ['sometimes', 'integer'],
        ];
    }
}
