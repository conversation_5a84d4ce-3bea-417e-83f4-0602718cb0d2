<?php

namespace App\Console\Commands;

use App\Jobs\ReportGenerator;
use Illuminate\Console\Command;
use App\Models\Report;

class GenerateReport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'report:generate {id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Force generation of report $id';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $report = Report::findOrFail($this->argument('id'));

        ReportGenerator::dispatch($report->id);

        return Command::SUCCESS;
    }
}
