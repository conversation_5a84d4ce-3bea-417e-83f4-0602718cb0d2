<?php

namespace App\StudioHandlers\Sony;

use App\Actions\Dcp\VerifyDcp;
use App\Actions\Slack\Notify;
use App\Jobs\SyncPackageToDeliveries;
use App\Models\{Booking, CinemaSite, Enum\PackageTypes, Release, StudioTransaction, Version};
use App\Models\Enum\BookingStatus;
use Storage;
use Carbon\Carbon;

class InboundProcessor extends \App\StudioHandlers\InboundProcessor
{
    protected $site;

    protected $theatreMap;
    protected $contentMap;
    protected $messages;

    function __construct($bookingId = null, $transactionId = null, $rerun = false)
    {
        parent::__construct($bookingId, $transactionId, $rerun);
    }

    function handleInbound()
    {
        $start = now()->timestamp;

        // ideally transactionId has the parsed xml saved as transaction_data
        $data = $this->transaction->transaction_data;

        if (isset($data['record_type']) && $data['record_type'] === 'C') { // cancel is all we care about, 'U' or 'B' will be procesed the same.
            // check the booking status, if it's done we go cancelled after completed
            if ($this->booking->overall_status === BookingStatus::Completed) {
                $this->booking->setOverallStatus(BookingStatus::CancelledAfterCompletion);
            }
            else {// otherwise
                $this->booking->setOverallStatus(BookingStatus::Cancelled);
            }
        }
        else {
            // standard booking procedure.
            $this->booking->setOverallStatus(BookingStatus::Pending);


            // if they indicated no shipping, this is likely a sub-component of a larger booking package,
            if (isset($data['shipment_indicator']) && $data['shipment_indicator'] === 'N') {
                // we can probably ingore it.
                $this->transaction->processed = 1;
                $this->transaction->save();
                // ????
                return;
            }

            if ($this->booking->cinema_site_id === null) {
                // lookup cinema site by data['theatre_id'];
                $theatreId = $data['theatre_id'];
                $cinemaSite = CinemaSite::where('sony_site_id', $theatreId)->first();
                if (!$cinemaSite) {
                    $this->messages[] = "Could not locate Cinema Site for Sony Theatre ID: {$theatreId} / " .
                        "{$data['theatre_name']} / {$data['theatre_address1']}";
                }
                else {
                    $this->booking->cinema_site_id = $cinemaSite->id;
                    $this->booking->save();
                }
            }

            // if I already have a package studio_data == $data['release_id']; then we can just use it.
            $package = Release::where('studio_data', $data['release_id'])->first();

            // todo: an updated request may have a new UUID...
            // but if we already have a package, then we had real content before so this is unlikely.

            // find the content by uuid, then the parent package of it
            if (!$package) {
                $uuid = $data['uuid'];
                $version = Version::where('cpl_uuid', $uuid)->first();
                if (!$version) {
                    $this->messages[] = "Could not locate Content matching UUID: {$uuid} / {$data['dcnc_filename']} " .
                        "-- Sony may be using a Placceholder UUID or the content has not been uploaded.";
                }
                else {
                    $this->booking->title_id = $version->title_id;

                    if ($version->releases->count() === 0) {
                        // if not, let's create a package with this version (and it's co-uploaded files dcps)
                        if ($version->parent_version_id) {
                            $verisonIds = Version::where('parent_version_id',
                                $version->parent_version_id)->get()->pluck('id');
                        }
                        $package = $this->booking->release()->create([
                            'package_name' => $data['release_name'],
                            'type' => PackageTypes::TYPE_OTHER,
                            'title_id' => $version->title_id,
                            'organization_id' => $this->booking->organization_id,
                            'studio_data' => $data['release_id'], // store this here for faster lookups next time.
                        ]);
                        if ($versionIds) {
                            try {
                                $package->content()->sync($versionIds);
                            }
                            catch (\Exception $e) {
                                // dupes, can be ignored.
                            }
                        }
                    }
                    else {
                        // if this version has been manually added to a package, use it.
                        $package = $version->releases()->first();
                        $package->studio_data = $data['release_id']; // hopefully this is unique to packages?
                        $package->save();
                    }

                }
            }

            if ($package) {
                $this->booking->package_id = $package->id;
                $this->transaction->processed = 1;
                SyncPackageToDeliveries::dispatch($package->id);
            }
            else {
                $this->messages[] = 'There was an issue finding or creating a package for this booking request.';
            }
        }

        $this->transaction->save();
        $this->booking->save();

        // we should be good to go.
        $end = now()->timestamp;
        $seconds = $end - $start;

        // ping slack with whatever happened here.
        logger("[Sony] Procsessed transaction {$this->transaction->id} in $seconds seconds with " .
            count($this->messages) . " issues.");

        foreach ($this->messages as $message) {
            $messageBlocks[] = ['type' => 'mrkdwn', 'text' => $message];
        };

        try {
            $slack = (new Notify("[Sony] Processed Transaction ID {$this->transaction->id} in " .
                "$seconds seconds with " . count($this->messages) . " issues."));

            foreach ($messageBlocks as $message) {
                $slack->addTextSection($message);
            }
            $slack->addDivider()->send();
        }
        catch (\Exception $e) {
        }

    }
}
