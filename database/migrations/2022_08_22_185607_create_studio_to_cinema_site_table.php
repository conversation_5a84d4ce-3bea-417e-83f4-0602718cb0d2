<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('studio_to_cinema_sites', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organization_id'); // the org that originated this mapping.
            $table->foreignId('cinema_site_id'); // our site Ids
            $table->unsignedBigInteger('studio_site_id'); // whatever their Id happens to be.
            $table->json('studio_data'); // raw data from the studio for lookup/historic purposes
            $table->timestamps();

            $table->unique(['organization_id', 'cinema_site_id']); // an org shouldn't be able to map the same site twice.
        });

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('studio_to_cinema_sites');
    }
};
