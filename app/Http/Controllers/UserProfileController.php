<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreUserRequest;
use App\Http\Requests\UpdateUserRequest;
use App\Http\Requests\UploadProfilePicture;
use App\Models\Organization;
use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\Response;

class UserProfileController extends Controller
{
    public function uploadProfilePicture(UploadProfilePicture $request)
    {
        return [
            'upload_url' => route('upload-profile-image', auth()->user()),
            'upload_details' => [
                '_id' => auth()->user()->id,
            ],
        ];
    }

    public function storeProfilePicture(User $user)
    {
        if (count(request()->all()) === 0) {
            // actual file contents.
            try {
                $tempFile = storage_path(uniqid());
                $pngFile = $tempFile . '.png';
                $fileContents = request()->getContent(true);
                file_put_contents($tempFile, $fileContents);

                // validate image
                logger("has file " . json_encode($tempFile));
                // reseize (if necesary)
                logger("converting to png " . $pngFile);
                $image = imagecreatefromstring(file_get_contents($tempFile));

                // resize.
                imagepng($this->cropAlign($image, 800, 600, 'center', 'middle'), $pngFile);

                // shove into S3 via generated name that stays the same.
                $destiny = 'p/' . md5($user->id) . '.png';
                Storage::disk('s3')->put($destiny, file_get_contents($pngFile));
                unlink($pngFile);
            }
            catch (\Exception $e) {
                // error out.
                abort(422, "Invalid image file.");
            }
            finally {
                unlink($tempFile);
            }

            return ['status' => 'ok', 'image' => Storage::disk('s3')->temporaryUrl($destiny, now()->addMinutes(10))];

        }
    }

    public function deleteProfilePicture()
    {
        // nuke the user's profile picture

    }

    function cropAlign($image, $cropWidth, $cropHeight, $horizontalAlign = 'center', $verticalAlign = 'middle')
    {
        $width = imagesx($image);
        $height = imagesy($image);
        $horizontalAlignPixels = $this->calculatePixelsForAlign($width, $cropWidth, $horizontalAlign);
        $verticalAlignPixels = $this->calculatePixelsForAlign($height, $cropHeight, $verticalAlign);
        return imagecrop($image, [
            'x' => $horizontalAlignPixels[0],
            'y' => $verticalAlignPixels[0],
            'width' => $horizontalAlignPixels[1],
            'height' => $verticalAlignPixels[1],
        ]);
    }

    function calculatePixelsForAlign($imageSize, $cropSize, $align)
    {
        switch ($align) {
            case 'left':
            case 'top':
                return [0, min($cropSize, $imageSize)];
            case 'right':
            case 'bottom':
                return [max(0, $imageSize - $cropSize), min($cropSize, $imageSize)];
            case 'center':
            case 'middle':
                return [
                    max(0, floor(($imageSize / 2) - ($cropSize / 2))),
                    min($cropSize, $imageSize),
                ];
            default:
                return [0, $imageSize];
        }
    }
}
