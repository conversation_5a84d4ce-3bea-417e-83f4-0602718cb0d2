<?php

namespace App\StudioHandlers\Disney;

class OrderStatuses
{
    const ACKNOWLEDGED = [
        'label' => 'Acknowledged',
        'code' => 'Acknowledged',
        'booking_code' => 'acknowledged',
        'available' => ['key', 'dcp'],
        'reason' => 'Vendor has received the order request and is giving this acknowledgement.',
    ];
    const COMPLETED = [
        'label' => 'Completed',
        'code' => 'CompletedED',
        'booking_code' => 'completed',
        'available' => ['key', 'dcp'],
        'reason' => 'The order has been delivered to the Theater.',
    ];
    const CANCELLED = [
        'label' => 'Cancelled',
        'code' => 'Cancelled',
        'booking_code' => 'cancelled',
        'available' => ['key', 'dcp'],
        'reason' => 'Disney has initiated a cancellation on the order and the Vendor has processed the cancellation. Keys and/or content is not delivered to the theater.',
    ];
    const CANCELLED_AFTER_COMPLETION = [
        'label' => 'Cancelled After Completion',
        'code' => 'CancelledAfterCompletion',
        'booking_code' => 'cancelled_after_completion',
        'available' => ['key', 'dcp'],
        'reason' => 'Vendor received a cancellation from OFE but the content was previously delivered to the theater.',
    ];
    const REJECTED = [
        'label' => 'Rejected',
        'code' => 'Rejected',
        'booking_code' => 'rejected',
        'available' => ['key', 'dcp'],
        'reason' => 'Delivery rejected. Example - vendor cannot deliver the content in the timeframe, or vendor cannot complete order.',
    ];
    const ERROR = [
        'label' => 'Error',
        'code' => 'Error',
        'booking_code' => 'error',
        'available' => ['key', 'dcp'],
        'reason' => 'There is something wrong with the fulfillment order request and the vendor cannot fulfill the order.',
    ];
    const PENDING_FULFILLMENT = [
        'label' => 'Pending Fulfillment',
        'code' => 'PendingFulfillment',
        'booking_code' => 'transmitting',
        'available' => ['dcp'],
        'reason' => 'The E-Delivery Vendor has started the transfer to the venue but is waiting for a "delivered to theater" confirmation.',
    ];
    const ACCEPTED = [
        'label' => 'Accepted',
        'code' => 'Accepted',
        'booking_code' => 'accepted',
        'available' => ['dcp'],
        'reason' => 'The E-Delivery Vendor has received the order from OFE and accepts it with the intent to fulfill and deliver the content order to the venue.',
    ];
    const INCOMPLETE = [
        'label' => 'Incomplete',
        'code' => 'Incomplete',
        'booking_code' => 'incomplete',
        'available' => ['dcp'],
        'reason' => 'The E-Delivery Vendor was only able to deliver part of the content to the venue. (i.e. not ALL of the Compositions in the Release or DP.)',
    ];

}
