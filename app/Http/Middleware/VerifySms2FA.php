<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken as Middleware;

class VerifySms2FA extends Middleware
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @param string|null $redirectToRoute
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse|null
     */
    public function handle($request, Closure $next, $redirectToRoute = null)
    {
        if (!$request->user() ||
            ($request->user()->getTwoFactorSmsActivatedAttribute() &&
                !$request->user()->hasVerifiedSMS())) {
            logger('need to ver sms');
            return response()->json(['message' => 'Please verify your SMS code.'], 409);
        }

        return $next($request);
    }

}
