<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('bookings', function (Blueprint $table) {
            $table->string('external_order_id')->nullable();
            $table->foreignId('title_id')->nullable()->change();
            $table->foreignId('cinema_site_id')->nullable()->change();
        });
        Schema::table('bookings', function (Blueprint $table) {
            $table->dropColumn('version_id');
        });
        Schema::table('studio_transactions', function (Blueprint $table) {
            $table->string('booking_id')->nullable();
        });
        Schema::table('aspera_access_keys', function (Blueprint $table) {
            $table->foreignId('delivery_id')->nullable(); // what delivery it belongs to
        });
        Schema::table('aspera_access_keys', function (Blueprint $table) {
            $table->dropColumn('booking_id');
        });
        Schema::table('media_manager_jobs', function (Blueprint $table) {
            $table->foreignId('delivery_id')->nullable(); // what delivery it belongs to
        });
        Schema::table('media_manager_jobs', function (Blueprint $table) {
            $table->dropColumn('booking_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('bookings', function (Blueprint $table) {
            $table->dropColumn('external_order_id');
        });
        Schema::table('bookings', function (Blueprint $table) {
            $table->foreignId('title_id')->change();
            $table->foreignId('cinema_site_id')->change();
            $table->foreignId('version_id')->nullable();
        });
        Schema::table('studio_transactions', function (Blueprint $table) {
            $table->dropColumn('booking_id');
        });
        Schema::table('aspera_access_keys', function (Blueprint $table) {
            $table->dropColumn('delivery_id');
        });
        Schema::table('aspera_access_keys', function (Blueprint $table) {
            $table->foreignId('booking_id');
        });
        Schema::table('media_manager_jobs', function (Blueprint $table) {
            $table->dropColumn('delivery_id');
        });
        Schema::table('media_manager_jobs', function (Blueprint $table) {
            $table->foreignId('booking_id');
        });
    }
};
