<?php

namespace Database\Factories;

use App\Models\Booking;
use App\Models\CinemaSite;
use App\Models\Organization;
use App\Models\Title;
use App\Models\Version;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Delivery>
 */
class DeliveryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'is_electronic' => true
        ];
    }

    public function forBooking(Booking $booking)
    {
        return $this->state(function (array $attributes) use ($booking) {
            return [
                'booking_id' => $booking->id,
            ];
        });
    }

    public function withVersion(Version $version)
    {
        return $this->state(function (array $attributes) use ($version) {
            return [
                'version_id' => $version->id,
            ];
        });
    }

    public function withCinema(CinemaSite $cinema)
    {
        return $this->state(function (array $attributes) use ($cinema) {
            return [
                'cinema_site_id' => $cinema->id,
            ];
        });
    }

    public function withOrganization(Organization $org)
    {
        return $this->state(function (array $attributes) use ($org) {
            return [
                'organization_id' => $org->id,
            ];
        });
    }


}
