<?php

namespace App\Providers;

use App\Events\BookingTransactionReceived;
use App\Events\CreateImportBookings;
use App\Events\DispatchWebhook;
use App\Events\ImportBookingsUpload;
use App\Listeners\Clear2FAStatus;
use App\Listeners\CreateImportedBooking;
use App\Listeners\ImportBookingUpload;
use App\Listeners\ProcessBookingTransaction;
use App\Listeners\UpdateLastLogin;
use App\Listeners\ProcessWebhook;
use App\Models\{Booking, BookingStatus, Delivery};
use Illuminate\Auth\Events\Login;
use Illuminate\Auth\Events\Logout;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     * If you declare a listener here that doesn't exist, you can quickly generate it via `artisan event:generate`
     * Just use fully qualified statements so that they are created in the proper paths;
     *      App\Events\MyEvent::class => [App\Listeners\MyEventListener::class],
     * Or choose an existing Laravel event that gets fired and provide your own listener.
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Login::class => [UpdateLastLogin::class,],
        Logout::class => [Clear2FAStatus::class,],
        Registered::class => [SendEmailVerificationNotification::class,],
        BookingTransactionReceived::class => [ProcessBookingTransaction::class],
        ImportBookingsUpload::class => [ImportBookingUpload::class],
        CreateImportBookings::class => [CreateImportedBooking::class],
        DispatchWebhook::class => [ProcessWebhook::class],
    ];

    protected $observers = [
        BookingStatus::class => [\App\Observers\BookingStatus::class],
        Booking::class => [\App\Observers\Booking::class],
        Delivery::class => [\App\Observers\Delivery::class],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     *
     * @return bool
     */
    public function shouldDiscoverEvents()
    {
        return false;
    }
}
