<?php

namespace App\Http\Requests;

use App\Models\CinemaSite;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreCinemaSiteRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return auth()->user()->isAdmin();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'name' => 'required',
            'address' => 'required',
            'city' => 'required',
            'state' => Rule::in(array_keys(CinemaSite::$stateList)),
            'zip' => 'required',
            'circuit' => 'sometimes|string',
        ];
    }
}
