<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class MediaManagerSpeedSummary extends Model
{
    protected $table = 'media_manager_speed_summary';

    protected $appends = ['graph_label'];

    protected $fillable = [
        'media_manager_id',
        'cinema_site_id',
        'year',
        'day_of_year',
        'hour_of_day',
        'min_speed',
        'max_speed',
        'average_speed',
        'sample_count',
        'sample_sum',
    ];
    use HasFactory;

    public function getGraphLabelAttribute()
    {
        // make a date obj for the label, php DoY 1st day is 0, carbon is 1.
        $date = Carbon::createFromFormat('Y z H',
            sprintf('%s %s %s', $this->year, $this->day_of_year - 1, $this->hour_of_day));

        return $date->format('Y-m-d H:i');
    }

    public function updateCurrentRecord($speedInMbps)
    {
        // set min if lower
        if ($this->min_speed === null || $speedInMbps < $this->min_speed) {
            $this->min_speed = $speedInMbps;
        }

        // set max if higher
        if ($this->max_speed === null || $speedInMbps > $this->max_speed) {
            $this->max_speed = $speedInMbps;
        }
        // increment sample_count
        $this->increment('sample_count');

        // increment sample_sum
        $this->sample_sum = $this->sample_sum + $speedInMbps;

        // set new average
        $this->average_speed = $this->sample_sum / $this->sample_count;

        // voila.
        $this->save();
    }
}
