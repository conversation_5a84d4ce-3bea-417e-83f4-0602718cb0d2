<?php

namespace App\StudioHandlers\Disney;

use App\Actions\Slack\Notify;
use App\Jobs\SyncPackageToDeliveries;
use App\Models\{Booking, CinemaSite, Version, Release};
use App\Models\Enum\BookingStatus;

class InboundProcessor extends \App\StudioHandlers\InboundProcessor
{
    protected $site;
    protected $package;

    function __construct($bookingId, $transactionId, $rerun = false)
    {
        parent::__construct($bookingId, $transactionId, $rerun);
    }

    function handleInbound()
    {
        // Add the notification class to the booking.
        $this->booking->notification_class = OutboundNotifier::class;
        $this->booking->notification_parameters = ['orderId' => $this->booking->external_order_id];
        $this->booking->save();

        // Always acknowledge the booking coming in.
        $this->addStatusUpdate(OrderStatuses::ACKNOWLEDGED);

        // Then process the transaction and create/update deliveries as relevant.
        $this->processBooking();

        $this->transaction->processed = 1;
        $this->transaction->save();

        if ($this->booking->package_id) {
            SyncPackageToDeliveries::dispatch($this->booking->package_id);
        }
    }

    function addStatusUpdate($status)
    {
        if (is_array($status)) {
            $status = BookingStatus::tryFrom($status['booking_code']);
        }

        $this->booking->setOverallStatus(
            $status,
            auth()->user()->id ?? null
        );
    }

    function processBooking()
    {
        $booking = $this->booking;
        $data = $this->transaction->transaction_data;

        // First, handle unmapped sites.
        $siteId = $data['physicalAddress']['siteId'];
        $this->site = CinemaSite::where('disney_site_id', $siteId)->first();
        if (!$this->site) {
            Notify::quickMessage("Could not find a Disney ID `{$siteId}` to book for transaction request " . $this->transaction->id);
            $this->addStatusUpdate(OrderStatuses::ERROR);
            return;
        }

        //if ($data['deliveryType'] === 'eDelivery') {
        if ($this->site->primaryMediaManager) {
            $this->booking->is_electronic = true;
        }
        else {
            Notify::quickMessage("Primary CS1 is not configured for this site ID: " . $this->site->id);
        }
        //}

        $this->booking->cinema_site_id = $this->site->id;
        $this->booking->save();

        // Figure out what we're doing with the booking and deliveries.
        switch (strtolower($data['transactionType'])) {
            case 'new' :
                $this->handleNew($data);
                break;
            case 'cancel':
                $this->handleCancelled($data);
                break;
            case 'datechange':
                $this->handleDateChange($data);
                break;
            case 'compositionupdate':
                $this->handleCompositionUpdate($data);
                break;
            default:
                break;
        }

        if ($this->booking->package_id) {
            SyncPackageToDeliveries::dispatch($this->booking->package_id);
        }

    }

    function handleNew($data)
    {
        // Loop through the compositions and create deliveries as relevant.
        $package = $data['package'];
        $compositions = $data['package']['compositions'];

        $this->handleNewCompositions($data, $compositions);

        $this->addStatusUpdate(OrderStatuses::ACCEPTED);
        // Slack notify here.
        $url = config('app.frontend_url') . '/titles/' . $this->booking->title_id . '/summary';
        $tUrl = config('app.frontend_url') . '/bookings/' . $this->booking->id . '/transactions';
        $bUrl = config('app.frontend_url') . '/bookings/' . $this->booking->id;
        $sUrl = config('app.frontend_url') . '/sites/' . $this->booking->cinema_site_id . '/general';

        (new Notify("Accepted New booking from Disney: "))
            ->addFieldsSection([
                ['type' => 'mrkdwn', 'text' => count($compositions) . " Compositions"],
                ['type' => 'mrkdwn', 'text' => "<$tUrl|View Transaction>"],
                ['type' => 'mrkdwn', 'text' => "<$bUrl|View Booking>"],
                ['type' => 'mrkdwn', 'text' => "<$sUrl|View {$this->booking->cinema->name}>"],
            ])
            ->addTextSection(['type' => 'mrkdwn', 'text' => "*Release Title:* <$url|{$package['titleName']}>"])
            ->addTextSection(['type' => 'mrkdwn', 'text' => "*Release Description:* {$package['description']}"])
            ->addDivider()
            ->send();
        return null;

    }

    function handleNewCompositions($data, $compositions)
    {
        // grab a list of all uuids in the composition list
        $uuids = [];
        $parents = [];
        foreach ($compositions as $composition) {
            $uuids[] = str_replace('urn:uuid:', '', $composition['uuid']);
        }

        // the rest should be the usual
        // Composition not provided yet, so we exit.
        if (count($uuids) === 0) {
            Notify::quickMessage("There were no CPLs provided in this Inbound request.");
            return null;
        }

        // Find a title and version. If none match, just return.
        // This should always match a 'parent' bundle from disney.
        $versions = Version::whereIn('cpl_uuid', $uuids)->get();

        foreach ($versions as $version) {
            if (!isset($parents[$version->parent_version_id])) {
                $parents[$version->parent_version_id] = [];
            }
            $parents[$version->parent_version_id][] = $version->id;
        }

        $versionIds = [];

        foreach ($parents as $parentId => $values) {
            if (count($values) === count($uuids))
            {
                $versionIds = $values;
                break;
            }
        }

        if (count($versionIds) === 0) {
            logger("Could not find a match for content on Booking {$this->booking->id}");
            return null;
        }

        // Save the title ID on the booking!
        if (!isset($this->booking->title_id)) {
            $this->booking->title_id = $version->title_id;
            $this->booking->save();
        }

        $this->setupPackage($data, $versionIds);

        // Only create a new delivery if there is a change in main IDs.
        // Keep non-ID fields in the update array in case date/delivery type change.
        $newDelivery = $this->booking->deliveries()->firstOrCreate([
            'organization_id' => $this->booking->organization_id,
            'package_id' => $this->package->id,
            'title_id' => $version->title_id,
            'version_id' => $version->id,
            'cinema_site_id' => $this->site->id,
            'booking_id' => $this->booking->id,
            // 'cpl_uuid' => $cplID,
            'external_id' => $composition['id'],
        ], [
            'deliver_at' => $data['deliveryDate'],
            'is_electronic' => true,
        ]);
    }

    function handleCancelled($data)
    {
        $deliveries = $this->booking->deliveries;
        $complete = 0;

        // Loop through the deliveries of the order. If all are complete, update accordingly.
        foreach ($deliveries as $delivery) {

            if ($delivery->progress === 100 || $delivery->status === "completed") {
                $complete++;
            }

        }

        if ($complete === count($deliveries) && $complete !== 0) {
            $this->addStatusUpdate(OrderStatuses::CANCELLED_AFTER_COMPLETION);
        }
        else if ($complete > 0) {
            $this->addStatusUpdate(OrderStatuses::INCOMPLETE);
        }
        else {
            $this->addStatusUpdate(OrderStatuses::CANCELLED);
        }
    }

    function handleDateChange($data)
    {
        $this->booking->deliver_at = $data['deliveryDate'];
        $this->booking->save();

        $bUrl = config('app.frontend_url') . '/bookings/' . $this->booking->id;
        Notify::quickMessage("Date changed on <{$bUrl}|booking> to {$this->booking->deliver_at->toDateString()}");

        // if the booking was alredy complete, don't reset it's status
        $this->addStatusUpdate($this->booking->overall_status);
    }

    function handleCompositionUpdate($data)
    {
        // we have package->id in the studio_data json, we acn find it and update the list.
        $compositions = $data['package']['compositions'];

        $versionIds = [];
        foreach ($compositions as $composition) {
            $cplID = str_replace('urn:uuid:', '', $composition['uuid']);

            $version = Version::where('cpl_uuid', $cplID)->first();
            if (!$version) {
                // if we don't have the content id, we need to error out.
                Notify::quickMessage("Could not find version to book for transaction request {$this->transaction->id}");
                continue;
            }

            $versionIds[] = $version->id;
        }

        if ($version->parent_version_id) {
            $versionIds = Version::where('parent_version_id', $version->parent_version_id)->get()->pluck('id');
        }

        $this->setupPackage($data, $versionIds);
        $this->addStatusUpdate(OrderStatuses::ACCEPTED);
    }

    private function setupPackage($data, $versionIds = null)
    {

        $this->package = Release::where('studio_data->id', $data['package']['id'])->first();

        if (!$this->package) {
            // make a new package and then attach compositions later on
            $this->package = $this->booking->release()->create([
                'package_name' => $data['package']['description'],
                'title_id' => $this->booking->title_id,
                // don't have title until we scan inbound compositions for versions...
                'organization_id' => $this->booking->organization_id,
                'studio_data' => $data['package'],
            ]);
            if ($versionIds) {
                try {
                    $this->package->content()->sync($versionIds);
                }
                catch (\Exception $e) {
                    // dupes, can be ignored.
                }
            }
        }

        $this->booking->package_id = $this->package->id;
        $this->booking->save();

        // do we have this package going to this cinema already? has it already been sent?
        // hide this booking form the UI, but allow it to do what it has to do in the background.
        $anotherBooking = Booking::where('package_id', $this->package->id)
            ->whereNotIn('overall_status',
                BookingStatus::getStatusesByGroup("completed")) // this should avoid flagging dupes on previously cancelled orders.
            ->where('id', '<', $this->booking->id)
            ->where('cinema_site_id', $this->booking->cinema_site_id)
            ->get();

        if ($anotherBooking && count($anotherBooking) > 0) {
            $this->booking->is_duplicate = true;
            $this->booking->save();
        }
    }
}
