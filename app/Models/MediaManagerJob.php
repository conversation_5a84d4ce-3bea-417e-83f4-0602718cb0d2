<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use GuzzleHttp\Client;

class MediaManagerJob extends Model
{
    use SoftDeletes, HasFactory;

    protected $fillable = [
        'user_id',
        'cinema_site_id',
        'delivery_id',
        'status',
        'name',
        'type',
        'code',
        'source_cpl_uuid',
        'source_partition',
        'source_directory',
        'destination_partition',
        'destination_directory',
        'media_manager_id',
        'directory',
        'cpl',
        'key',
        'progress',
        'media_manager_id',
        'update_url',
    ];

    protected $casts = ['errors' => 'json'];

    protected $hidden = ['cinemaSite'];

    public function getUpdateUrlAttribute($original)
    {
        if ($original) {
            return $original;
        }
        // the default will just be straight to this API, but we can override it by setting a value to update_url.
        // if for example, the cmms needs to ping the _other_ api for a download completed.
        return route('media-managers.post-update-jobs', [
            'serialNumber' => $this->cinemaSite->primaryMediaManager->serial_number,
            'mediaManagerJob' => $this->id,
        ]);
    }

    public function cinemaSite()
    {
        return $this->belongsTo(CinemaSite::class);
    }

    public function delivery()
    {
        return $this->belongsTo(Delivery::class);
    }

    public function mediaManager()
    {
        return $this->belongsTo(MediaManager::class);
    }

    public function sendJob()
    {
        $mediaManager = $this->cinemaSite->primaryMediaManager;
        try {
            if ($this->type === 'aspera_download') {
                $this->sendJobToCineSend();
            }
            else {
                $this->sendJobToDevice();
            }
        }
        catch (\Exception $e) {
            $message = "Failed to send a job {$this->id} to {$mediaManager->serial_number}: {$e->getMessage()}";
            $this->status = 'error';
            $this->errors = $e;
            $this->latest_message = $message;
            $this->save();
            abort(422, "Could not send job to the Media Manager. Please check the port configuration and try again.");
        }
        return true;
    }

    public function sendJobToDevice()
    {
        // only send the job to the media manager that it was created for.
        $mediaManager = $this->mediaManager;
        $url = $mediaManager->getWebURL('/run_job');
        if (!$url) {
            $message = "Cannot send new job - no web port set - to {$mediaManager->serial_number} / job ID: {$this->id}";
            \Sentry\captureMessage($message);
            logger($message);
        }

        $response = (new Client())
            ->post($url, [
                'headers' => [
                    'Authorization' => "Bearer " . $mediaManager->getFreshJWT(
                            ['serial' => $mediaManager->serial_number],
                            $mediaManager->csx_jwt_secret
                        ),
                ],
                'json' => [
                    'job' => array_merge($this->setHidden(['mediaManager', 'cinemaSite', 'delivery'])->toArray(),
                        ['_id' => $this->id]),
                ],
            ]);
        $message = "Sent new job to {$mediaManager->serial_number} / job ID: {$this->id}";
        \Sentry\captureMessage($message);
        logger($message);
    }

    public function sendJobToCineSend()
    {
        /** @var Delivery $delivery */
        $delivery = $this->delivery;
        if (!$delivery) {
            $this->status = 'no_delivery'; // delivery has been removed so this job is no longer processable.
            $this->save();
            return;
        }

        $credentials = $delivery->createDownloadCredentials($this->key);
        $mediaManager = $this->mediaManager;

        if (!$mediaManager) {
            // but don't alter the job and it'll get picked up when a primary comes on.
            logger()->error("No primary media manager for {$delivery->cinema->name}");
            return;
        }

        $serialNumber = $mediaManager->serial_number;
        $version = $delivery->version;
        // $sourceRecord = $version->asperaTransfer()->where('direction', 'upload')->first();

        // $pathName = basename($sourceRecord->transfer_spec['paths'][0]['source'] ?? $delivery->version->version_name);
        $pathName = basename($version->s3_details['path']);

        // logger("Found version id: {$version->id} source id: {$sourceRecord->id} -- using {$pathName} ");

        // deliver via cinesend api
        $response = (new Client())->post(
            config('cinesend.download_job_url') . $serialNumber . "/download-jobs",
            [
                'json' => [
                    'dcdc_job_id' => $this->id,
                    'booking_id' => $delivery->id,
                    'is_fazzt' => $delivery->release->is_fazzt,
                    'update_job_url' => config('app.url') . '/api/e-deliveries/' . $delivery->id . '/job/' . $this->id,
                    'file' => [
                        'name' => $pathName,
                        'size' => $delivery->version->size,
                        'uuid' => $delivery->version->cpl_uuid,
                        'key' => $this->key,
                    ],
                    'aspera_download_key' => $credentials->id,
                    'aspera_download_secret' => $credentials->secret,
                ],
            ]
        );
        //
        try {
            $json = json_decode($response->getBody());
            if ($json->data) {
                $this->cinesend_job_id = $json->data->_id;
                $this->save();
            }
        }
        catch (\Exception $e) {
            // there was an error
            $message = "An error happened trying to save cinesend_job_id on job $this->id";
            \Sentry\captureMessage($message);
            logger($message);
        }
    }

    public function addError($error)
    {
        $errors = $this->errors ?? [];
        $errors[] = $error;
        $this->errors = $errors;
        $this->save();
    }

    public function checkCinesendJob()
    {
        if ($this->cinesend_job_id) {
            $client = new Client();
            $url = config('cinesend.download_job_url') . "{$this->media_manager_id}/check-download-job/{$this->cinesend_job_id}";
            try {
                $response = $client->get($url, ['connect_timeout' => 1]);
            }
            catch (\Exception $e) {
                // unable to update status w/CS, let's skip this one on this run.
                return false;
            }
            if ($response->getStatusCode() === 404) {
                // not found, job deleted
                $this->delete();
                return false;
            }
            if ($response->getStatusCode() === 200) {
                $jobStatus = json_decode($response->getBody());
                $this->status = $jobStatus->status;
                $this->save();
            }
        }

        return true;
    }

}
