<?php

namespace Database\Factories;

use App\Models\Organization;
use App\Models\Title;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Release>
 */
class ReleaseFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'package_name' => uniqid(),
            'type' => 127,
        ];
    }

    public function onTitle(Title $title)
    {
        return $this->state(function (array $attributes) use ($title) {
            return [
                'title_id' => $title->id,
            ];
        });
    }

    public function withOrganization(Organization $org)
    {
        return $this->state(function (array $attributes) use ($org) {
            return [
                'organization_id' => $org->id,
            ];
        });
    }
}
