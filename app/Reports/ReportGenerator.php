<?php

namespace App\Reports;

use App\Models\Report;
use App\Reports\Type\DistributionByTitle;
use Storage;

class ReportGenerator
{

    static function build(Report $report)
    {

        $error = false;
        $report->is_ready = false;
        $report->is_processing = true;
        $report->save();

        $start = now();
        // verify the type
        $type = $report->parameters['type'];

        // the builder should verify the parameters needed and generate the CSV (or error out)
        try {
            logger("what");
            [$error, $tempFile] = match ($type) {
                'dist_by_title' => DistributionByTitle::build($report->parameters['title_id'])
            };
        } catch (\Exception $e) {
            $error = true;
            logger($e->getMessage());
        }

        if (! $error) {
            // upload the TMP
            $destiny = 'r/' . $report->title;
            Storage::disk('s3')->put($destiny, file_get_contents($tempFile));
            unlink($tempFile);
            $report->s3_path = $destiny;
        }

        // fin.
        $end = now();
        $report->time_to_generate = $end->timestamp - $start->timestamp;
        $report->is_ready = true;
        $report->is_processing = false;
        $report->is_error = $error;
        $report->save();
    }

}
