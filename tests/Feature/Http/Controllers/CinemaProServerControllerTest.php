<?php

namespace Tests\Feature\Http\Controllers;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class CinemaProServerControllerTest extends TestCase
{
    /**
     * Pew pew pew.
     *
     * @return void
     */
    public function it_gets_cinema_pro_servers()
    {
        $admin = User::factory()->create()->assignRole('admin');
        $response = $this->actingAs($admin)->getJson(route('cinema-pro-servers.index'));
        $response->assertSuccessful();
    }
}
