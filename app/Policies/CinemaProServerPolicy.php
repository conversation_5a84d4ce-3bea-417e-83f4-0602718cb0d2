<?php

namespace App\Policies;

use App\Models\CinemaProServer;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class CinemaProServerPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param \App\Models\User $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user)
    {
        return $user->isAdmin() || $user->canBoth('view-equipment');
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param \App\Models\User $user
     * @param \App\Models\CinemaProServer $cinemaProServer
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, CinemaProServer $cinemaProServer)
    {
        return $user->isAdmin()
            || ($user->canBoth('view-equipment') && $cinemaProServer->organization_id === $user->organization_id);
    }

    /**
     * Determine whether the user can create models.
     *
     * @param \App\Models\User $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user)
    {
        // no creating these, they come from polling the Fazzt server.
        return false;
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param \App\Models\User $user
     * @param \App\Models\CinemaProServer $cinemaProServer
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, CinemaProServer $cinemaProServer)
    {
        return $user->isAdmin()
            || ($user->canBoth('edit-equipment') && $cinemaProServer->organization_id === $user->organization_id);;
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param \App\Models\User $user
     * @param \App\Models\CinemaProServer $cinemaProServer
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, CinemaProServer $cinemaProServer)
    {
        return false;
    }
}
