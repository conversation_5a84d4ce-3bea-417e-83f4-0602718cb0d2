<?php

namespace Tests\Feature\Auth;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class UpdateUserProfileTest extends TestCase
{
    public function test_user_can_update_profile_and_reverify_email()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->putJson('/user/profile-information', [
            'name' => 'new name',
            'email' => uniqid() . '<EMAIL>',
        ]);

        $response->assertSuccessful();
    }

    public function test_user_can_can_update_name_only()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->put<PERSON>son('/user/profile-information', [
            'name' => 'new name',
            'email' => $user->email,
        ]);

        $response->assertSuccessful();
    }
}
