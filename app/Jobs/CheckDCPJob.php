<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Artisan;

class CheckDCPJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $versionId;
    public $bucket;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($versionId, $bucket = 's3')
    {
        //
        $this->versionId = $versionId;
        $this->bucket = $bucket;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        //
        $results = Artisan::call('dcp:check', ['version_id' => $this->versionId]);
        logger(Artisan::output());

    }
}
