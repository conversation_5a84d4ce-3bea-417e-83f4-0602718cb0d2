<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('cinema_sites', function (Blueprint $table) {
            $table->dropColumn('media_library');
        });

        Schema::table('cinema_sites', function (Blueprint $table) {
            $table->dropColumn('drives');
        });
        Schema::table('cinema_sites', function (Blueprint $table) {
            $table->dropColumn('ssh_port');
        });
        Schema::table('cinema_sites', function (Blueprint $table) {
            $table->dropColumn('web_port');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('cinema_sites', function (Blueprint $table) {
            $table->integer('ssh_port')->nullable();
            $table->integer('web_port')->nullable();
            $table->json('media_library')->nullable();
            $table->json('drives')->nullable();
        });
    }
};
