<?php

namespace App\StudioHandlers\Cielo;

use Illuminate\Support\Carbon;
use Http;

class OutboundNotifier extends \App\StudioHandlers\OutboundNotifier
{
    function sendNotification($status, $booking)
    {
        if (config('app.env') === 'testing') {
            return;
        }

        logger("Cielo: Updated status  {$status['booking_code']}.");
        $url = isset($booking->notification_parameters['notification_url']) ? $booking->notification_parameters['notification_url'] : null;

        if ($url) {
            logger("pinging url: {$url}");
            // easy ping back to the booking->notification url
            $response = Http::post($url, [
                'status' => $status['booking_code'],
                'transaction' => $booking->transactions()->latest('updated_at')->first(),
                'booking' => $booking,
            ]);

        }

    }

    static function pending($booking)
    {
        (new self())->sendNotification(OrderStatuses::PENDING_FULFILLMENT, $booking);
    }

    static function error($booking)
    {
        (new self())->sendNotification(OrderStatuses::ERROR, $booking);
    }

    static function accepted($booking)
    {
        (new self())->sendNotification(OrderStatuses::ACCEPTED, $booking);
    }

    static function acknowledged($booking)
    {
        (new self())->sendNotification(OrderStatuses::ACKNOWLEDGED, $booking);
    }

    static function transmitting($booking)
    {
        (new self())->sendNotification(OrderStatuses::PENDING_FULFILLMENT, $booking);
    }

    static function failed_pending_remediation($booking)
    {
        (new self())->sendNotification(OrderStatuses::ERROR, $booking);
    }

    static function failed_remediation_actioned($booking)
    {
        (new self())->sendNotification(OrderStatuses::ERROR, $booking);
    }

    static function failed_remediation_in_transit($booking)
    {
        (new self())->sendNotification(OrderStatuses::ERROR, $booking);
    }

    static function incomplete($booking)
    {
        (new self())->sendNotification(OrderStatuses::INCOMPLETE, $booking);
    }

    static function rejected($booking)
    {
        (new self())->sendNotification(OrderStatuses::REJECTED, $booking);
    }

    static function cancelled($booking)
    {
        (new self())->sendNotification(OrderStatuses::CANCELLED, $booking);
    }

    static function cancelled_after_completion($booking)
    {
        (new self())->sendNotification(OrderStatuses::CANCELLED_AFTER_COMPLETION, $booking);
    }

    static function delivered($booking)
    {
        (new self())->sendNotification(OrderStatuses::COMPLETED, $booking);
    }

    static function completed($booking)
    {
        (new self())->sendNotification(OrderStatuses::COMPLETED, $booking);
    }
}
