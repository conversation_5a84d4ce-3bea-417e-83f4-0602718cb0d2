<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreRoleRequest;
use App\Http\Requests\UpdateRoleRequest;
use App\Models\Role;

class RoleController extends Controller
{
    public function __construct()
    {
        $this->authorizeResource(Role::class);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return Role::orderBy('name')->with('permissions')->get();
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \App\Http\Requests\StoreRoleRequest $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreRoleRequest $request)
    {
        // this will validate and reject failed posts.
        $values = $request->validated();

        /** @var Role $role */
        $role = Role::create($values);

        if (isset($values['permissionIds'])) {
            $role->permissions()->sync($values['permissionIds']);
        }

        return response(['data' => $role]);
    }

    /**
     * Display the specified resource.
     *
     * @param \App\Models\Role $role
     * @return \Illuminate\Http\Response
     */
    public function show(Role $role)
    {
        return response(['data' => $role->load('permissions')]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \App\Http\Requests\UpdateRoleRequest $request
     * @param \App\Models\Role $role
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateRoleRequest $request, Role $role)
    {
        // this will validate and reject failed posts.
        $values = $request->validated();

        if (isset($values['permissionIds'])) {
            $role->permissions()->sync($values['permissionIds']);
        }

        // if we're here, we're good...
        $role->update($values);

        return response(['data' => $role]);

    }
}
