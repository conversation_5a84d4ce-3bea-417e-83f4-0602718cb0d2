<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use Auth;
use Hash;

class AwsController extends Controller
{
    public function ftpBridge()
    {
        // sign in via username / password and map to ARN role
        // basic how-to:
        /*
         * 1. Add the user in the portal under their respective org
         * 2. Flip their 'ftp_enabled' flag to true/1
         * 3. If their org is new, add a bucket to S3
         * 4. If their org is new, create a role with S3 perms
         * 5. Add their org id, bucket, and role string to this map.
         */
        $roleMap = [
            config('cinesend.paramount_org_id') => [
                'arn' => 'arn:aws:iam::661083776413:role/paramount-sftp-ingest-role',
                'directory' => '/dcdc-paramount-ingest-production',
            ],
            config('cinesend.lionsgate_org_id') => [
                'arn' => 'arn:aws:iam::661083776413:role/lionsgate-sftp-ingest-role',
                'directory' => '/dcdc-lionsgate-ingest-production',
            ],
        ];

        // return a specific AWS response object with ARN role to assume
        $username = request()->json('username');
        $password = request()->json('password');

        $credentials = [
            'email' => $username,
            'password' => $password,
            'status' => 'active',
            'ftp_enabled' => 1,
        ];

        $json = new \stdClass;
        $user = false;
        //$json->creds = "Auth check: " . json_encode($credentials);

        if (Auth::attempt($credentials) && $user = auth()->user()) {
        }
        else {
            $json->error = "Auth Fail"; // . json_encode($credentials);
        }

        if ($user && isset($roleMap[$user->organization_id])) {
            $json->Role = $roleMap[$user->organization_id]['arn'];
            $json->HomeDirectory = $roleMap[$user->organization_id]['directory'];
        }
        else {

            $json->error = "Error Mapping"; // . json_encode($user) . "  => " . json_encode($roleMap);
        }

        // voila, they can sign into the FTPS / SFTP server.
        /*
         response = {
          Role: 'arn:aws:iam::661083776413:role/paramount-sftp-ingest-role', // The user is authenticated if and only if the Role field is not blank
          Policy: '', // Optional, JSON stringified blob to further restrict this user's permissions
          HomeDirectory: '/dcdc-paramount-ingest-production' // Not required, defaults to '/'
         };
         */
        return response()->json($json);
    }
}
