<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreCinemaSiteRequest;
use App\Http\Requests\UpdateCinemaSiteRequest;
use App\Models\CinemaSite;
use App\Models\CinemaSiteContact;

class CinemaSiteController extends Controller
{

    public function __construct()
    {
        $this->authorizeResource(CinemaSite::class, 'cinema');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $query = CinemaSite::query()->select([
            'id',
            'name',
            'city',
            'circuit',
            'address',
            'status',
            'state',
            'country_code',
            'csx_serial_number',
            'disney_site_id',
            'paramount_theatre_id',
            'lionsgate_theatre_id',
            'tcn',
            'secondary_tcn',
            'rentrack',
            'sony_site_id',
            'sage_customer_number',
            'universal_site_id',
            'typical_speeds',
        ]);

        $search = request()->get('search');
        $status = request()->get('status');
        $state = request()->get('state');
        $circuit = request()->get('circuit');

        return $query
            ->when($status, function ($q, $status) {
                $q->whereIn('status', explode(',', $status));
            })
            ->when($circuit, function ($q, $circuit) {
                $q->whereIn('circuit', explode(',', $circuit));
            })
            ->when($state, function ($q, $state) {
                $q->whereIn('state', explode(',', $state));
            })
            ->when($search, function ($q, $search) {
                $wildSearch = "%{$search}%";
                $q->where('name', 'like', $wildSearch)
                    ->orWhere('disney_site_id', $search)
                    ->orWhere('sony_site_id', $search)
                    ->orWhere('paramount_theatre_id', $search)
                    ->orWhere('lionsgate_theatre_id', $search)
                    ->orWhere('tcn', $search)
                    ->orWhere('secondary_tcn', $search)
                    ->orWhere('rentrack', $search)
                    ->orWhere('sage_customer_number', $search)
                    ->orWhere('universal_site_id', $search)
                    ->orWhere('city', 'like', $wildSearch)
                    ->orWhere('address', 'like', $wildSearch);
            })
            ->whereNot('circuit', 'warehouse')
            ->orderBy(request()->get('sortBy', 'name'), request()->get('sortDirection', 'ASC'))
            ->paginate(request()->get('take', 15));
    }

    public function warehouses()
    {
        $query = CinemaSite::query()->select([
            'id',
            'name',
            'city',
            'circuit',
            'address',
            'status',
            'state',
            'country_code',
            'csx_serial_number',
        ]);

        return $query->where('circuit', 'warehouse')->orderBy('name')->paginate(100);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \App\Http\Requests\StoreCinemaSiteRequest $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreCinemaSiteRequest $request)
    {
        $values = $request->validated();
        $values['organization_id'] = auth()->user()->organization_id;
        $cinema = CinemaSite::create($values);
        return response(['data' => $cinema]);
    }

    /**
     * Display the specified resource.
     *
     * @param \App\Models\CinemaSite $cinema
     * @return \Illuminate\Http\Response
     */
    public function show(CinemaSite $cinema)
    {
        $cinema->load([
            'contacts',
            'mediaManagers',
            'appleTelevisions',
            'cinemaProServers',
            'primaryMediaManager:id,web_port,cinema_site_id,status_updated_at,name',
        ])->setAppends([
            'media_manager_is_online',
            'media_manager_status_updated_at',
            'status_log',
        ])->makeVisible(['primaryMediaManager']);
        return response(['data' => $cinema]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \App\Http\Requests\UpdateCinemaSiteRequest $request
     * @param \App\Models\CinemaSite $cinema
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateCinemaSiteRequest $request, CinemaSite $cinema)
    {
        $values = $request->validated();
        $cinema->update($values);
        return response(['data' => $cinema]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\Models\CinemaSite $cinema
     * @return \Illuminate\Http\Response
     */
    public function destroy(CinemaSite $cinema)
    {
        return response($cinema->delete());
    }

    public function createContact(CinemaSite $cinemaSite) {
        $contact = $cinemaSite->contacts()->create(request()->post());
        return $contact;
    }

    public function deleteContact(CinemaSite $cinemaSite, CinemaSiteContact $cinemaSiteContact) {
        $cinemaSiteContact->delete();
        return $cinemaSiteContact;
    }

}
