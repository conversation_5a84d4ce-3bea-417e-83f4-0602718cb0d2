<?php

namespace App\Providers;

use App\Models\User;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        // special abilities need to be defined here so they cannot be assigned to random users in the UI.
        Gate::define('restore_users', function(User $user) {
            return $user->isSuperAdmin();
        });
        Gate::define('restore_organizations', function(User $user) {
            return $user->isSuperAdmin();
        });

    }
}
