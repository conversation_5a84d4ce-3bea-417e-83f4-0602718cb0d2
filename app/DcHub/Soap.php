<?php

namespace App\DcHub;

use SoapClient;
use SoapFault;
use stdClass;

class Soap
{
    /**
     * @throws SoapFault
     * @throws Exception
     */
    static function soapCall(string $method, array $params = [])
    {
        $wsdl = config('dchub.wsdl');
        $username = config('dchub.username');
        $password = config('dchub.password');
        $apiVersion = config('dchub.api_version');

        $credentials = new stdClass();
        $credentials->username = $username;
        $credentials->password = $password;

        // every call is a param sandwich with credentials and api version for bread.
        $params = ['credentials' => $credentials] + $params + ['apiVersion' => $apiVersion];

        $client = new SoapClient($wsdl, ['trace' => 1]);

        $result = $client->__soapCall($method, $params);

        $code = isset($result->code) ? $result->code
            : (isset($result->result->code) ? $result->result->code : null);
        if ($code !== 1) {
            $description = isset($result->description) ? $result->description
                : (isset($result->result->description) ? $result->result->description : null);
            switch ($code) {
                case -1:
                    throw new Exception("Authentication Failed.");
                case -2:
                    throw new Exception("General Error.");
                case -5:
                    throw new Exception("Not authorized for this request.");
                case -7:
                    throw new Exception("No data found. Check parameters and try again.");
                case -9:
                    throw new Exception("Shipment Not Found");
                case -39:
                    throw new Exception("Invalid API Version.");
                case -40:
                    throw new Exception("Shipment Status Not Found");
                case -41:
                    throw new Exception("Invalid Shipment Status");
                case -42:
                    throw new Exception("Invalid time frame.");
                case -58:
                    throw new Exception("On Hold description is required.");
                case -75:
                    throw new Exception("Content not found.");
                default:
                    logger($description);
                    // throw new Exception($description);
            }
        }

        // return the response because they vary a lot.
        // leave it to the caller to process the xml.
        return $result;
    }
}
