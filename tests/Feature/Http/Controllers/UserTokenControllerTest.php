<?php

namespace Tests\Feature\Http\Controllers;

use App\Models\Organization;
use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Event;
use Symfony\Component\HttpFoundation\Response;
use Tests\TestCase;

class UserTokenControllerTest extends TestCase
{

    public function test_regular_user_can_create_token()
    {
        $user = User::factory()->create()->assignRole('studio-read-write');
        $response = $this->actingAs($user)->postJson(route('users.tokens.store', $user), ['name' => uniqid()]);
        $response->assertSuccessful();
        $response->assertJsonStructure(['token', 'message']);
    }

    public function test_regular_user_can_delete_own_token()
    {
        $user = User::factory()->create()->assignRole('studio-read-write');
        $token = $user->createToken('test');

        $response = $this->actingAs($user)->deleteJson(route('users.tokens.destroy',
            ['user' => $user, 'token' => $token->accessToken]));
        $response->assertSuccessful();
    }

    public function test_regular_user_cannot_delete_others_token()
    {
        $user = User::factory()->create()->assignRole('studio-read-write');
        $userTwo = User::factory()->create()->assignRole('studio-read-write');
        $token = $userTwo->createToken('test');

        $response = $this->actingAs($user)->deleteJson(route('users.tokens.destroy',
            ['user' => $user, 'token' => $token->accessToken]));
        $response->assertForbidden();

    }


    public function test_admin_user_can_create_token_for_regular_user()
    {

        $adminUser = User::factory()->create()->assignRole('admin');
        $organization = Organization::factory()->withOwner($adminUser)->create();
        $organization->users()->save($adminUser);

        $user = User::factory()->create()->assignRole('studio-read-write');

        $response = $this->actingAs($adminUser)->getJson(route('users.tokens.store', $user), ['name' => uniqid()]);
        $response->assertSuccessful();

    }


}
