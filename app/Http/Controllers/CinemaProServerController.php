<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreCinemaProServerRequest;
use App\Http\Requests\UpdateCinemaProServerRequest;
use App\Models\CinemaProServer;

class CinemaProServerController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
        $query = CinemaProServer::query()->select([
            'id',
            'site_id',
            'cinema_site_id',
            'serial_number',
            'receive_site_name',
            'raid_type',
            'raid_state',
            'raid_percent_used',
            'raid_size',
            'ip_address',
            'updated_at',
        ]);

        $search = request()->get('search');
        $status = request()->get('status');

        return $query
            ->with('cinemaSite:id,name')
            ->when($search, function ($q, $search) {
                $search = "%{$search}%";
                $q->where('site_id', 'like', $search);
                $q->orWhere('receive_site_name', 'like', $search);
                $q->orWhere('serial_number', 'like', $search);
            })
            ->orderBy(request()->get('sortBy', 'site_id'), request()->get('sortDirection', 'ASC'))
            ->paginate(request()->get('take', 15));
    }

    /**
     * Display the specified resource.
     *
     * @param \App\Models\CinemaProServer $cinemaProServer
     * @return \Illuminate\Http\Response
     */
    public function show(CinemaProServer $cinemaProServer)
    {
        //
        $cinemaProServer->load(['cinemaSite', 'content', 'content.version']);
        return ['cinema_pro' => $cinemaProServer];
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \App\Http\Requests\UpdateCinemaProServerRequest $request
     * @param \App\Models\CinemaProServer $cinemaProServer
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateCinemaProServerRequest $request, CinemaProServer $cinemaProServer)
    {
        $request->validated();
        $cinemaProServer->update($request->all());
        return $this->show($cinemaProServer);

    }

    public function publish(CinemaProServer $cinemaProServer)
    {
        \App\Fazzt\DCP\Publish::exec(request()->post('asset_id'), $cinemaProServer->site_id);
        return $this->show($cinemaProServer);
    }

    public function unpublish(CinemaProServer $cinemaProServer)
    {
        \App\Fazzt\DCP\Unpublish::exec(request()->post('asset_id'), $cinemaProServer->site_id);
        return $this->show($cinemaProServer);
    }

    public function publishCru(CinemaProServer $cinemaProServer)
    {
        // this only works when the CP is in Manual Mode.
        \App\Fazzt\Cru\Publish::exec(request()->post('asset_id'), $cinemaProServer->site_id);
        return $this->show($cinemaProServer);
    }

    public function unpublishCru(CinemaProServer $cinemaProServer)
    {
        // this only works when the CP is in Manual Mode.
        \App\Fazzt\Cru\Unpublish::exec(request()->post('asset_id'), $cinemaProServer->site_id);
        return $this->show($cinemaProServer);
    }

    public function refreshContent(CinemaProServer $cinemaProServer)
    {

        \App\Fazzt\Cinema\Content::exec($cinemaProServer->site_id);
        return $this->show($cinemaProServer);

    }
}
