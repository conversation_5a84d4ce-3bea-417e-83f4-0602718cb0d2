<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Carbon;
use Illuminate\Validation\Rule;
use Laravel\Fortify\Rules\Password;
use Laravel\Fortify\TwoFactorAuthenticatable;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Activitylog\ActivitylogServiceProvider;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Permission\Traits\HasPermissions;
use Spatie\Permission\Traits\HasRoles;
use Storage;

class User extends Authenticatable implements MustVerifyEmail
{
    use SoftDeletes, TwoFactorAuthenticatable, HasApiTokens, HasFactory, HasPermissions, HasRoles, Notifiable, SoftDeletes, LogsActivity;

    protected $attributes = ['status' => 'pending'];

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'status',
        'last_login_at',
        'organization_id',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_secret',
        'two_factor_recovery_codes',
        'two_factor_confirmed_at',
        'roles', // hide the individual values, append the whole list in one shot later.
        'permissions',
    ];

    protected $appends = ['profile_image', 'role'];

    protected $dates = [
        'sms_2fa_verified_at',
        'email_verified_at',
        'last_login_at',
    ];

    /**
     * A sneaky little method to if the access token can do it and the parent user of said access token can do it.
     * this allows the token to be a subset or all of the user's permissions, but not exceed them.
     * @param string $ability
     * @return bool
     */
    public function canBoth(string $ability)
    {
        return $this->tokenCan($ability) && $this->can($ability);
    }

    /**
     * @param User|object|null $updateModel
     * @return array
     */
    static function validationRules(User $updateModel = null)
    {
        if ($updateModel) {
            // when updating, we only validate data if it is sent.
            // if the key is missing we'll just not update it.
            $required = 'sometimes';
            $emailRule = Rule::unique(User::class)->ignore($updateModel->id);
        }
        else {
            // if we are creating, we want them to be enforced.
            $required = 'required';
            $emailRule = Rule::unique(User::class);
        }
        if (auth()->user()->isAdmin()) {
            $roleRule = ['sometimes', 'string', Rule::in(['admin', 'operations', 'subscriber'])];
        }
        else {
            $roleRule = ['prohibited'];
        }

        return [
            'name' => [$required, 'string', 'max:255'],
            'email' => [
                $required,
                'string',
                'email',
                'max:255',
                $emailRule,
            ],
            'password' => [
                'sometimes',
                'string',
                (new Password)->length(8)->requireNumeric()->requireSpecialCharacter()->requireUppercase(),
                'confirmed',
            ],
            'organization_id' => ['integer'],
            'status' => ['sometimes', 'in:active,inactive,pending'],
            'role' => $roleRule,
        ];
    }

    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Simple Role Checks
     */
    function isSuperAdmin()
    {
        return $this->hasRole('super-admin');
    }

    function isAdmin()
    {
        return $this->hasRole(['admin', 'super-admin']);
    }

    // for the UI, this will cascade through assigned roles and direct permissions returning just a list
    function getAllPermissionsAttribute()
    {
        if ($this->needs_password ||
            ($this->getTwoFactorSmsActivatedAttribute() && !$this->hasVerifiedSMS())) {
            return [];
        }
        return $this->getAllPermissions()->pluck('name');
    }

    function getAllRolesAttribute()
    {
        if ($this->needs_password) {
            return [];
        }
        return $this->getRoleNames();
    }

    function getTwoFactorActivatedAttribute()
    {
        return $this->getTwoFactorAppActivatedAttribute() || $this->getTwoFactorSmsActivatedAttribute();
    }

    public function getTwoFactorAppActivatedAttribute()
    {
        return ($this->two_factor_secret && $this->two_factor_confirmed_at);
    }

    public function getTwoFactorSmsActivatedAttribute()
    {

        return ($this->phone && $this->phone->phone_number && $this->phone->last_verified_at);
    }

    function getOrganizationTypeAttribute()
    {
        return $this->organization->type;
    }

    function getTwoFactorPendingAttribute()
    {
        return ($this->two_factor_secret && !$this->two_factor_confirmed_at);
    }

    function getTwoFactorSmsPendingAttribute()
    {
        return ($this->phone && $this->phone->phone_number && !$this->phone->last_verified_at);
    }

    function getNeedsPasswordAttribute()
    {
        return $this->password === null;
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->dontSubmitEmptyLogs()
            ->logFillable()
            ->logExcept(['remember_token', 'password'])
            ->logOnlyDirty();
    }

    // things the User has changed
    public function caused(): MorphMany
    {
        return $this->morphMany(ActivitylogServiceProvider::determineActivityModel(), 'causer');
    }

    public function getChangesAttribute()
    {
        // this is activities on this user model (no need for subject, because $this is the subject).
        return $this->activities()->with('causer')->latest()->limit(10)->get();
    }

    public function getActivitiesAttribute()
    {
        // actions the user has done. (it's backwards from activity logger's wording.
        // include the subject object for display purposes on the UI
        return $this->caused()->with([
            'subject',
            'causer',
        ])->latest()->limit(10)->get();
    }

    public function getStatusAttribute($original)
    {
        if ($original) {
            return $original;
        }

        return $this->email_verified_at ? 'active' : 'pending';
    }

    public function getLastLoginAtAttribute($original)
    {
        if ($activity = $this->getLastVerifiedEvent()) {
            return $activity->created_at;
        }
        return $original;
    }

    public function getRoleAttribute()
    {

        if ($this->hasRole('super-admin')) {
            return 'super-admin';
        }
        if ($this->hasRole('admin')) {
            return 'admin';
        }
        if ($this->hasRole('operations')) {
            return 'operations';
        }
        if ($this->hasRole('subscriber')) {
            return 'subscriber';
        }
        return null;
    }


    public function getLastVerifiedEvent()
    {
        return ActivityLog::where('causer_id', $this->id)
            ->where('causer_type', User::class)
            ->where('log_name', 'system')->where('event', 'verified')
            ->whereDate('created_at', Carbon::today())
            ->first();
    }

    public function activityWithIp($description, $event)
    {
        activity()
            ->withProperties([
                'ip' => request()->ip(),
                'agent' => request()->userAgent(),
            ])
            ->causedBy($this)
            ->event($event)
            ->useLog('system')
            ->log($description);
    }

    public function roleChangeActivity($description, $event, $property, $performedOn)
    {
        activity()
            ->performedOn($performedOn)
            ->withProperties($property)
            ->causedBy($this)
            ->event($event)
            ->useLog('system')
            ->log($description);
    }

    public function phone()
    {
        return $this->hasOne(UserPhoneNumber::class);
    }

    public function hasVerifiedSMS()
    {
        return $this->sms_2fa_verified_at;
    }

    public function getProfileImageAttribute()
    {
        $path = 'p/' . md5($this->id) . '.png';
        try {
            if (Storage::disk('s3')->exists($path)) {
                return Storage::disk('s3')->temporaryUrl($path, now()->addDays(7));
            }
        }
        catch (\Exception $e) {
            // nothing exciting here, move along.
        }
        return null;
    }
}
