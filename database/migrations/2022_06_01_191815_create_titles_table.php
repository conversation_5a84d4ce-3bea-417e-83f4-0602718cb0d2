<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('titles', function (Blueprint $table) {
            $table->id();
            $table->string('friendly_title'); // ie "Top Gun: Maverick"

            $table->unsignedBigInteger('organization_id'); // owner org
            $table->unsignedBigInteger('creator_id'); // user who created it
            $table->unsignedBigInteger('version_count')->default(0); // a running total for versions, to be updated when a version is added/deleted.

            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('titles');
    }
};
