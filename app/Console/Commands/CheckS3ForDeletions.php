<?php

namespace App\Console\Commands;

use App\Models\Version;
use App\Models\AsperaTransfer;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class CheckS3ForDeletions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 's3:check {--do-delete=false : Actually Delete files by passing true.}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check the S3 bucket for mismatched content and delete orphans. ** NOTE ** THIS WILL DESTROY UPLOADED DATA THAT IS NOT REFERENCED BY AN ACTIVE VERSION';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $s3 = Storage::disk('s3');
        $s3folders = $s3->directories(AsperaTransfer::ASPERA_ROOT);

        $deletables = [];

        foreach ($s3folders as $folder) {
            // Find any version that references this folder within it's s3_details 'root'
            $versions = Version::whereJsonContains('s3_details->root', $folder)->get();

            if (count($versions) == 0) {
                // if it doesn't exist flag it for delete.
                $deletables[] = $folder;
            }
        }

        if (count($deletables) > 0) {
            foreach ($deletables as $deletable) {
                if ($this->option('do-delete') === "true") { // options are strings, make sure it's explicit and not 'truthy'
                    $this->info("Deleting: {$deletable}");
                    $s3->deleteDirectory($deletable);
                }
                else {
                    $this->info("Would delete: {$deletable}");
                }
            }
        }

        $this->info(sprintf("Searched %d folders. %s %s orphaned folders.",
            count($s3folders),
            ($this->option('do-delete') === "true" ? "Deleted" : "Would delete"),
            count($deletables)));

        return 0;
    }
}
