<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('aspera_access_keys', function (Blueprint $table) {
            $table->id();
            $table->foreignId('booking_id'); // what booking it belongs to
            $table->json('config')->nullable(); // holds aspera key and secret
            $table->string('path'); // holds the path to the S3 directory
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('aspera_access_keys');
    }
};
