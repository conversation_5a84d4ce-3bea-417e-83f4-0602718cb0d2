<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreReleaseRequest;
use App\Http\Requests\UpdateReleaseRequest;
use App\Jobs\SyncPackageToDeliveries;
use Illuminate\Http\Response;
use App\Models\{Delivery, MediaManager, Release, Title};
use Storage;

class TitleReleaseController extends Controller
{
    /**
     * Store a newly created resource in storage.
     *
     * @param \App\Http\Requests\StoreReleaseRequest $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreReleaseRequest $request, Title $title)
    {
        $validated = $request->validated();

        $validated['organization_id'] = auth()->user()->organization_id;
        /** @var Release $release */
        $release = $title->releases()->create($validated);

        if ($request->has('attached_content')) {
            // todo: make sure attached_content belongs to the title.
            $release->content()->sync($validated['attached_content']);
        }

        return $this->show($title, $release);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Title $title)
    {
        $search = request()->get('search');
        return $title->releases()
            ->when($search, function ($q) use ($search) {
                $q->where('package_name', 'like', "%{$search}%");
            })
            ->orderBy(request()->get('sortBy', 'created_at'), request()->get('sortDirection', 'DESC'))
            ->paginate(request()->get('take', 15));

    }

    /**
     * Display the specified resource.
     *
     * @param \App\Models\Release $release
     * @return \Illuminate\Http\Response
     */
    public function show(Title $title, Release $release)
    {
        $release->load(['content:id,version_name,cpl_uuid,size'])->setAppends(['content_available','types_list']);
        return response(['data' => $release]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \App\Http\Requests\UpdateReleaseRequest $request
     * @param \App\Models\Release $release
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateReleaseRequest $request, Title $title, Release $release)
    {
        $validated = $request->validated();

        if ($release->title_id !== $title->id) {
            logger("package mismatch");
            abort(403, "Release owner mismatch.");
        }

        if ($request->has('add_content_id')) {
            if ($title->versions()->where('id', $request->get('add_content_id'))->exists()) {
                $release->content()->attach($validated['add_content_id']);
            }
        }
        if ($request->has('remove_content_id')) {
            if ($title->versions()->where('id', $request->get('remove_content_id'))->exists()) {
                $release->content()->detach($validated['remove_content_id']);
            }
        }

        if ($request->has('attached_content')) {
            $release->content()->sync($validated['attached_content']);
        }

        SyncPackageToDeliveries::dispatch($release->id);

        $release->fill($validated);
        $release->save();
        return $this->show($title, $release);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Title $title
     * @param Release $release
     * @return Response
     */
    public function destroy(Title $title, Release $release)
    {
        $release->delete();
        return $this->show($title, $release);
    }

    public function uploadIngestLetter(Release $release) {
        {
            return [
                'upload_url' => route('upload-ingest-letter', $release),
                'upload_details' => [
                    '_id' => $release->id,
                ],
            ];
        }
    }

    public function storeIngestLetter(Release $release)
    {
        if (count(request()->all()) === 0) {
            // actual file contents.
            try {
                $tempFile = storage_path(uniqid());
                $fileContents = request()->getContent(true);
                file_put_contents($tempFile, $fileContents);
                $destiny = 'igl/' . md5($release->id) . '.pdf';
                Storage::disk('s3')->put($destiny, $fileContents);
            }
            catch (\Exception $e) {
                // error out.
                logger($e->getMessage());
                abort(422, "Invalid PDF.");
            }
            finally {
                unlink($tempFile);
            }

            return ['status' => 'ok', 'ingest_letter' => Storage::disk('s3')->temporaryUrl($destiny, now()->addMinutes(10))];

        }
    }

    public function prepareForFazzt(Title $title, Release $release) {
        //
        // release must have exactly 1 content (DCP),
        if ($release->content()->count() <> 1) {
            abort(422, "This release cannot be sent via FAZZT. It must be a single DCP folder. (merged DCPs accepted)");
        }
        if ($release->is_fazzt) {
            // do not create a second is_fazzt transfer for this release.
            abort(422, "This release is already set for Fazzt Transmission.");
        }

        $mm = MediaManager::where('is_fazzt_headend', 1)->first();

        if (!$mm) {
            abort(422, "There is no Fazzt headend configured.");
        }

        // create a transfer immediately to the MM that is_fazzt_headend
        /** @var Delivery $delivery */
        $delivery = Delivery::create([
            'version_id' => $release->content()->first()->id,
            'booking_id' => 0,
            'organization_id' => $release->title->organization_id, //auth()->user()->organization_id,
            'cinema_site_id' => $mm->cinema_site_id,
            'package_id' => $release->id,
            'title_id' => $release->title_id,
            'is_electronic' => 1,
        ]);

        // flag with is_fazzt set to true and auto-pin it.
        $release->is_fazzt = true;
        $release->is_pinned = true;
        $release->save();

        // send the transfer immediately to queue.
        $delivery->sendDownloadJob();

        // and return this release.
        return $this->show($title, $release);

    }

}
