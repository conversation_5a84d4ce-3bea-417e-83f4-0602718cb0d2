<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('cinema_sites', function (Blueprint $table) {
            //
            $table->unsignedInteger('disney_site_id')->nullable();
            $table->string('tcn')->nullable();
            $table->string('rentrack')->nullable();

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('cinema_sites', function (Blueprint $table) {
            $table->dropColumn('disney_site_id');
        });

        Schema::table('cinema_sites', function (Blueprint $table) {
            $table->dropColumn('tcn');
        });

        Schema::table('cinema_sites', function (Blueprint $table) {
            $table->dropColumn('rentrack');
        });
    }
};
