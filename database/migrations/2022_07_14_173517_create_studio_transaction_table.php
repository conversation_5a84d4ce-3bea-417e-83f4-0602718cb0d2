<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('studio_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id'); // the signed-in user that sent it in
            $table->boolean('processed')->default(false); // has our system acted on this request?
            $table->json('transaction_data');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('studio_transactions');
    }
};
