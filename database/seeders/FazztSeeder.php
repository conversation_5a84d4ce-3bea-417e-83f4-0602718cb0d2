<?php

namespace Database\Seeders;

use App\Models\FazztContent;
use App\Models\FazztTransfer;
use App\Models\Title;
use App\Models\Version;
use \Exception;
use App\Models\Release;
use Illuminate\Database\Seeder;
use App\Models\User;

class FazztSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        //
        $user = User::first();
        if( !$user) {
            throw new Exception("make/seed a user first");
        }
        $organization = $user->organization;

        // title
        $title = Title::factory()->withCreator($user)->withOrganization($organization)->create();
        $content = Version::factory()->onTitle($title)->withCreator($user)->create();
        $release = Release::factory()->onTitle($title)->withOrganization($organization)->create();

        $release->content()->saveMany([$content]);

        // fazzt content
        FazztContent::factory()->onContent($content)->create();

        FazztTransfer::factory()->onContent($content)->create();
        FazztTransfer::factory()->onContent($content)->asInProgress()->create();
        FazztTransfer::factory()->onContent($content)->asComplete()->create();

    }
}
