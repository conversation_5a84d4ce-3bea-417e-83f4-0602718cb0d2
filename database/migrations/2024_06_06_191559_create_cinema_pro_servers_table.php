<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('cinema_pro_servers', function (Blueprint $table) {
            $table->id();

            $table->integer('site_id')->unique();

            $table->foreignId('cinema_site_id')->nullable();

            $table->string('receive_site_name')->nullable();
            $table->string('serial_number')->nullable();
            $table->string('pub_key_name')->nullable();
            $table->string('raid_type')->nullable();
            $table->string('raid_state')->nullable();
            $table->integer('raid_percent_used')->nullable();
            $table->string('raid_size')->nullable();
            $table->string('ip_address')->nullable();

            $table->timestamps();
            $table->softDeletes();

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('cinema_pro_servers');
    }
};
