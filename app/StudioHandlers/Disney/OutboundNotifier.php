<?php

namespace App\StudioHandlers\Disney;

use App\Jobs\RetryOutboundNotification;
use App\Models\OutboundNotifierLog;
use Illuminate\Support\Carbon;

class OutboundNotifier extends \App\StudioHandlers\OutboundNotifier
{
    // const URL = 'https://mockbin.org/bin/0044d628-c3c4-4c78-98bc-3b517282d5b7';
    // PUT https://webvan.disney.com/studio/ofe/v1/orders/contentorders/{OrderId}

    function sendNotification($status, $booking)
    {
        // Ignore notifications in testing for now.
        // Would be cool to capture these using MockHandler or similar.
        if (config('app.env') === 'testing') {
            return;
        }

        $url = config('cinesend.disney_notify_url');
        if (!$url) {
            $this->logToSentry("DISNEY_NOTIFY_URL is not set in the ENV. Cannot send notification.");
            return;
        }

        $firstTransaction = $booking->transactions()->first();
        if (!$firstTransaction) {
            $this->logToSentry("Missing a transaction to pull content order ID from!");
            return;
        }


        $orderId = $firstTransaction->transaction_data['orderId'] ?? null;
        if (!$orderId) {
            $this->logToSentry("Missing orderId from transaction data!");
            return;
        }

        $contentOrderId = $firstTransaction->transaction_data['contentOrderId'] ?? null;
        if (!$contentOrderId) {
            $this->logToSentry("Missing contentOrderId from transaction data!");
            return;
        }

        $url .= "/contentorders/{$contentOrderId}";
        $message = "Sending Code: {$status['code']} for OID {$contentOrderId} / Booking: {$booking->id}";

        try {
            $data = [
                'json' => [
                    'OrderId' => $orderId,
                    'Status' => $status['code'],
                    'StatusDate' => (new Carbon())->toJSON(),
                ],
                'http_errors' => false,
            ];

            $client = new \GuzzleHttp\Client();
            $response = $client->put($url, $this->appendCerts($data));
            $bodyContent = $response->getBody()->getContents();

            $responseCode = $response->getStatusCode();

            $completed = ($responseCode === 200);

            $onl = OutboundNotifierLog::create([
                'booking_id' => $booking->id,
                'status' => $booking->overall_status,
                'completed' => $completed,
                'response_code' => $responseCode,
                'response_body' => $bodyContent,
                'notification_class' => self::class,
            ]);

            if (!$completed) {
                // dispatch a job with a 10 minute delay to retry the status update.
                RetryOutboundNotification::dispatch($onl)->delay(now()->addMinutes(10));
            }
        }
        catch (\Exception $e) {
            $this->logToSentry("Error {$message}\nError: {$e->getMessage()}");
        }
    }

    function appendCerts($data)
    {
        // If we're local, ignore the certs.
        if (config('app.env') === 'local') {
            return $data;
        }

        // Make sure the storage path exists.
        if (!file_exists(storage_path('certs/'))) {
            mkdir(storage_path('certs/'));
        }

        // Where we store the key file and cert file.
        $keyFileName = storage_path('certs/ssl-key.key');
        $certificateFileName = storage_path('certs/ssl-cert.crt');
        $alwaysOverride = true;

        // Fetch from Vapor secrets and store locally for both ssl_key
        // and ssl_certificate to make sure they're sane.
        // these must be local files for curl/guzzle to work.

        if (!file_exists($keyFileName) || $alwaysOverride) {
            $keyContents = config('cinesend.ssl_key');
            if ($keyContents === null) {
                throw new \Exception("Key contents are null, not writing file.");
            }
            file_put_contents($keyFileName, $keyContents);
        }

        if (!file_exists($certificateFileName) || $alwaysOverride) {
            $certificateContents = config('cinesend.ssl_certificate');
            if ($certificateContents === null) {
                throw new \Exception("Certificate contents are empty, not writing file.");
            }
            file_put_contents($certificateFileName, $certificateContents);
        }

        //        $this->logToSentry("Key Data:\n" . file_get_contents($keyFileName));
        //        $this->logToSentry("Cert Data:\n". file_get_contents($certificateFileName));

        $certificateData = [
            'cert' => $certificateFileName,
            'ssl_key' => $keyFileName,
        ];

        // Then just merge the paths of the key and cert into our data.
        return array_merge($data, $certificateData);
    }

    static function pending($booking)
    {
        (new self())->sendNotification(OrderStatuses::PENDING_FULFILLMENT, $booking);
    }

    static function error($booking)
    {
        (new self())->sendNotification(OrderStatuses::ERROR, $booking);
    }

    static function accepted($booking)
    {
        (new self())->sendNotification(OrderStatuses::ACCEPTED, $booking);
    }

    static function acknowledged($booking)
    {
        (new self())->sendNotification(OrderStatuses::ACKNOWLEDGED, $booking);
    }

    static function transmitting($booking)
    {
        (new self())->sendNotification(OrderStatuses::PENDING_FULFILLMENT, $booking);
    }

    static function failed_pending_remediation($booking)
    {
        (new self())->sendNotification(OrderStatuses::ERROR, $booking);
    }

    static function failed_remediation_actioned($booking)
    {
        (new self())->sendNotification(OrderStatuses::ERROR, $booking);
    }

    static function failed_remediation_in_transit($booking)
    {
        (new self())->sendNotification(OrderStatuses::ERROR, $booking);
    }

    static function incomplete($booking)
    {
        (new self())->sendNotification(OrderStatuses::INCOMPLETE, $booking);
    }

    static function rejected($booking)
    {
        (new self())->sendNotification(OrderStatuses::REJECTED, $booking);
    }

    static function cancelled($booking)
    {
        (new self())->sendNotification(OrderStatuses::CANCELLED, $booking);
    }

    static function cancelled_after_completion($booking)
    {
        (new self())->sendNotification(OrderStatuses::CANCELLED_AFTER_COMPLETION, $booking);
    }

    static function delivered($booking)
    {
        (new self())->sendNotification(OrderStatuses::COMPLETED, $booking);
    }

    static function completed($booking)
    {
        (new self())->sendNotification(OrderStatuses::COMPLETED, $booking);
    }
}
