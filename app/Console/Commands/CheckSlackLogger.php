<?php

namespace App\Console\Commands;

use App\Actions\Dcp\VerifyDcp;
use App\Models\AsperaTransfer;
use App\Models\Title;
use App\Actions\Slack\Notify;

use App\Models\Version;
use Illuminate\Console\Command;

class CheckSlackLogger extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'slack:logger {message}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Ping the slack logger to make sure it\'s working';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        // logger()->channel('slack')->notice($this->argument('message'));
        // logger()->notice($this->argument('message'));
        $version = Version::first();

        $url = config('app.frontend_url') . '/titles/' . $version->title_id . '/summary';
        $vUrl = config('app.frontend_url') . '/titles/' . $version->title_id . '/content/' . $version->id;

        Notify::quickMessage("Happily split <{$vUrl}|{$version->version_name}> into many CPLs");
//
//        $slack = (new Notify("Successful DCP Validation for \"{$version->title->friendly_title}.\""))
//            ->addFieldsSection([
//                ['type' => 'mrkdwn', 'text' => "<{$url}|{$version->title->friendly_title}>"],
//                ['type' => 'mrkdwn', 'text' => "<{$vUrl}|{$version->version_name}>"]
//            ]);
//        $slack->send();
//
//        $slack = (new Notify())
//            ->addTextSection(['type' => 'mrkdwn', 'text' => ":fire: fun times _had by all_ <{$url}|{$version->title->friendly_title}>"])->send();
//
    }
}
