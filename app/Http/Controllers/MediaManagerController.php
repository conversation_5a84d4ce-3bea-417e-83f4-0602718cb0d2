<?php

namespace App\Http\Controllers;

use App\Actions\Slack\Notify;
use App\Models\CinemaSite;
use App\Models\MediaManager;
use App\Models\MediaManagerJob;
use App\Models\MediaManagerDrive;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Schema;
use App\Http\Requests\{UpdateMediaManagerRequest, StoreMediaManagerRequest};
use Carbon\Carbon;

class MediaManagerController extends Controller
{

    public function login()
    {
        $serial = request()->post('serial');

        if (!$serial) {
            return response()->json([
                'error' => 'The credentials provided are invalid. Missing: serial number.',
            ], 401);
        }

        // Grab the first one, or create it.
        /** @var MediaManager $mediaManager */
        $mediaManager = MediaManager::firstOrCreate(
            ['serial_number' => $serial],
            ['name' => '[' . $serial . ']', 'organization_id' => 1]
        );

        if ($mediaManager->wasRecentlyCreated) {
            $mediaManager->cinema_site_id = 29; // iStar (prod) warehoused by default.
        }

        // If no password has been set, let's set it now.
        if (!isset($mediaManager->password)) {
            $mediaManager->password = bcrypt(request()->post('password'));
        }

        // TODO: Remove this later, we are constantly overriding the password until cmms app / keyctl feature is in place.
        if (app()->environment('production') || true) {
            $mediaManager->password = bcrypt(request()->post('password'));
        }

        // Removing this now since the ports get created from CineSend API when the CSX originally registers.
        // If no web port exists, let's create one now.
        // if (!isset($mediaManager->web_port)) {
        //     $mediaManager->web_port = MediaManager::generatePort();
        //     $mediaManager->save();
        // }

        // // If no web port exists, let's create one now.
        // if (!isset($mediaManager->ssh_port)) {
        //     $mediaManager->ssh_port = MediaManager::generatePort();
        //     $mediaManager->save();
        // }

        $mediaManager->save();

        // Now try to auth with the provided password.
        $authSuccess = auth()->guard('media-manager')->attempt([
            'serial_number' => $serial,
            'password' => request()->post('password'),
        ], true);
        if (!$authSuccess) {
            return response()->json([
                'error' => 'The credentials provided are invalid.',
            ], 401);
        }

        // Otherwise, successful. Generate tokens etc.
        auth()->guard('media-manager')->login($mediaManager);

        // Hey, maybe send a job too if it was just rebooted and has a pending job...
        if ($mediaManager->cinemaSite) {
            // todo: disable sending next job for now.
            // $mediaManager->cinemaSite->load('primaryMediaManager');
            // $mediaManager->cinemaSite->sendNextPendingJob(true);
        }

        // Get new JWT keys and save them in the database for future csx endpoint calls
        $mediaManager->csx_jwt_secret = $mediaManager->getJWTSecret();
        $mediaManager->public_ip = request()->getClientIp();
        $mediaManager->save();

        // We need to specifically merge the secret in because in all other cases of returning
        // the media manager properties it will and should be hidden
        $returnData = [
            'csx_jwt_secret' => $mediaManager->csx_jwt_secret,
            'serial_number' => $mediaManager->serial_number,
            'name' => $mediaManager->name,
            // TODO: Figure this part out? 'decoder_ip_address' => $mediaManager->decoder_ip_address
        ];

        if ($mediaManager->canReceiveJobs()) {
            $returnData['is_reachable'] = true;
        }

        return response()->json($returnData);
    }

    public function triggerHeartbeat($serialNumber)
    {

        if (!$serialNumber) {
            return response()->json([
                'error' => 'The credentials provided are invalid. Missing: serial number.',
            ], 401);
        }

        $mediaManager = MediaManager::where('serial_number', $serialNumber)->first();
        if (!$mediaManager) {
            return response()->json([
                'error' => 'No media manager found with the serial number provided.',
            ], 401);
        }

        $mediaManager->status_updated_at = Carbon::now();
        $mediaManager->public_ip = request()->getClientIp();
        $mediaManager->save();

        $site = $mediaManager->cinemaSite()->where('status', \App\Models\Enum\SiteStatus::Fault)->first();
        if ($site) {
            $site->status = \App\Models\Enum\SiteStatus::Online;
            $site->save();

            $url = config('app.frontend_url') . '/sites/' . $site->id . '/general';
            $slack = Notify::quickMessage(":heavy_check_mark: <$url|{$site->name}> has recovered from fault.");

        }

        return response()->json(['status' => 'ok']);
    }

    public function updateMediaLibrary()
    {
        return ['status' => 'ok'];
    }

    public function updateDrives()
    {
        return ['status' => 'ok'];
    }

    public function updateSystemSettings()
    {
        // if results['key'] exists on the media manager model, save it.

        /** @var MediaManager $mediaManager */
        $mediaManager = $this->getMediaManager();
        $results = request()->results;
        foreach ($results as $key => $value) {

            // If the value is a transfer speed, let's save it as a data point on the cinema site.
            if ($key === 'download_speed') {
                if ($site = $mediaManager->cinemaSite) {
                    $site->downloadSpeeds()->create([
                        'download_speed' => $value,
                        'media_manager_id' => $mediaManager->id,
                    ]);
                }
            }
            else {
                $mediaManager->fill([$key => $value]);
            }

        }
        $mediaManager->status_updated_at = Carbon::now();
        $mediaManager->save();
    }

    public function updateSmartInfo()
    {
        // save it to MediaManagerDrive
        /** @var MediaManager $mediaManager */
        $mediaManager = $this->getMediaManager();

        $drive = $mediaManager->drives()->firstOrCreate([
            'drive' => request()->post('device'),
        ]);

        $drive->smart_data = $drive->parseData(request()->post('results'));

        // this will only write if it's dirty.
        $drive->save();

    }

    public function updateDownloads()
    {
        // tree -> downloads_folder_tree
        $results = request()->post('tree');
        /** @var MediaManager $mediaManager */
        $mediaManager = $this->getMediaManager();
        $mediaManager->fill(['downloads_folder_tree' => $results]);
        $mediaManager->save();

    }

    public function getMediaManager()
    {
        $mediaManager = auth()->user() ?? MediaManager::where('serial_number', request()->serialNumber)->first();
        if (!$mediaManager) {
            abort(404);
        }
        return $mediaManager;
    }

    public function show(MediaManager $mediaManager)
    {
        return ['mediaManager' => $mediaManager->getDetails()];
    }

    public function update(UpdateMediaManagerRequest $request, MediaManager $mediaManager)
    {
        $request->validated();

        if ($request->post('is_primary')) {
            // only do this part if it's online.
            if ($mediaManager->isOnline()) {
                $mediaManager->cinemaSite->mediaManagers()->whereNot('id',
                    $mediaManager->id)->update(['is_primary' => 0]);
            }
            else {
                abort(422, "Media manager is not online. Cannot set as primary.");
            }
        }

        if ($request->post('cinema_site_id')) {
            // force this one to be primary
            $request->merge(['is_primary' => 1]);
            // and remove others if they exist...
            $mediaManager->cinemaSite->mediaManagers()->whereNot('id',
                $mediaManager->id)->update(['is_primary' => 0]);
        }

        $mediaManager->update($request->all());
        return $this->show($mediaManager);
    }

    public function index()
    {

        $query = MediaManager::query()->select([
            'id',
            'name',
            'serial_number',
            'type',
            'kind',
            'cinema_site_id',
            'is_primary',
            'updated_at',
            'status_updated_at',
            // let the UI show is_online based on this so we don't hammer each CSX on full listings.
        ]);

        $search = request()->get('search');
        $status = request()->get('status');

        return $query
            ->with('cinemaSite')
            ->when($status, function ($q, $status) {
                $q->whereHas('cinemaSite', function ($q) use ($status) {
                    match ($status) {
                        'deployed' => $q->whereNot('circuit', 'warehouse'),
                        'warehouse' => $q->where('circuit', 'warehouse'),
                    };
                });
            })
            ->when($search, function ($q, $search) {
                $search = "%{$search}%";
                $q->where('serial_number', 'like', $search);
                $q->orWhere('name', 'like', $search);
            })
            ->orderBy(request()->get('sortBy', 'serial_number'), request()->get('sortDirection', 'ASC'))
            ->paginate(request()->get('take', 15));
    }

}
