<?php

namespace App\Console\Commands;

use App\Actions\Slack\Notify;
use App\Events\BookingTransactionReceived;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use App\Models\StudioTransaction;

class CheckForInboundParamountXML extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'paramount:inbound';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check our S3 for inbound XML';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Look at the S3 bucket, all in the root. (see filesystems.php config),
        // Override bucket with env AWS_PARAMOUNT_BUCKET
        // all xml files should be in the root of this bucket and they will be moved to xml-processed when completed.
        $disk = Storage::disk('s3-paramount');

        $files = $disk->files();
        foreach ($files as $file) {
            // parse any XML filthat are there
            $this->info("Parsing $file");

            // - create transaction from the file
            try {
                $this->info("-- Loaded XML contents.");
                $xml = simplexml_load_string($disk->get($file));

                $transaction = StudioTransaction::create([
                    'user_id' => config('cinesend.paramount_user_id'),
                    'organization_id' => config('cinesend.paramount_org_id'),
                    'booking_id' => null,
                    'transaction_data' => $xml,
                ]);
            }
            catch (\Exception $e) {
                // send this to slack
                $this->error("[Paramount XML] Error processing inbound file: $file: {$e->getMessage()}");
                $slack = Notify::quickMessage("[Paramount]  Error processing inbound file: $file: {$e->getMessage()}, file moved to errors folder.");

                $disk->move($file, 'xml-errors/' . $file);
                continue;
            }

            // delete XML (move to /archived subfolder for now, even though the transaction data is saved.)
            $disk->move($file, 'xml-processed/' . $file);

            // fire transaction processing job
            $this->info("-- Firing off processing job.");
            event(new BookingTransactionReceived(null, $transaction->id, 'paramount'));

            $data = $transaction->transaction_data;
            $data['filename'] = $file;
            $transaction->transaction_data = $data;
            $transaction->save();
        }


        $this->info("All Done.");
        return Command::SUCCESS;
    }
}
