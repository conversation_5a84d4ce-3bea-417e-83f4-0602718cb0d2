<?php

namespace App\Models\Enum;

use App\Models\Booking;
use App\Models\Release;

enum TitleStatus: int
{
    case STATUS_ACTIVE = 1;
    case STATUS_ARCHIVE = 2;
    case STATUS_UNKNOWN = 127;

    // Release - SMPTE, Release - InterOp, Release - LAS, Release - Dolby Vision, Other
    public function label()
    {
        return match ($this) {
            TitleStatus::STATUS_ARCHIVE => "Archived",
            TitleStatus::STATUS_ACTIVE => "Active",
            TitleStatus::STATUS_UNKNOWN => "Unknown",
        };
    }

    public static function contains($value): self|null
    {
        return self::tryFrom($value);
    }
}
