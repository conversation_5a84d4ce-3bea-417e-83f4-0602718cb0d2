<?php

namespace App\Jobs;

use App\Models\DownloadSpeed;
use App\Models\MediaManagerSpeedSummary;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SummarizeDownloadRange implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $startId;
    private $lastId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($startId = 0)
    {
        //
        $this->startId = $startId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        //
        $this->lastId = $this->startId;
        logger("Starting {$this->lastId}");

        $downloads = DownloadSpeed::where('id', '>', $this->startId)
            ->where('is_aspera_transfer', 1)
            ->orderBy('id')
            ->limit(100)
            ->get()
            ->each(function ($download) {
                $created = new Carbon($download->created_at);
                $summary = MediaManagerSpeedSummary::firstOrCreate([
                    'year' => $created->year,
                    'day_of_year' => $created->dayOfYear,
                    'hour_of_day' => $created->hour,
                    'media_manager_id' => $download->media_manager_id,
                    'cinema_site_id' => $download->cinema_site_id,
                ]);
                $summary->updateCurrentRecord($download->download_speed);
                $this->lastId = $download->id;
            });

        if ($this->lastId !== $this->startId) {
            logger("Dispatching next batch {$this->lastId}");
            SummarizeDownloadRange::dispatch($this->lastId);
        }
        else {
            logger("Completed {$this->lastId}");
        }
    }
}
