<?php

namespace App\Models;

use App\Models\Enum\BookingStatus as BS;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use GuzzleHttp\Client;
use Illuminate\Database\Eloquent\SoftDeletes;
use Psy\Readline\Hoa\_Protocol;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Booking extends Model
{
    use SoftDeletes, HasFactory, LogsActivity;

    protected $fillable = [
        'organization_id',
        'creator_id',
        'cinema_site_id',
        'title_id',
        'package_id',
        'email_sent',
        'deliver_at',
        'release_date',
        'is_electronic',
        'notification_class',
        'notification_parameters',
        'overall_status',
        'transfer_counts',
        'external_order_id',
    ];
    protected $dates = ['deliver_at', 'release_date'];
    protected $hidden = ['statuses', 'transactions'];
    protected $appends = ['latest_status', 'transaction_release_description'];
    protected $casts = [
        'email_sent' => 'boolean',
        'notification_parameters' => 'json',
        'transfer_counts' => 'json',
        'overall_status' => BS::class,
    ];
    protected $attributes = [
        'is_electronic' => false,
    ];

    public function organization()
    {
        return $this->belongsTo(Organization::class)->withTrashed();
    }

    public function title()
    {
        return $this->belongsTo(Title::class)->withTrashed();
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'creator_id')->withTrashed();
    }

    public function cinema()
    {
        return $this->belongsTo(CinemaSite::class, 'cinema_site_id')->withTrashed();
    }

    public function deliveries()
    {
        return $this->hasMany(Delivery::class);
    }

    public function statuses()
    {
        return $this->hasMany(BookingStatus::class);
    }

    public function release()
    {
        return $this->belongsTo(Release::class, 'package_id');
    }

    public function transactions()
    {
        return $this->hasMany(StudioTransaction::class)->orderBy('created_at', 'desc');
    }

    public function transfers()
    {
        return $this->hasMany(Delivery::class);
    }

    public function setOverallStatus(BS $value, $userId = null)
    {
        // don't set it if it's the same as the last status
        $lastStatus = $this->statuses->last();
        if ($lastStatus && $lastStatus->status == $value) {
            return;
        }

        // make sure statuses() doesn't already contain it
        $existing = $this->statuses()->where('status', $value)->first();
        if ($existing) {
            // nuke the old, so we add a new one with updated time stamps.
            $existing->delete();
        }


        // and add to statuses()
        $this->statuses()->create([
            'status' => $value,
            'user_id' => $userId,
        ]);

        $this->overall_status = $value;

        if (!$this->is_duplicate) {
            // sync this with duplicates
            $duplicates = Booking::where('package_id', $this->package_id)
                ->where('cinema_site_id', $this->cinema_site_id)
                ->where('is_duplicate', 1)
                ->where('id', '>', $this->id)
                ->each(function ($booking) use ($value) {
                    $booking->setOverallStatus($value);
                });
        }
        $this->save();
    }

    public function getLatestStatusAttribute()
    {

        if ($this->overall_status) {
            return $this->overall_status->groupedLabel();
        }

        $status = $this->statuses->last();
        if ($status) {
            $this->overall_status = $status->status;
            $this->save();
        }

        return $this->overall_status ? $this->overall_status->groupedLabel() : 'pending';

    }

    public function getTransactionReleaseDescriptionAttribute()
    {
        if ($first = $this->transactions->first()) {
            return [
                'description' => $first->transaction_data['package']['description'] ?? 'N/A',
                'title' => $first->transaction_data['package']['titleName'] ?? 'N/A'
            ];
        }
        return ['description' => 'N/A', 'title' => 'N/A'];
    }

    public function fillTransferCounts()
    {
        $speedInMegabits = null;

        $activeDelivery = $this->deliveries()->where('status', 'transmitting')
            ->whereNotNull('speed_in_mbps')->first();

        if ($activeDelivery) {
            $speedInMegabits = $activeDelivery->speed_in_mbps;
        }
        $totalBytes = 0;
        $currentBytes = 0;
        $completedTransfers = 0;
        $totalTransfers = 0;

        foreach ($this->deliveries as $delivery) {
            $totalBytes += $delivery->version->size;
            $currentBytes += ($delivery->progress / 100) * $delivery->version->size;
            $totalTransfers++;
            if ($delivery->status == BS::Completed) {
                $completedTransfers++;
            }
        }
        if ($speedInMegabits) {
            $remainingBytes = $totalBytes - $currentBytes;
            $remainingMegabytes = $remainingBytes / 1024 / 1024;
            $remainingMegabits = $remainingMegabytes * 8;
            $remainingTimeInSeconds = $remainingMegabits / $speedInMegabits;
            $completionDate = now()->addSeconds($remainingTimeInSeconds);
        }
        $this->transfer_counts = [
            'transfers_completed' => $completedTransfers,
            'transfers_total' => $totalTransfers,
            'current_bytes' => $currentBytes,
            'total_bytes' => $totalBytes,
            'speed_in_mbps' => $speedInMegabits,
            'progress' => $totalBytes ? round($currentBytes / $totalBytes, 2) : 0,
            'estimated_completion' => $speedInMegabits ? $completionDate : null,
        ];
        $this->saveQuietly();
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->dontSubmitEmptyLogs()
            ->logFillable()
            ->logExcept(['transfer_counts'])
            ->logOnlyDirty();
    }

}
