<?php

namespace App\DcHub;

use Carbon\Carbon;
use SoapFault;

class Distributors
{
    /*
    Soap Calls:
    getDistributorList(credentials $credentials, string $apiVersion)
     */
    static function getDistributors()
    {
        $response = Soap::soapCall('getDistributorList');

        $distributors = simplexml_load_string($response->xml);

        logger("identified " . count($distributors) . " distributor(s)");
        $results = [];

        foreach ($distributors as $distributor) {
            $results[] = (array) $distributor;
        }

        return $results;

    }
}
