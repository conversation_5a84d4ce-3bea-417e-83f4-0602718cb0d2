<?php

namespace Tests\Feature\Http\Controllers;

use App\Models\CinemaSite;
use App\Models\Booking;
use App\Models\Enum\BookingStatus;
use App\Models\Organization;
use App\Models\Title;
use App\Models\User;
use App\Models\Version;
use Illuminate\Support\Str;
use Illuminate\Testing\Fluent\AssertableJson;
use Tests\TestCase;

class StudioBookingControllerTest extends TestCase
{
    public function test_studio_user_can_submit_transaction()
    {
        $user = User::factory()->create();
        $organization = Organization::factory()->asType('studio')->withOwner($user)->create();
        $organization->users()->save($user);

        $title = Title::factory()->withOrganization($organization)->withCreator($user)->create();
        Version::factory()->onTitle($title)->withCreator($user)->create(['cpl_uuid' => '123']);

        $response = $this->actingAs($user)->postJson(route(
            'studio-booking',
            ['studio' => 'disney']
        ), $this->buildTransaction());
        $response->assertSuccessful();
        $response->assertJson(fn(AssertableJson $json) => $json->has('status')->has('booking')->etc());
    }

    public function test_studio_user_cannot_submit_to_bogus_studio()
    {
        $user = User::factory()->create();
        $organization = Organization::factory()->asType('studio')->withOwner($user)->create();
        $organization->users()->save($user);

        $response = $this->actingAs($user)->postJson(route('studio-booking', ['studio' => 'bogus']),
            ['orderId' => 123, 'transactionType' => 'new']);
        $response->assertStatus(500);

    }

    public function test_non_studio_user_cannot_submit_transaction()
    {
        $user = User::factory()->create();
        $organization = Organization::factory()->asType('exhibitor')->withOwner($user)->create();
        $organization->users()->save($user);

        $response = $this->actingAs($user)->postJson(route('studio-booking', ['studio' => 'test']), []);
        $response->assertSuccessful();
        $response->assertJson(['error - invalid org type']);
    }

    public function test_creates_booking()
    {
        // given
        $user = User::factory()->create();
        $organization = Organization::factory()->asType('studio')->withOwner($user)->create();
        $organization->users()->save($user);

        $title = Title::factory()->withOrganization($organization)
            ->withCreator($user)->create(['friendly_title' => "Create Booking Title"]);
        $orderID1 = time() . rand(0, 1000);
        $orderID2 = time() . rand(0, 1000);
        $contentOrderID1 = time() . rand(0, 1000);
        $contentOrderID2 = time() . rand(0, 1000);
        $uniqueSiteID = time() . rand(0, 1000);

        $sameUUID = Str::UUID();
        $cplUUID1 = [$sameUUID];
        $cplUUID2 = [$sameUUID, Str::UUID()];

        $parent1 = Version::factory()->onTitle($title)->withCreator($user)->create(['multi_cpl_uuids' => $cplUUID1]);
        $parent2 = Version::factory()->onTitle($title)->withCreator($user)->create(['multi_cpl_uuids' => $cplUUID2]);

        $child1 = Version::factory()->onTitle($title)->withCreator($user)->create(['cpl_uuid' => $cplUUID1[0], 'parent_version_id' => $parent1->id]);
        $child2 = Version::factory()->onTitle($title)->withCreator($user)->create(['cpl_uuid' => $cplUUID2[0], 'parent_version_id' => $parent2->id]);
        $child3 = Version::factory()->onTitle($title)->withCreator($user)->create(['cpl_uuid' => $cplUUID2[1], 'parent_version_id' => $parent2->id]);

        $newCinema = CinemaSite::factory()->withOrganization($organization)->create(['disney_site_id' => $uniqueSiteID]);

        // when
        $transaction = $this->buildTransaction($orderID1, $contentOrderID1, $cplUUID1, $uniqueSiteID, 1);
        $this->actingAs($user)->postJson(route(
            'studio-booking',
            ['studio' => 'disney']
        ), $transaction);

        $booking1 = Booking::where('external_order_id', $transaction['contentOrderId'])->first();
        $this->assertTrue(isset($booking1));
        $this->assertEquals($title->id, $booking1->title_id);
        $this->assertNotNull($booking1->package_id);
        $this->assertEquals($newCinema->id, $booking1->cinema_site_id);

        $transaction = $this->buildTransaction($orderID2, $contentOrderID2, $cplUUID2, $uniqueSiteID, 2);
        $this->actingAs($user)->postJson(route(
            'studio-booking',
            ['studio' => 'disney']
        ), $transaction);

        // then
        $booking2 = Booking::where('external_order_id', $transaction['contentOrderId'])->first();
        $this->assertTrue(isset($booking2));
        $this->assertEquals($title->id, $booking2->title_id);
        $this->assertNotNull($booking2->package_id);
        $this->assertEquals($newCinema->id, $booking2->cinema_site_id);

        $this->assertNotEquals($booking1->id, $booking2->id);
    }

    public function test_updates_a_delivery_date()
    {
        // given
        $user = User::factory()->create();
        $organization = Organization::factory()->asType('studio')->withOwner($user)->create();
        $organization->users()->save($user);

        $title = Title::factory()->withOrganization($organization)->withCreator($user)->create();
        $orderID = time() . rand(0, 1000);
        $contentOrderID = time() . rand(0, 1000);
        $cplUUID = time() . rand(0, 1000);
        $uniqueSiteID = time() . rand(0, 1000);
        Version::factory()->onTitle($title)->withCreator($user)->create(['multi_cpl_uuids' => [$cplUUID]]);
        CinemaSite::factory()->withOrganization($organization)->create(['disney_site_id' => $uniqueSiteID]);

        $transaction = $this->buildTransaction($orderID, $contentOrderID, [$cplUUID], $uniqueSiteID, 3);
        $this->actingAs($user)->postJson(route(
            'studio-booking',
            ['studio' => 'disney']
        ), $transaction);
        $booking = Booking::where('external_order_id', $transaction['contentOrderId'])->first();

        // when
        $sooner = now()->addDays(1);
        $transaction['transactionType'] = 'Datechange';
        $transaction['deliveryDate'] = $sooner;
        $this->actingAs($user)->postJson(route(
            'studio-booking',
            ['studio' => 'disney']
        ), $transaction);

        // then
        $this->assertEquals($sooner->timestamp, $booking->refresh()->deliver_at->timestamp);
    }


    /**
     * This builds a transaction to mimic the Disney Studio input structure.
     *
     * @param  int  $orderID
     * @param  int  $contentOrderID
     * @param  array  $cplUUIDs
     * @param  int  $siteID
     * @param  int  $disneyPackageId
     * @return array
     *
     * @return array
     */
    private function buildTransaction(int $orderID = 123123123, int $contentOrderID = 987987987, array $cplUUIDs = ['uuid123'], int $siteID = 789, int $disneyPackageId = 10000): array
    {
        $UUIDs = collect($cplUUIDs)->map(function ($cplUUID) {
            return [
                'id' => $cplUUID,
                'uuid' => $cplUUID,
                'contentUniqueId' => $cplUUID,
            ];
        });

        return [
            'orderId' => $orderID,
            'contentOrderId' => $contentOrderID,
            'transactionType' => 'new',
            'deliveryDate' => now()->addWeek(),
            'deliveryType' => 'eDelivery',
            'package' => [
                'id' => $disneyPackageId,
                'titleName' => 'Test Title',
                'description' => 'testDescription',
                'compositions' => $UUIDs,
            ],
            'physicalAddress' => [
                'siteId' => $siteID,
                'iso' => 'US',
                'city' => 'city',
                'address1' => '123 street',
                'region' => 'OH',
                'circuitName' => 'circuit',
                'postalCode' => '19238',
                'siteName' => 'test Site',
            ],
        ];
    }
}
