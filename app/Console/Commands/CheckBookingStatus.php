<?php

namespace App\Console\Commands;

use App\Jobs\VerifyBooking;
use App\Models\Booking;
use App\Models\Delivery;
use App\Models\Enum\BookingStatus;
use GuzzleHttp\TransferStats;
use Illuminate\Console\Command;

class CheckBookingStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bookings:check';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check active bookings and update their statuses if needed.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Check all of these non-completed states:
        $bookings = Booking::whereIn('overall_status', [
            BookingStatus::Transmitting,
            BookingStatus::Pending,
            BookingStatus::Accepted,
            BookingStatus::Acknowledged,
        ])->with(['transfers'])->get();

        // check their transfer records and make sure they're still that.
        /** @var Booking $booking */
        foreach ($bookings as $booking) {
            $this->info("Checking booking ID {$booking->id} Status: {$booking->overall_status->label()}");
            $transfers = $booking->transfers;
            $count = $transfers->count();

            // count = 0 means nothing has been matched yet, so just roll with it.
            $this->info("-- Transfer count: {$count}");

            if ($count == 0) {
                continue;
            }

            $transferCounts = [];
            /** @var Delivery $transfer */
            foreach ($transfers as $transfer) {
                $this->info("-- Transfer Status: {$transfer->status->label()}");
                if (isset($transferCounts[$transfer->status->value])) {
                    $transferCounts[$transfer->status->value]++;
                }
                else {
                    $transferCounts[$transfer->status->value] = 1;
                }
            }

            $transfersTransmitting = intval($transferCounts[BookingStatus::Transmitting->value] ?? 0);
            $transfersError = intval($transferCounts[BookingStatus::Error->value] ?? 0);
            $transfersComplete = intval($transferCounts[BookingStatus::Completed->value] ?? 0);
            $transfersTimedOut = intval($transferCounts[BookingStatus::Timedout->value] ?? 0);

            // any transfer transmitting = booking transmitting
            if ($transfersTransmitting > 0) {
                $this->info("Still transmitting...");
                $booking->setOverallStatus(BookingStatus::Transmitting);
                continue;
            }

            // any transfer error = booking error
            if ($transfersError > 0) {
                $this->info("-- Has an actual error...");
                $booking->setOverallStatus(BookingStatus::Error);
                continue;
            }

            // if transfers complete + timed out = and we're before delivery deadline = Error
            if (($transfersComplete + $transfersTimedOut == $count) && $booking->deliver_at->isAfter(now())) {
                $this->info("-- There's still time, so we'll Error and maybe manually recover.");
                $booking->setOverallStatus(BookingStatus::Error);
                continue;
            }

            // if transfers complete + timed out = and we're after delivery deadline = Incomplete
            if (($transfersComplete + $transfersTimedOut == $count) && $booking->deliver_at->isBefore(now())) {
                $this->info("-- It's too late to ship. We're Incomplete.");
                $booking->setOverallStatus(BookingStatus::Incomplete);
                continue;
            }

            // all transfers complete = booking complete
            if ($transfersComplete == $transferCounts) {
                $this->info("-- Fully Completed Trigger Verification utility.");
                if (in_array($booking->overall_status, BookingStatus::getStatusesByGroup('in_progress'))) {
                    $booking->setOverallStatus(BookingStatus::Verify);
                }
                continue;
            }

        }
        // flag as error or incomplete if necessary.
        return Command::SUCCESS;
    }
}
