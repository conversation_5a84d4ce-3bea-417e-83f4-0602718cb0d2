<?php

namespace App\Fazzt\DCP;

use App\Fazzt\Exception;
use App\Fazzt\Json;

class Ingest
{
    /**
     * map p;
     * p["method"] = "CinemaDCPIngestSubmit";
     * p["params"][0] = myFilepath;
     *
     * if(myEntryID!="")
     * p["params"][1]["EntryID"] = myEntryID;
     * if(myOnProcessChange!="")
     * p["params"][1]["OnProcessChange"] = myOnProcessChange;
     *
     * p["id"] = id;
     *
     * map post;
     * // We construct map p and format it into JSON string.
     * post["Data"] = ValueToJSONString(p);
     * post["Headers"] = "Content-Type: application/json\r\n\r\n";
     * post["URL"] = "http://127.0.0.1:4039/ws/json-rpc/InvokeFazztFunction.fzt";
     */

    static function exec($path) {
        $params = [
            0 => $path,
        ];
        try {
            $response = Json::jsonCall(
                'CinemaDCPIngestSubmit',
                null,
                $params,
                true);
            // dump($response);
            return true;
        }
        catch (\Exception $e) {
            logger($e->getMessage());
        }
        return false;
    }
}
