<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Booking;
use App\StudioHandlers\Disney\InboundProcessor as DisneyInboundProcessor;
use App\StudioHandlers\Sony\InboundProcessor as SonyInboundProcessor;

class ProcessBooking extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bookings:process {booking_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process a booking: check transactions / create relevant deliveries';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $booking = Booking::findOrFail($this->argument('booking_id'));

        if (!$booking->transactions()->count()) {
            $this->info("No transactions to process...");
            return;
        }

        foreach ($booking->transactions as $transaction) {
            if ($booking->organization_id === 2) {
                $handler = new DisneyInboundProcessor($booking->id, $transaction->id, true);
            }
            if (true) { //$booking->organization_id === 4)  {
                $handler = new SonyInboundProcessor($booking->id, $transaction->id, true);
            }

            $handler->handleInbound();
            $this->info("Handled booking: " . $booking->id);
        }
    }
}
