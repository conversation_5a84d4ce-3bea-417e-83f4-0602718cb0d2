<?php

namespace App\Http\Controllers;

use App\Models\{CinemaSite, MediaManager, MediaManagerJob};
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Schema;

class JobController extends Controller
{

    public function index(CinemaSite $cinemaSite)
    {
        $search = request()->search;
        return $cinemaSite->jobs()->when($search, function ($q, $search) {
            $search = "%{$search}%";
            $q->where('name', 'like', $search);
        })
            ->orderBy(request()->sortBy ?? 'created_at', request()->sortDirection ?? 'DESC')
            ->paginate(request()->take);

    }

    /**
     * To be called from the dcdc-frontend to trigger a job (copy dcp, delete dcp, etc.)
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // the rest is on media managers, but things from the UI need regular auth.
        $this->middleware('auth');

        // determine what type of job this is via the payload
        /** @var CinemaSite $site */
        $site = CinemaSite::findOrFail(request()->post('site_id'))->load(['primaryMediaManager']);

        $code = request()->post('code');
        // type is only used for delete via the UI: library or drive
        // other values can be 'aspera_upload', 'aspera_download, 'reboot' and 'toggle_share', but those are not sent via the UI.
        $type = request()->post('type', '');

        if (!$code) {
            logger()->error('A code was not provided for a new job.');
            return response()->json(false);
        }

        $data = [
            'user_id' => auth('web')->user()->id,
            'media_manager_id' => $site->primaryMediaManager->id,
            'type' => $type,
            'code' => $code,
        ];

        if (request()->name) {
            $data['name'] = request()->name;
        }

        $cpls = request()->post('cpls', []);
        $rows = [];

        if (count($cpls) > 0) {
            foreach ($cpls as $cpl) {
                $data['source_cpl_uuid'] = $cpl['uuid'];
                $supplemental = $this->getSupplementalData($site->primaryMediaManager, $code, $cpl);
                $data = array_merge($data, $supplemental);

                $rows[] = $data;
            }
        }
        else {
            $supplemental = $this->getSupplementalData($site->primaryMediaManager, $code);
            $data = array_merge($data, $supplemental);
            $rows[] = $data;
        }

        // create the job
        $jobs = $site->jobs()->createMany($rows);

        // send the job directly to the cmms app if it does not have a currently running job
        // otherwise it will sit in the queue until the running job is completed.
        // $job->setAppends(['update_url']);
        // logger($jobs);
        return response()->json($site->sendNextPendingJob());
    }

    /**
     * Delete the job from the DB
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\MediaManagerJob $mediaManagerJob
     * @return \Illuminate\Http\Response
     */
    public function delete(MediaManagerJob $mediaManagerJob)
    {
        $mediaManagerJob->delete();

        return response()->json(true);
    }

    /**
     * Send the job again
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\MediaManagerJob $mediaManagerJob
     * @return \Illuminate\Http\Response
     */
    public function retryJob(MediaManagerJob $mediaManagerJob)
    {
        $mediaManagerJob->status = 'pending';
        $mediaManagerJob->save();

        $mediaManagerJob->sendJob();

        return response()->json(true);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\MediaManagerJob $mediaManagerJob
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $serialNumber, MediaManagerJob $mediaManagerJob)
    {
        $finishedStatuses = ['Completed', 'Failed'];

        $status = request()->post('status');
        logger("updating job {$mediaManagerJob->id} with status $status");

        // If we're already finished, don't set. (IE don't override Failed with Triggered)
        if (in_array($mediaManagerJob->status, $finishedStatuses)) {
            return;
        }

        // save any necessary status/data... if a job is completed and there are pending jobs
        $id = request()->post('id'); // hdd jobs report this
        $mediaManagerJob->status = request()->post('status');
        $jobDetails = request()->post('jobDetails');

        if ($jobDetails && is_array($jobDetails)) {
            logger("job details: " . json_encode($jobDetails));
            $id = $jobDetails['JOB_ID'] ?? $jobDetails['id'];
            $mediaManagerJob->directory = $jobDetails['destination'] ?? null;
        }

        $mediaManagerJob->media_manager_job_id = $id;

        logger("Updating an individual job status! {$mediaManagerJob->id} : {$mediaManagerJob->status} remote id {$mediaManagerJob->job} ");

        if ($mediaManagerJob->status === 'Failed') {
            $mediaManagerJob->addError($jobDetails);
        }

        // if we're complete or failed, send the next in line.
        if (in_array($mediaManagerJob->status, $finishedStatuses)) {
            if ($mediaManagerJob->status == 'Completed') {
                // if it's completed set progress to 100 because prior updates wouldn't have hit 100.
                $mediaManagerJob->progress = 100;
            }
        }

        if ($mediaManagerJob->isDirty()) {
            $mediaManagerJob->save();
            if ($mediaManagerJob->status == 'Completed') {
                $this->maybeCreateValidateJob($mediaManagerJob);
                $mediaManagerJob->cinemaSite->sendNextPendingJob();
                $mediaManagerJob->mediaManager->getStorage();
            }
        }
    }

    private function maybeCreateValidateJob($mediaManagerJob)
    {
        // if it's a cpl, library, dcp to drive job queue the subsequent validate
        $postValidateTypes = [
            'ingest_cpl_from_library_to_drive',
            'ingest_cpl_from_drive_to_drive',
            'ingest_cpl_from_drive_to_library',
        ];

        if (in_array($mediaManagerJob->code, $postValidateTypes)) {
            // if it requires a validate job after, create one... on the same media manager.
            $name = str_replace('Ingest', 'Validate', $mediaManagerJob->name);
            $data = [
                'status' => 'pending',
                'media_manager_id' => $mediaManagerJob->media_manager_id,
                'source_cpl_uuid' => $mediaManagerJob->source_cpl_uuid,
                'type' => '',
                'code' => 'validate_dcp',
                'directory' => $mediaManagerJob->directory,
                'name' => $name,
            ];
            $mediaManagerJob->cinemaSite->jobs()->firstOrCreate($data);
        }
    }

    private function getSupplementalData($mediaManager, $code, $cpl = null)
    {
        // attach supplemental data based on type & code combos.
        switch ($code) {
            case 'ingest_cpl_from_library_to_drive':
                return $this->getIngestFromLibraryToDriveData($cpl);
                break;
            case 'ingest_cpl_from_drive_to_drive':
                return $this->getIngestCPLFromDriveToDriveData($cpl);
                break;
            case 'ingest_cpl_from_drive_to_library':
                return $this->getIngestCPLFromDriveToLibraryData($cpl);
                break;
            case 'ingest_dcp_from_drive_to_library':
                // this is actually handled by ingest_cpl_from_drive_to_library...
                break;
            case 'ingest_cpl_from_local_to_library':
                // this is handled by the download portion and cinesend api for now.
                break;
            case 'validate_dcp':
                return $this->getValidateDCPData($cpl);
                break;
            case 'delete_cpl':
                return $this->getDeleteCPLData($cpl);
                break;
            case 'delete_dcp':
                return $this->getDeleteDCPData($cpl);
                break;
            case 'format_drive':
                return $this->getFormatDriveData();
                break;
        }

        return [];
    }

    private function getIngestFromLibraryToDriveData($cpl)
    {
        $drive = request()->drive;
        return [
            'name' => sprintf(
                "Ingest %s from Library to %s",
                $cpl['title'],
                $drive['location_name']
            ),
            'source_cpl_uuid' => $cpl['uuid'],
            'destination_partition' => $drive['partitions'][0]['device'],
        ];
    }

    private function getIngestCPLFromDriveToDriveData($cpl)
    {
        $sourceDrive = request()->sourceDrive;
        $destinationDrive = request()->destinationDrive;
        return [
            'name' => sprintf(
                "Copy %s from %s to %s",
                $cpl['title'],
                $sourceDrive['location_name'],
                $destinationDrive['location_name']
            ),
            'source_cpl_uuid' => $cpl['uuid'],
            'source_directory' => $cpl['directory'],
            'destination_partition' => $destinationDrive['partitions'][0]['device'],
        ];
    }

    private function getIngestCPLFromDriveToLibraryData($cpl)
    {
        $drive = request()->drive;
        return [
            'name' => sprintf(
                "Ingest %s from %s to Library",
                $cpl['title'],
                $drive['location_name']
            ),
            'source_cpl_uuid' => $cpl['uuid'],
            'source_directory' => $cpl['directory'],
        ];
    }

    private function getValidateDCPData($cpl)
    {
        return [
            'name' => sprintf("Validate DCP %s", $cpl['title']),
            'directory' => $cpl['directory'],
        ];
    }

    private function getDeleteCPLData($cpl)
    {
        return [
            'cpl' => $cpl['uuid'],
            'name' => sprintf("Delete CPL %s", $cpl['title']),
        ];
    }

    private function getDeleteDCPData($cpl)
    {
        return [
            'name' => sprintf("Delete DCP %s", $cpl['title']),
            'directory' => $cpl['directory'],
        ];
    }

    private function getFormatDriveData()
    {
        $drive = request()->drive;
        return [
            'name' => sprintf("Format Drive %s", $drive['location_name']),
            'drive' => $drive['device'],
            'format_type' => 'mixed',
        ];
    }

    public function updateDriveJobs()
    {
        // jobs -> media_drive_jobs
        $results = request()->post('jobs');
        /** @var MediaManager $mediaManager */
        $mediaManager = auth()->user();

        logger("updating drive jobs on {$mediaManager->id}");

        $mediaManager->fill(['media_drive_jobs' => $results]);
        $mediaManager->save();

        // match up each job with the media manager job (via the uuid)
        // and update respective statuses.
        $this->processCmmsJobs($results);
    }

    public function updateMediaLibraryJobs()
    {
        // jobs -> library_jobs
        $results = request()->post('jobs');
        /** @var MediaManager $mediaManager */
        $mediaManager = auth()->user();

        logger("updating library jobs on {$mediaManager->id}");

        $mediaManager->fill(['library_jobs' => $results]);
        $mediaManager->save();

        // find any JOB_ID's in the media manager job_ids and update status.
        $this->processCmmsJobs($results);
    }

    private function processCmmsJobs($results)
    {
        // library job is	"JOB_STATUS": "Complete",  /	"JOB_ID": 177,
        // drive job is  		"status": "completed", / 		"id": "dd8dfdd5-dd21-45cd-bcb1-7b12fa71d1bb",

        $mediaManager = auth()->user();

        foreach ($results as $job) {

            $id = $job['id'] ?? $job['JOB_ID'];

            $mediaManagerJob = $mediaManager->jobs()->where('media_manager_job_id', $id)->first();

            if (!$mediaManagerJob) {
                continue;
            }

            $status = $job['status'] ?? $job['JOB_STATUS'] ?? 'Unknown';
            if ($status == 'complete') {
                $status = 'completed';
            }

            $progress = $job['overall_percentage'] ?? $job['JOB_CURRENT_PROGRESS'] ?? 0;

            $mediaManagerJob->status = $status;
            $mediaManagerJob->progress = $progress;

            if ($status == 'completed') {
                $mediaManagerJob->progress = 100;
            }

            if ($mediaManagerJob->isDirty()) {
                logger(sprintf('Bulk Updated job: %s, %s', $mediaManagerJob->name, $mediaManagerJob->status));
                // it's possible that he job update payload contains the same jobs over and over, so only trigger
                // this if the data was actually updated.
                $mediaManagerJob->save();

                if ($status == 'completed') {
                    $this->maybeCreateValidateJob($mediaManagerJob);
                    $mediaManagerJob->cinemaSite->sendNextPendingJob();
                    $mediaManagerJob->mediaManager->getStorage();
                }
            }
        }
    }

}
