<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('bookings', function (Blueprint $table) {
            // Add a generated column for sorting
            $table->unsignedTinyInteger('status_priority')
                ->virtualAs("CASE WHEN overall_status IN ('transmitting', 'verify') 
                THEN 0 ELSE 1 END");
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('bookings', function (Blueprint $table) {
            if (Schema::hasColumn('bookings', 'status_priority')) {
                $table->dropColumn('status_priority');
            }
        });
    }
};
