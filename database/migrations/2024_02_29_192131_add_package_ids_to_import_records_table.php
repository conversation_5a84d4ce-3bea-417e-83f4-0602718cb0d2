<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('import_bookings', function (Blueprint $table) {
            //
            $table->foreignId('package_id')->after('version_ids')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('import_bookings', function (Blueprint $table) {
            //
            $table->dropColumn('package_id');
        });
    }
};
