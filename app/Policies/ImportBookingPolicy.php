<?php

namespace App\Policies;

use App\Models\ImportBooking;
use App\Models\ImportBookingRecord;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class ImportBookingPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param \App\Models\User $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user)
    {
        return true;
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param \App\Models\User $user
     * @param \App\Models\ImportBooking $importBooking
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, ImportBooking $importBooking)
    {
        //
        return $user->isAdmin() || $importBooking->user_id === $user->id;
    }

    /**
     * Determine whether the user can create models.
     *
     * @param \App\Models\User $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user)
    {
        //
        return $user->isAdmin() || $user->canBoth('create', ImportBookingRecord::class);
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param \App\Models\User $user
     * @param \App\Models\ImportBooking $importBooking
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, ImportBooking $importBooking)
    {
        //
        return $user->isAdmin() || $importBooking->user_id === $user->id;
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param \App\Models\User $user
     * @param \App\Models\ImportBooking $importBooking
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, ImportBooking $importBooking)
    {
        //
        return $user->isAdmin();

    }

    /**
     * Determine whether the user can restore the model.
     *
     * @param \App\Models\User $user
     * @param \App\Models\ImportBooking $importBooking
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function restore(User $user, ImportBooking $importBooking)
    {
        //
        return $user->isSuperAdmin();
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param \App\Models\User $user
     * @param \App\Models\ImportBooking $importBooking
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function forceDelete(User $user, ImportBooking $importBooking)
    {
        //
        return $user->isSuperAdmin();
    }
}
