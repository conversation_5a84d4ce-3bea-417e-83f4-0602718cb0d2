<?php

namespace Tests\Feature\Http\Controllers;

use App\Models\Permission;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class PermissionControllerTest extends TestCase
{
    public function test_super_admin_can_update_individual_permission() {
        $admin = User::factory()->create()->assignRole('super-admin');
        $permission = Permission::create(['name' => uniqid('permission-test')]);
        $response = $this->actingAs($admin)->putJson(route('permissions.update', $permission), ['name' => uniqid('permission-test')]);
        $response->assertSuccessful();
    }

    public function test_admin_cannot_update_individual_permission() {
        $admin = User::factory()->create()->assignRole('admin');
        $permission = Permission::create(['name' => uniqid('permission-test')]);
        $response = $this->actingAs($admin)->putJson(route('permissions.update', $permission), ['name' => uniqid('permission-test')]);
        $response->assertForbidden();
    }


    public function test_super_admin_can_see_individual_permission() {
        $admin = User::factory()->create()->assignRole('super-admin');
        $permission = Permission::create(['name' => uniqid('permission-test')]);
        $response = $this->actingAs($admin)->getJson(route('permissions.show', $permission));
        $response->assertSuccessful();
    }

    public function test_admin_cannot_see_individual_permission() {
        $admin = User::factory()->create()->assignRole('admin');
        $permission = Permission::create(['name' => uniqid('permission-test')]);
        $response = $this->actingAs($admin)->getJson(route('permissions.show', $permission));
        $response->assertForbidden();
    }

    public function test_admin_can_list_permissions()
    {
        $admin = User::factory()->create()->assignRole('admin');
        $response = $this->actingAs($admin)->getJson(route('permissions.index'));
        $response->assertSuccessful();
    }

    public function test_super_admin_can_create_permission()
    {
        $admin = User::factory()->create()->assignRole('super-admin');
        $response = $this->actingAs($admin)->postJson(route('permissions.store'), ['name' => uniqid('test')]);
        $response->assertSuccessful();
    }

    public function test_admin_cannot_create_permission()
    {
        $admin = User::factory()->create()->assignRole('admin');
        $response = $this->actingAs($admin)->postJson(route('permissions.store'), ['name' => uniqid('test')]);
        $response->assertForbidden();
    }

    public function test_user_cannot_create_permission()
    {
        $user = User::factory()->create();
        $response = $this->actingAs($user)->postJson(route('permissions.store'), ['name' => uniqid('test')]);
        $response->assertForbidden();
    }

}
