<?php

namespace Tests\Feature\Http\Controllers;

use App\Models\Organization;
use App\Models\Title;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Testing\Fluent\AssertableJson;
use Tests\TestCase;

class TitleControllerTest extends TestCase
{
    public function test_admin_can_see_titles()
    {
        $user = User::factory()->create()->assignRole('admin');
        $response = $this->actingAs($user)->getJson(route('titles.index'));
        $response->assertSuccessful();
    }

    public function test_org_user_can_see_own_titles()
    {
        $user = User::factory()->create();
        $organization = Organization::factory()->withOwner($user)->create();
        $title = Title::factory()->withOrganization($organization)->withCreator($user)->create();
        $organization->users()->save($user);

        $response = $this->actingAs($user)->getJson(route('titles.index'));

        $response->assertSuccessful();
        $response->assertJson(fn(AssertableJson $json) => $json->has('data')->etc());

    }

    public function test_admin_can_see_individual_title()
    {
        $admin = User::factory()->create()->assignRole('admin');

        $user = User::factory()->create();
        $organization = Organization::factory()->withOwner($user)->create();
        $title = Title::factory()->withOrganization($organization)->withCreator($user)->create();

        $response = $this->actingAs($admin)->getJson(route('titles.show', $title));
        $response->assertSuccessful();
    }

    public function test_org_user_cannot_see_another_org_title()
    {
        $user = User::factory()->create();
        $organization = Organization::factory()->withOwner($user)->create();
        $title = Title::factory()->withOrganization($organization)->withCreator($user)->create();

        $anotherUser = User::factory()->create();
        $anotherOrg = Organization::factory()->withOwner($anotherUser)->create();
        $anotherOrg->users()->save($anotherUser);

        $response = $this->actingAs($anotherUser)->getJson(route('titles.show', $title));
        $response->assertForbidden();
    }

    public function test_org_user_can_search()
    {
        $user = User::factory()->create();
        $organization = Organization::factory()->withOwner($user)->create();

        $title = Title::factory()->withOrganization($organization)->withCreator($user)->create();
        $organization->users()->save($user);

        $response = $this->actingAs($user)->getJson(route('titles.index', [
            'search' => $title->title,
        ]));

        $response->assertSuccessful();
        $response->assertJson(fn(AssertableJson $json) => $json->has('data')->etc());

    }

    public function test_studio_user_can_create_title()
    {
        $studio = User::factory()->create()->assignRole('studio-read-write');
        $organization = Organization::factory()->withOwner($studio)->create();
        $organization->users()->save($studio);
        $title = Title::factory()->withOrganization($organization)->withCreator($studio)->make()->toArray();
        $response = $this->actingAs($studio)->postJson(route('titles.store'), $title);
        $response->assertSuccessful();
    }

    public function test_vendor_user_cannot_create_title()
    {
        $vendor = User::factory()->create()->assignRole('vendor-read-write');
        $organization = Organization::factory()->withOwner($vendor)->create();
        $organization->users()->save($vendor);
        $title = Title::factory()->withOrganization($organization)->withCreator($vendor)->make()->toArray();
        $response = $this->actingAs($vendor)->postJson(route('titles.store'), $title);
        $response->assertForbidden();

    }

    public function test_owner_can_update_title()
    {
        $studio = User::factory()->create()->assignRole('studio-read-write');
        $organization = Organization::factory()->withOwner($studio)->create();
        $organization->users()->save($studio);
        $title = Title::factory()->withOrganization($organization)->withCreator($studio)->create();

        $response = $this->actingAs($studio)->putJson(route('titles.update', $title), $title->toArray());
        $response->assertSuccessful();
    }

    public function test_non_owner_cannot_update_title()
    {
        $studio = User::factory()->create()->assignRole('studio-read-write');
        $organization = Organization::factory()->withOwner($studio)->create();
        $organization->users()->save($studio);

        $studio2 = User::factory()->create()->assignRole('studio-read-write');
        $organization2 = Organization::factory()->withOwner($studio2)->create();
        $organization2->users()->save($studio2);

        $title = Title::factory()->withOrganization($organization)->withCreator($studio)->create();

        $response = $this->actingAs($studio2)->putJson(route('titles.update', $title), $title->toArray());
        $response->assertForbidden();

    }

    public function test_admin_can_delete_title()
    {
        $studio = User::factory()->create()->assignRole('studio-read-write');
        $organization = Organization::factory()->withOwner($studio)->create();
        $organization->users()->save($studio);

        $admin = User::factory()->create()->assignRole('admin');
        $organization2 = Organization::factory()->withOwner($admin)->create();
        $organization2->users()->save($admin);

        $title = Title::factory()->withOrganization($organization)->withCreator($studio)->create();

        $response = $this->actingAs($admin)->deleteJson(route('titles.destroy', $title));
        $response->assertSuccessful();

    }

    public function test_non_admin_cannot_delete_title()
    {
        $studio = User::factory()->create()->assignRole('studio-read-write');
        $organization = Organization::factory()->withOwner($studio)->create();
        $organization->users()->save($studio);

        $title = Title::factory()->withOrganization($organization)->withCreator($studio)->create();

        $response = $this->actingAs($studio)->deleteJson(route('titles.destroy', $title));
        $response->assertForbidden();

    }

}
