<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreUserRequest;
use App\Http\Requests\UpdateUserRequest;
use App\Models\Organization;
use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Symfony\Component\HttpFoundation\Response;
use App\Mail\UserRoleUpdated;
use Illuminate\Support\Facades\Mail;

class UserController extends Controller
{
    public function __construct()
    {
        $this->authorizeResource(User::class);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if (auth()->user()->isAdmin()) {
            $query = User::query();
        }
        else {
            $query = User::whereOrganizationId(auth()->user()->organization_id);
        }
        return $this->executeQuery($query);
    }

    /**
     * Display a listing of the users by organization
     *
     * @return \Illuminate\Http\Response
     */
    public function indexByOrganization(Organization $organization)
    {
        if (auth()->user()->cant('view', $organization)) {
            abort(Response::HTTP_FORBIDDEN);
        }
        return $this->executeQuery($organization->users());
    }

    private function executeQuery($query)
    {
        $search = request()->search;
        return $query->with('organization')->when($search, function ($q, $search) {
            $search = "%{$search}%";
            $q->where('email', 'like', $search)->orWhere('name', 'like', $search);
        })->orderBy(request()->sortBy ?? 'name', request()->sortDirection ?? 'ASC')
            ->paginate(request()->take);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreUserRequest $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreUserRequest $request)
    {
        // this will validate and reject failed posts.
        $values = $request->validated();

        if (isset($values['password'])) {
            $values['password'] = bcrypt($values['password']);
        }

        // also check if the originating user is even allowed to use the organization_id override.
        if (!isset($values['organization_id'])) {
            $values['organization_id'] = auth()->user()->organization_id;
            $organization = auth()->user()->organization;
        }
        else {
            $organization = Organization::find($values['organization_id']);
        }

        if (!$organization) {
            abort(Response::HTTP_NOT_FOUND, "Organization Not Found.");
        }

        $newUser = User::create($values);

        if (isset($values['role'])) {
            $newUser->assignRole($values['role']);
        } else {
            // todo: allow a readwrite/readonly flag to be passed on create.
            switch ($organization->type) {
                case Organization::TYPE_ADMIN:
                    $newUser->assignRole('admin');
                    break;
                case Organization::TYPE_EXHIBITOR:
                    $newUser->assignRole('exhibitor-read-write');
                    break;
                case Organization::TYPE_STUDIO:
                    $newUser->assignRole('studio-read-write');
                    break;
                case Organization::TYPE_VENDOR:
                    $newUser->assignRole('vendor-read-write');
                    break;
            }
        }

        event(new Registered($newUser));

        return response(['data' => $newUser]);

    }

    public function resend(User $user)
    {
        if (auth()->user()->isAdmin()) {
            event(new Registered($user));
        }

        return response(['success' => 'ok']);
    }

    /**
     * Display the specified resource.
     *
     * @param User $user
     * @return \Illuminate\Http\Response
     */
    public function show(User $user)
    {
        $user->append(['activities', 'changes']);
        return response(['data' => $user]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \App\Http\Requests\UpdateUserRequest $request
     * @param User $user
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateUserRequest $request, User $user)
    {
        $values = $request->validated();

        $oldRole = $user->role;

        if (isset($values['password'])) {
            $values['password'] = bcrypt($values['password']);
            auth()->user()->activityWithIp('Changed Password', 'password');
            // any other user data that is changed will be logged as a modified User model.
        }
        if (isset($values['role'])) {
            if ($user->hasRole('super-admin')) {
                abort(response()->json([
                    'message' => 'Unable to change role of Super Admin',
                ], 403));
            }
            $newRole = $values['role'];
            $user->roles()->detach();
            $user->assignRole($newRole);
            if ($newRole != $oldRole) {
                Mail::to($user->email)->send(new UserRoleUpdated($user, $newRole));
                auth()->user()->roleChangeActivity('Changed user role', 'Updated', [
                    'old' => ['role' => $oldRole],
                    'attributes' => ['role' => $newRole],
                ], $user);

            }
        }

        $user->update($values);
        return response(['data' => $user]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param User $user
     * @return \Illuminate\Http\Response
     */
    public function destroy(User $user)
    {
        if (auth()->user()->id === $user->id) {
            abort(Response::HTTP_UNPROCESSABLE_ENTITY, 'No self deleting.');
        }

        return response($user->delete());
    }

    public function restore($id)
    {
        // this one needs to be manually checked because the model is unknown until here.
        $user = User::findOrFail($id)->withTrashed();
        if (auth()->user()->cant('restore_users', $user)) {
            abort(Response::HTTP_FORBIDDEN);
        }

        $user->restore();
        return response(['data' => $user]);
    }
}
