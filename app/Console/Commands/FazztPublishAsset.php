<?php

namespace App\Console\Commands;

use App\Fazzt\Json;
use Illuminate\Console\Command;

class FazztPublishAsset extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fazzt:publish {uuid}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Publish a DCP by its AssetMap UUID';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        \App\Fazzt\DCP\Pubish::exec($this->argument('uuid'));

        return Command::SUCCESS;
    }
}
