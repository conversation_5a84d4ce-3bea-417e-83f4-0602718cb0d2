<?php

namespace App\Traits;

use App\Actions\Slack\Notify;
use App\Jobs\UpdateMediaLibrary;
use App\Models\CinemaSite;
use App\Models\MediaManager;
use GuzzleHttp\Client;
use Carbon\Carbon;
use Str;

trait HasStorage
{
    protected $tunnel = 'tunnel.bitcine.com';
    protected $cacheTTL = 300; // 5 minute, should update via job manager more often when data actually changes.

    private function getCacheTTL()
    {
        if (config('app.debug')) {
            return 0;
        }
        return $this->cacheTTL;
    }

    public function getStorage($fromJob = false)
    {
        if (!isset($this->web_port)) {
            return;
        }
        $this->fetchMediaLibrary($fromJob);
        $this->fetchDrives($fromJob);
        // $this->getSystemSettings();
    }

    public function getWebURL($path = '')
    {
        if (!$this->web_port) {
            return null;
        }
        return "http://{$this->tunnel}:{$this->web_port}{$path}";
    }

    public function fetchMediaLibrary($fromJob = false)
    {
        $mediaLibrary = $this->media_library ?? [];

        try {
            $url = $this->getWebURL("/media-library");
            if (!$fromJob && $url) {
                UpdateMediaLibrary::dispatch($this->id);
            }
            if (!$fromJob || !$url) {
                return $mediaLibrary;
            }

            $client = new Client();
            $response = $client->get($url, [
                'connect_timeout' => 1,
                'timeout' => $fromJob ? 300 : 30, // allow jobs to run longer. UI has a 30 second time out.
            ]);
            $rawMediaLibrary = json_decode($response->getBody());
            if (is_array($rawMediaLibrary)) {
                $this->media_library = $rawMediaLibrary;
                $this->save();
            }
            $mediaLibrary = $rawMediaLibrary;
        }
        catch (\Exception $e) {
            logger("Fetch of Media library failed: " . $e->getMessage());
        }
        return $mediaLibrary;
    }

    public function fetchDrives($fromJob = false)
    {
        try {
            $url = $this->getWebURL("/drives");
            if (!$url) {
                return $this->media_drives;
            }

            if ($fromJob) {
                cache()->forget(Str::slug($url));
            }

            return cache()->remember(Str::slug($url), $this->getCacheTTL(), function () use ($url, $fromJob) {
                $client = new Client();
                $response = $client->get($url, [
                    'connect_timeout' => 1,
                    'timeout' => $fromJob ? 120 : 30, // allow jobs to run longer. UI has a 30 second time out.
                ]);
                $rawDrives = json_decode($response->getBody());
                if (is_array($rawDrives)) {
                    $this->media_drives = $rawDrives;
                    $this->save();
                }
                return $this->media_drives;
            });
        }
        catch (\Exception $e) {
            logger("fetch drives failed: " . $e->getMessage());
            // TODO: Some indication on the dashboard that the data is stale
        }
    }

    public function isOnline()
    {
        if ($this->status_updated_at && $this->status_updated_at > now()->subMinute(2)) {
            return true;
        }

        try {
            if (!$this->getWebURL("/ping")) {
                return false;
            }

            $response = (new Client())->get($this->getWebURL("/ping"), [
                'connect_timeout' => 1,
                'timeout' => 30,
            ]);
            $online = $response->getStatusCode() === 200;
            if ($online) {
                $site = $this->cinemaSite()->where('status', \App\Models\Enum\SiteStatus::Fault)->first();
                if ($site) {
                    $site->status = \App\Models\Enum\SiteStatus::Online;
                    $site->save();

                    $url = config('app.frontend_url') . '/sites/' . $this->cinemaSite->id . '/general';
                    $slack = Notify::quickMessage(":heavy_check_mark: <$url|{$site->name}> has recovered from fault.");

                }
                $this->status_updated_at = now();
                $this->save();
            }
            return $online;
        }
        catch (\Exception $e) {
            logger("is online failed to check {$this->serial_number}: " . $e->getMessage());
            return false;
        }
    }

    public function getMediaLibraryAttribute($cpls)
    {
        if (!isset($cpls)) {
            return [];
        }

        $cpls = json_decode($cpls);

        $results = [];
        foreach ($cpls as $cpl) {
            // force assoc array for it to work.
            $results[] = $this->formatLibraryCPL(json_decode(json_encode($cpl), true));
        }

        $results['cpls'] = $results;
        $results['disk_usage'] = $this->disk_usage;

        return $results;
    }

    public function getMediaDrivesAttribute($drives)
    {
        if (!isset($drives)) {
            return [];
        }

        $drives = json_decode($drives, true);

        $results = [];

        foreach ($drives as $drive) {
            if ($drive['location_name'] === 'UNKNOWN') {
                continue;
            }
            $freeSpace = 0;
            foreach ($drive['partitions'] as &$partition) {
                $cpls = [];
                foreach ($partition['dcps'] as $dcp) {
                    foreach ($dcp['cpls'] as $cpl) {
                        $cpls[$cpl['uuid']] = $this->formatDriveCPL($dcp, $cpl);
                    }
                }
                $partition['cpls'] = $cpls;
                $freeSpace += $partition['mount_point']['free'] ?? 0;
            }
            $drive['used'] = $drive['size'] - $freeSpace;
            $results[] = $drive;
        }
        return $results;
    }

    public function getSettingsAttribute()
    {
        return [
            'email_notifications' => [],
            'network' => [],
            'raid_drives' => [],
            'storage' => [],
            'system' => [],
        ];
    }

    private function formatLibraryCPL($cpl)
    {
        $isValidating = is_null($cpl['is-corrupt']);
        $isCorrupt = intval($cpl['is-corrupt']) !== 0;
        $isComplete = intval($cpl['is-complete']) === 1;

        $isValid = !$isValidating && !$isCorrupt && $isComplete;
        $error = $cpl['error-str'];

        $message = "Awaiting Validation";
        $color = "gray";
        $icon = "access_time";
        if ($isValid) {
            $message = "Valid";
            $color = "green";
            $icon = "check_circle";
        }
        else if ($isValidating) {
            $message = "Validating";
            $color = "orange";
            $icon = "refresh";
        }
        else if (!$isValid && $error) {
            $message = "Not Valid";
            $color = "red";
            $icon = "error_outline";
        }

        return [
            'id' => $cpl['id'],
            'uuid' => $cpl['id'],
            'is_smpte' => intval($cpl['is-smpte']) === 1,
            'kind' => strtolower($cpl['kind']),
            'size' => $cpl['size-bytes'],
            'title' => $cpl['title'],
            'timecode' => $this->convertFramesToTimecode($cpl['duration'], $cpl['edit-rate']),
            'is_encrypted' => intval($cpl['is-encrypted']) === 1,
            // 'is_removing' => in_array($cpl['id'], $removingUUIDs),
            'validation_error' => $cpl['error-str'],
            'status' => [
                'message' => $message,
                'color' => $color,
                'icon' => $icon,
                'is_animated' => $message === "Validating",
            ],
            'is_valid' => $isValid,
            'is_transfer' => false,
            'is_queued' => false,
            'frame_rate' => explode('/', $cpl['edit-rate'])[0],
            // 'color' => $libraryCPLColors[$cpl['id']] ?? null
        ];
    }

    private function formatDriveCPL($dcp, $cpl)
    {
        // TODO: We can add this later when jobs are present
        $validatingJobExists = false;
        $deletingCPLJobExists = false;
        $deletingDCPJobExists = false;

        $isEncrypted = ($cpl['is_encrypted'] == 1);
        $keyCount = $cpl['key_count'];
        $keyText = 'Yes (' . $keyCount . ($keyCount === 1 ? ' key)' : ' keys)');

        $multiCPL = (count($dcp['cpls']) > 1);

        $isValid = ($dcp['valid'] ?? 0) === 1;
        $isValidating = ($dcp['validating'] === 1 || $validatingJobExists);
        $error = $dcp['validation_error'] ?? '';

        $message = "Awaiting Validation";
        $color = "gray";
        $icon = "access_time";
        if ($isValid) {
            $message = "Valid";
            $color = "green";
            $icon = "check_circle";
        }
        else if ($isValidating) {
            $message = "Validating";
            $color = "orange";
            $icon = "refresh";
        }
        else if (!$isValid && $error) {
            $message = "Not Valid";
            $color = "red";
            $icon = "error_outline";
        }

        return [
            'title' => $cpl['title_text'],
            'dci' => ($cpl['dci'] === 1),
            'size' => $cpl['size_b'],
            'uuid' => $cpl['uuid'],
            'is_encrypted' => $isEncrypted,
            'is_removing' => $deletingCPLJobExists || $deletingDCPJobExists,
            'timecode' => $this->convertFramesToTimecode($cpl['duration'], $cpl['frame_rate']),
            'kind' => strtolower($cpl['kind']),
            'width' => $cpl['width'],
            'height' => $cpl['height'],
            'frame_rate' => explode('/', $cpl['frame_rate'])[0],
            'directory' => $dcp['directory'],
            'status' => [
                'message' => $message,
                'color' => $color,
                'icon' => $icon,
                'is_animated' => $message === "Validating",
            ],
            'is_valid' => $isValid,
            'is_transfer' => false,
            'validation_error' => $error,
            'dcp_title' => $dcp['text'] ?? $cpl['title_text'],
            'is_multi_cpl' => $multiCPL,
            'aspect_ratio' => $cpl['aspect_ratio'],
            'reel_count' => $cpl['reel_count'],
            'key_count' => $cpl['key_count'],
            'issuer' => $cpl['issuer'],
            'issue_date' => $cpl['issue_date'],
            'creator' => $cpl['creator'],
            'dimension' => $cpl['is_3d'] === 1 ? '3D' : '2D',
            'encrypted' => $isEncrypted ? $keyText : 'No',
        ];
    }

    private function convertFramesToTimecode($frames, $editRate)
    {
        $frameRate = explode('/', $editRate)[0];

        if (!is_numeric($frames) || !is_numeric($frameRate)) {
            return "";
        }

        $hours = floor($frames / ($frameRate * 60 * 60));
        $framesleft = $frames - ($hours * $frameRate * 60 * 60);
        $minutes = floor($framesleft / ($frameRate * 60));
        $framesleft -= ($minutes * $frameRate * 60);
        $seconds = floor($framesleft / ($frameRate));
        $framesleft -= ($seconds * $frameRate);
        $tc = sprintf("%02d:%02d:%02d:%02d", $hours, $minutes, $seconds, $framesleft);

        return $tc;
    }
}
