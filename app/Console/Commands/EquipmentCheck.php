<?php

namespace App\Console\Commands;

use App\Actions\Slack\Notify;
use App\Models\CinemaSite;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;

class EquipmentCheck extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'equipment:check';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Do an equipment check and set sites to faulted if their equipment hasn\'t called home.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $offlineSites = CinemaSite::where('status', \App\Models\Enum\SiteStatus::Fault)
            ->whereNot('circuit', 'warehouse')
            ->with(['primaryMediaManager'])->get();

        /** @var CinemaSite $site */
        foreach ($offlineSites as $site) {
            if (! $site->primaryMediaManager) {
                continue;
            }

            // ping it, it'll fix itself.
            $site->primaryMediaManager->isOnline();
        }

        $onlineSites = CinemaSite::where('status', \App\Models\Enum\SiteStatus::Online)
            ->whereNot('circuit', 'warehouse')
            ->with(['primaryMediaManager'])->get();

        foreach ($onlineSites as $site) {
            /** @var Carbon $time */
            if (! $site->primaryMediaManager) {
                // site has no MM, they should probably be "pending" still?
                continue;
            }
            $time = $site->primaryMediaManager->status_updated_at;

            if ($time !== null) {
                $hourAgo = now()->subHour(1);

                if ($time < $hourAgo) {
                    // before flagging it as fault, try and ping it this way (so perhaps the heartbeat is slow)
                    if (! $site->primaryMediaManager->isOnline()) {
                        $site->status = \App\Models\Enum\SiteStatus::Fault;
                        $site->save();

                        // ping slack. poor site.
                        $url = config('app.frontend_url') . '/sites/' . $site->id . '/general';
                        $slack = Notify::quickMessage(":fire: <$url|{$site->name}> has gone into fault.");
                    }
                }
            }
        }

        return 0;
    }
}
