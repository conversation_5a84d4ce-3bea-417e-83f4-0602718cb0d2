<?php

namespace App\Fazzt\DCP;

use App\Fazzt\Exception;
use App\Fazzt\Json;
use Illuminate\Console\Command;

class Status
{
    /**
     *
     * map p;
     * p["method"] = "CinemaDCPIngestStatus";
     * p["params"][0] = entrtyuuid; <-- from the submit dcp call
     * p["id"] = id;
     *
     * map post;
     * // We construct map p and format it into JSON string.
     * post["Data"] = ValueToJSONString(p);
     * post["Headers"] = "Content-Type: application/json\r\n\r\n";
     * post["URL"] = "http://127.0.0.1:4039/ws/json-rpc/InvokeFazztFunction.fzt";
     */

    static function exec($uuid)
    {
        $params = [
            0 => $uuid,
        ];
        try {
            $response = Json::jsonCall(
                'CinemaDCPIngestStatus',
                null,
                $params,
                false);
            return $response;
        }
        catch (\Exception $e) {
            logger($e->getMessage());
        }

        return false;

    }
}
