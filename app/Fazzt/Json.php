<?php

namespace App\Fazzt;

use App\Fazzt\Exception;
use App\Models\FazztRequestLog;

class Json
{
    /**
     * @throws Exception
     */
    static function jsonCall(string $method, string|null $include = null, array $params = [], $logAction = false, $args = null)
    {

        // todo: figure out actual authentication, this does nothing.
        $username = config('fazzt.username');
        $password = config('fazzt.password');

        if ($include) {
            $url = sprintf(config('fazzt.remote_url') . "InvokeFazztFunction.fzt?includes=%s.fzt", $include);
        }
        else {
            $url = config('fazzt.remote_url') . "InvokeFazztFunction.fzt";
        }

        $body['method'] = $method;
        $body['params'] = $params;

        if ($args && is_array($args)) {
            foreach ($args as $key => $value) {
                $body[$key] = $value;
            }
        }

        $body['id'] = time(); // this needs to be a unique id, seems meaningless but it won't respond without it.

        // hit the endpoint with the method
        $client = new \GuzzleHttp\Client();
        $response = $client->post($url,
            [
                \GuzzleHttp\RequestOptions::JSON => $body,
            ]);

        $response = json_decode($response->getBody()->getContents(), JSON_OBJECT_AS_ARRAY);

        if ($logAction && $response) {
            // shove it all into the table for future validations.
            FazztRequestLog::create([
                'include_file' => $include,
                'method' => $method,
                'payload' => $params,
                'response_code' => $response['result'],
                'is_completed' => false,
            ]);
        }

        return $response;
    }
}
