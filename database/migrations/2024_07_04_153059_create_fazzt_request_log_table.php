<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('fazzt_request_log', function (Blueprint $table) {
            $table->id();

            $table->string('include_file')->nullable(); // something like CinemaPro.fzt
            $table->string('method'); // what is actually being called on the file.

            $table->json('payload')->nullable(); // what we sent.

            $table->json('site_ids')->nullable(); // where it's gong? blank for everyone!

            $table->uuid('response_code')->nullable(); // this is what fazzt sends back to track the command
            $table->boolean('is_completed')->default(false); // whether nor not we got a completed ack via callback

            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('fazzt_request_log');
    }
};
