<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('fazzt_transfers', function (Blueprint $table) {
            $table->id();

            $table->uuid('transmit_id');
            $table->uuid('asset_uuid');

            $table->json('site_ids')->nullable();

            $table->integer('duration')->default(0);
            $table->integer('percent')->default(0);

            $table->dateTime('start_time');

            $table->string('status')->default('PENDING');
            $table->string('state')->default('PENDING');

            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('fazzt_transfers');
    }
};
