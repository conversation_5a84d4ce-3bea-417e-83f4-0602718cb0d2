<?php

return [
    /*
     * The webhook URLs that we'll use to send a message to Slack.
     */
    'webhook_urls' => [
        'default' => env('SLACK_ALERT_WEBHOOK', '*******************************************************************************'),
    ],
    'default_channel' => env('SLACK_DEFAULT_CHANNEL', '#dcdc-staging'),

    /*
     * This job will send the message to Slack. You can extend this
     * job to set timeouts, retries, etc...
     */
    'job' => <PERSON><PERSON>\SlackAlerts\Jobs\SendToSlackChannelJob::class,
];
