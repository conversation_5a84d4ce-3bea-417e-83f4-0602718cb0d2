<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreDeliveryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'organization_id' => ['required', 'numeric'],
            'cinema_site_id' => ['required', 'numeric'],
            'version_id' => ['required', 'numeric'],
            'deliver_at' => ['required', 'date'],
            'is_electronic' => ['required', 'boolean']
        ];
    }
}
