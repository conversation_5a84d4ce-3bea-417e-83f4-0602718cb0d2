<?php

$url = "https://local-api.cinesend.com.test";
$disneyUrl = "http://mockbin.org/bin/d0b38ebe-bd3f-4ff4-90de-0525695adf07";

if (env('APP_ENV') === 'production') {
    $url = "https://api.cinesend.com";
    $disneyUrl = "https://webvan.disney.com:443/studio/ofe/v1/orders";
}
else if (env('APP_ENV') === 'staging') {
    $url = "https://staging-api.cinesend.com";
    $disneyUrl = "https://webvan.disney.com:8443/studio/stage/ofe/v1/orders";
}

return [
    'download_job_url' => "{$url}/api/media-managers/",

    // any studio's specific notify URLs that may need to be per ENV:
    'disney_notify_url' => $disneyUrl,

    // these are strings that need to be saved to files on lambda when needed.
    // the string limit is 4k, so they might need to be split when saved to vapor secrets.
    // vapor secrets are stored in SSM by default
    'ssl_key' => env('SSL_KEY'),
    'ssl_certificate_chain_1' => env('SSL_CERTIFICATE_CHAIN_1'),
    'ssl_certificate_chain_2' => env('SSL_CERTIFICATE_CHAIN_2'),
    'ssl_certificate_chain_3' => env('SSL_CERTIFICATE_CHAIN_3'),
    'ssl_certificate' => env('SSL_CERTIFICATE'),

    'paramount_user_id' => env('PARAMOUNT_USER_ID', 1),
    'paramount_org_id' => env('PARAMOUNT_ORG_ID', 1),

    'warnerbros_user_id' => env('WARNERBROS_USER_ID', 1),
    'warnerbros_org_id' => env('WARNERBROS_ORG_ID', 1),

    'lionsgate_user_id' => env('LIONSGATE_USER_ID', 1),
    'lionsgate_org_id' => env('LIONSGATE_ORG_ID', 1),

];
