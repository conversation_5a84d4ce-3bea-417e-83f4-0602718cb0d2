<?php

namespace App\DcHub;

use Carbon\Carbon;
use SoapFault;

class Requests
{
    /*
    Soap calls:
        getRequests(credentials $credentials, dateTime $startDate, dateTime $endDate, string $apiVersion)
        addRequests(credentials $credentials, base64Binary $addRequestMessage, string $apiVersion)
        getRequestsByDistributor(credentials $credentials, dateTime $startDate, dateTime $endDate, string $distributorId, string $apiVersion)
        updateRequestStatus(credentials $credentials, string $codeShipping, string $codeStatus, string $onHoldDescription, string $apiVersion)
        updateRequestStatusMetaData(credentials $credentials, string $codeShipping, base64Binary $metaData, string $apiVersion)
        rerouteRequest(credentials $credentials, string $codeShipping, string $codeDepotTo, string $apiVersion)
    */

    /**
     * @throws SoapFault
     */
    static function getRequests($onlyNew = false)
    {
        // override start/end with proper dates to pull existing/previously exported requests.
        // to clarify: all NEW requests must be fetched with the 2050 date - at which point they will be
        // automatically converted to 'received by vendor' status
        // after that, we can work with ones that are only in the past week...

        if ($onlyNew) {
            $startDate = Carbon::create('2050-01-01');
            $endDate = Carbon::create('2050-01-01');
        }
        else {

            // Let's grab requests booked for the next few months?
            $startDate = Carbon::today();
            $endDate = Carbon::today()->addMonth(5);
        }

        $requests = Soap::soapCall('getRequests',
            [
                'startDate' => $startDate->toDateTimeLocalString(),
                'endDate' => $endDate->toDateTimeLocalString(),
            ]
        );

        $requests = simplexml_load_string($requests->xml);
        $results = [];

        foreach ($requests as $request) {
            $results[] = (array)$request;
        }

        return $results;
    }

    static function updateRequestStatus($requestID, $newStatus)
    {
        $result = Soap::soapCall('updateRequestStatus',
            [
                'codeShipping' => $requestID,
                'codeStatus' => $newStatus['code'],
                'onHoldDescription' => $newStatus['label']
            ]
        );
        return $result->code === 1;
    }

    static function rerouteRequestToDeluxe($requestID)
    {
        return self::rerouteRequest($requestID, config('dchub.deluxe_vendor_id'));
    }

    static function rerouteRequest($requestID, $newVendorID)
    {
        $result = Soap::soapCall('rerouteRequest',
            [
                'codeShipping' => $requestID,
                'codeDepotTo' => $newVendorID,
            ]
        );

        if (isset($result->xml)) {
            $result = simplexml_load_string($result->xml);
        }
        else {
            return false;
        }

        return $result;
    }

    static function updateRequestMetadata($requestID, $metaData)
    {
        /* generate this xml, base64 encode it, and use it as the metaData param. */

        $xmlFiller = '';

        // only send actual values.
        foreach ($metaData as $key => $value) {
            if ($value) {
                $xmlFiller .= sprintf("<%s>%s</%s>\n", $key, $value, $key);
            }
        }

        $metaDataXML = <<< EOT
<?xml version="1.0" encoding="UTF-8"?>
<RequestStatusMetaDataMessage xmlns="http://www.maccs.com/2020/04/RequestStatusMetaDataMessage">
$xmlFiller
</RequestStatusMetaDataMessage>
EOT;

        $result = Soap::soapCall('updateRequestStatus',
            [
                'codeShipping' => $requestID,
                'metaData' => base64_encode($metaDataXML),
            ]
        );

        if (isset($result->xml)) {
            $result = simplexml_load_string($result->xml);
        }
        else {
            return false;
        }

        return $result;

    }
}
