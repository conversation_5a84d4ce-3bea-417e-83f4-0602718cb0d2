<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('cinema_sites', function (Blueprint $table) {
            $table->string('primary_contact_name')->nullable();
            $table->string('primary_contact_number')->nullable();
            $table->string('primary_contact_email')->nullable();
            $table->string('secondary_contact_name')->nullable();
            $table->string('secondary_contact_number')->nullable();
            $table->string('secondary_contact_email')->nullable();
            $table->string('secondary_tcn')->nullable();
            $table->string('sage_customer_number')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('cinema_sites', function (Blueprint $table) {
            $table->dropColumn('primary_contact_name');
        });
        Schema::table('cinema_sites', function (Blueprint $table) {
            $table->dropColumn('primary_contact_number');
        });
        Schema::table('cinema_sites', function (Blueprint $table) {
            $table->dropColumn('primary_contact_email');
        });
        Schema::table('cinema_sites', function (Blueprint $table) {
            $table->dropColumn('secondary_contact_name');
        });
        Schema::table('cinema_sites', function (Blueprint $table) {
            $table->dropColumn('secondary_contact_number');
        });
        Schema::table('cinema_sites', function (Blueprint $table) {
            $table->dropColumn('secondary_contact_email');
        });
        Schema::table('cinema_sites', function (Blueprint $table) {
            $table->dropColumn('secondary_tcn');
        });
        Schema::table('cinema_sites', function (Blueprint $table) {
            $table->dropColumn('sage_customer_number');
        });
    }
};
