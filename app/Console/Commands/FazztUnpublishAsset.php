<?php

namespace App\Console\Commands;

use App\Fazzt\Json;
use Illuminate\Console\Command;

class FazztUnpublishAsset extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fazzt:unpublish {uuid}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Unublish a DCP by its AssetMap UUID';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        \App\Fazzt\DCP\Unpublish::exec($this->argument('uuid'));

        return Command::SUCCESS;
    }
}
