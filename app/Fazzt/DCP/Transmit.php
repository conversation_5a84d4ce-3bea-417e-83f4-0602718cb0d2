<?php

namespace App\Fazzt\DCP;

use App\Fazzt\Exception;
use App\Fazzt\Json;
use App\Models\FazztTransfer;
use Illuminate\Console\Command;

class Transmit
{

    static function exec($file)
    {
        // transmit an array of files, this needs to know the
        // local path name of the FPK on the Fazzt server.
        $params = [
            [
                'File' => [$file],
                'QueueName' => 'Channel 1 Preprocess', // TODO: configure channel.
                'TransmissionModeName' => 'With Reports',
            ],
        ];
        try {
            $response = Json::jsonCall(
                'CinemaDCPPackageTransmit',
                null,
                $params,
                false);
            /**
             * "result" => "9A228D5C-B6CC-4E5D-8E27-D7E60E329F8F"
             * "error" => null
             * "id" => 1723735583
             */
            return $response;
//            dump($response);
//            $ft = FazztTransfer::create(
//                [
//                    'transmit_id' => $response['result'],
//                    'status' => 'PENDING',
//                ]
//            );
        } catch (\Exception $e) {
            logger($e->getMessage());
        }

        return false;

    }
}
