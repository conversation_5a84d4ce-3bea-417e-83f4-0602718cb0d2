<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Foundation\Http\Middleware\TransformsRequest;

class PageParameterShift extends TransformsRequest
{
    public function handle($request, Closure $next)
    {
        return parent::handle($request, $next);
    }

    /**
     * The page value from the Theme table is 0 based.
     * The page value for laravel paginate() expects it to be 1 based.
     * So we'll shift all incoming values to match what <PERSON><PERSON> expects.
     *
     * @param string $key
     * @param mixed $value
     * @return mixed
     */
    protected function transform($key, $value)
    {
        if ($key === 'page' && intval($value)) {
            return $value + 1;
        }
        return $value;
    }
}

