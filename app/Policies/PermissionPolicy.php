<?php

namespace App\Policies;

use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;
use Spatie\Permission\Models\Permission;

class PermissionPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param \App\Models\User $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user)
    {
        // only admin and org owners can view the list for assignment purposes
        // but this is still restricted to the inherited permissions of the organization
        // ie: an org can't see "create orders" if they can't actually do it...
        return $user->isAdmin() || $user->organization->owner_id === $user->id;
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param \App\Models\User $user
     * @param Permission $organization
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, Permission $organization)
    {
        return $user->isSuperAdmin();
    }

    /**
     * Determine whether the user can create models.
     *
     * @param \App\Models\User $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user)
    {
        return $user->isSuperAdmin();
    }

    /**
     * Determine whether the user can update the model.
     * Note: Changing the name of a permission can have implications to code. Use with caution.
     *
     * @param \App\Models\User $user
     * @param Permission $organization
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, Permission $organization)
    {
        return $user->isSuperAdmin();
    }

}
