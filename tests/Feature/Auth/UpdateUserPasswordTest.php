<?php

namespace Tests\Feature\Auth;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class UpdateUserPasswordTest extends TestCase
{

    public function test_users_can_update_password()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->putJson('/user/password', [
            'current_password' => 'testing123',
            'password' => 'newpassword',
            'password_confirmation' => 'newpassword',
        ]);

        $this->assertAuthenticated();
        $response->assertSuccessful();
    }

    public function test_user_provides_wrong_current_password()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->putJson('/user/password', [
            'current_password' => 'wrong-password',
            'password' => 'newpassword',
            'password_confirmation' => 'newpassword',
        ]);

        $response->assertUnprocessable();
    }

    public function test_user_mismatches_password()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->put<PERSON>son('/user/password', [
            'current_password' => 'password',
            'password' => 'newpassword',
            'password_confirmation' => 'mismatched',
        ]);


        $response->assertUnprocessable();
    }

}
