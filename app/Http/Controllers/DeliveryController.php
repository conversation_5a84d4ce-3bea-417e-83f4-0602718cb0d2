<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreDeliveryRequest;
use App\Jobs\UpdateTransferCounts;
use App\Http\Requests\{UpdateDeliveryRequest, UpdateElectronicDeliveryRequest};
use App\Models\{MediaManagerJob, Delivery, CinemaSite, Booking, MediaManagerSpeedSummary, Title, Version};
use App\Models\Enum\BookingStatus;
use Illuminate\Http\Response;

class DeliveryController extends Controller
{
    public function __construct()
    {
        $this->authorizeResource(Delivery::class);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return $this->executeQuery(Delivery::query());
    }

    public function indexByOrganization()
    {
        // $this->authorize('view orders');
        return $this->executeQuery(auth()->user()->organization->deliveries());
    }

    // public function indexByTitle(Title $title)
    // {
    //     // $this->authorize('view orders');
    //     return $this->executeQuery($title->deliveries());
    // }

    // public function indexByCinemaSite(CinemaSite $cinemaSite)
    // {
    //     // $this->authorize('view orders');
    //     return $this->executeQuery($cinemaSite->deliveries());
    // }

    public function indexByBooking(Booking $booking)
    {
        // $this->authorize('view orders');
        return $this->executeQuery($booking->deliveries());
    }

    private function executeQuery($query)
    {
        return $query
            ->select([
                'id',
                'created_at',
                'progress',
                'status',
                'speed_in_mbps',
                'booking_id',
                'version_id',
                'package_id',
                'title_id',
                'cinema_site_id',
                'is_electronic',
                'organization_id',
            ])
            ->with(['booking', 'release', 'cinema:id,name,disney_site_id,tcn,circuit,address', 'title', 'version', 'organization'])
            // ->when(request()->get('search'), fn($q) => $q->where('version.title.friendly_title', request()->get('search')))
            ->when(request()->get('status'), fn($q) => $q->whereIn('status', explode(',', request()->get('status'))))
            ->orderBy(request()->sortBy ?? 'created_at', request()->sortDirection ?? 'DESC')
            ->paginate(request()->take);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \App\Http\Requests\StoreDeliveryRequest $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreDeliveryRequest $request)
    {
        $version = Version::find($values['version_id']);
        $values = $request->validated();
        // $values['creator_id'] = auth()->user()->id;
        $values['organization_id'] = auth()->user()->organization_id;
        $values['version_id'] = $version->id;
        $values['title_id'] = $version->title_id;
        $values['status'] = BookingStatus::Pending;
        $newDelivery = Delivery::create($values);
        return $this->show($newDelivery);
    }

    /**
     * Display the specified resource.
     *
     * @param \App\Models\Delivery $delivery
     * @return \Illuminate\Http\Response
     */
    public function show(Delivery $delivery)
    {
        $delivery->load(['version', 'version.title', 'cinema', 'creator']);
        return response(['data' => $delivery]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \App\Http\Requests\UpdateDeliveryRequest $request
     * @param \App\Models\Delivery $delivery
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateDeliveryRequest $request, Delivery $delivery)
    {
        $delivery->updateData($request->validated());
        return $this->show($delivery);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \App\Http\Requests\UpdateElectronicDeliveryRequest $request
     * @param \App\Models\Delivery $delivery
     * @return \Illuminate\Http\Response
     */
    public function updateElectronic(UpdateElectronicDeliveryRequest $request, Delivery $delivery, MediaManagerJob $job = null)
    {
        $values = $request->validated();

        $delivery->updateData($values);

        // also update any matching e-deliveries for the exact same content and exact same site to prevent double deliveries.
        Delivery::where('cinema_site_id', $delivery->cinema_site_id)
            ->where('title_id', $delivery->title_id)
            ->where('version_id', $delivery->version_id)
            ->where('package_id', $delivery->package_id)
            ->where('status', '<>', 'completed') // no touching old completed ones!
            ->get()
            ->each
            ->updateData($values);

        if (!$job) {
            $job = MediaManagerJob::where('delivery_id', $delivery->id)->first();
        }

        if ($job) {
            $job->update($values);
            if ($request->has('speed_in_mbps') && $request->speed_in_mbps > 0) {
                if ($site = $job->cinemaSite) {
                    // save this for now for historic purposes and to be able to regen the sumary table.
                    $site->downloadSpeeds()->create([
                        'is_aspera_transfer' => true,
                        'download_speed' => $request->speed_in_mbps,
                        'media_manager_id' => $job->media_manager_id,
                    ]);
                    // shove it into the summary table for this
                    // YYYY DoY H - find the record, set the min/max, add to tally, incrememnt count.
                    $summary = MediaManagerSpeedSummary::firstOrCreate([
                        'year' => now()->year,
                        'day_of_year' => now()->dayOfYear,
                        'hour_of_day' => now()->hour,
                        'media_manager_id' => $job->media_manager_id,
                        'cinema_site_id' => $job->cinema_site_id,
                    ]);

                    $summary->updateCurrentRecord($request->speed_in_mbps);
                }
            }
        }

        // this update gets hit a lot per transfer, let's only update the transfer counts every minute for each booking.
        cache()->remember("transfer_counts_booking_{$delivery->id}", 60, function () use ($delivery) {
            if ($delivery->booking) {
                UpdateTransferCounts::dispatch($delivery->booking);
            }
            return true;
        });

        return $this->show($delivery);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\Models\Delivery $delivery
     * @return \Illuminate\Http\Response
     */
    public function destroy(Delivery $delivery)
    {
        return response($delivery->delete());
    }

    // /**
    //  * Bulk update one or many Deliveries with a new status. This will use the same update validation with a
    //  * check for delivery_ids as array, we'll still have to do a check to make sure the user can update each ID
    //  * passed in though.
    //  * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\Routing\ResponseFactory|Response
    //  */
    // public function bulkUpdateStatuses(UpdateDeliveryRequest $request)
    // {
    //     // a simple validation for array & a valid status.
    //     $values = $request->validated();

    //     $ids = $values['delivery_ids'];
    //     $deliveries = Delivery::whereIn('id', $ids)->get();

    //     // this should be safe via validation. if the value is actually invalid it will throw an exception.
    //     $status = BookingStatus::from($values['status']);

    //     $updatedIds = [];
    //     $rejectedIds = [];
    //     foreach ($deliveries as $delivery) {
    //         if (auth()->user()->canBoth('update', $delivery)) {
    //             $delivery->status = $status;
    //             $delivery->save();
    //             $updatedIds[] = $delivery->id;
    //         }
    //     }

    //     $rejectedIds = array_diff($ids, $updatedIds);

    //     return response([
    //         'updated_ids' => $updatedIds,
    //         'rejected_ids' => $rejectedIds,
    //     ]);
    // }

    public function sendDownloadJob(Delivery $delivery)
    {
        if ($delivery->is_electronic) {
            // check and see if there is a job already queued or running
            // allow for a new one after a completed (resend)
            // but don't allow stacking when something is in progress or waiting.
            $result = $delivery->sendDownloadJob();
        }

        return response()->json(['success' => $result]);
    }
}
