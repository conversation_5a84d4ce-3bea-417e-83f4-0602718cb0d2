<?php

namespace App\Fazzt\DCP;

use App\Fazzt\Exception;
use App\Fazzt\Json;
use Illuminate\Console\Command;

class Retransmit
{

    static function exec($uuid)
    {

        $params = [$uuid];

        try {
            $response = Json::jsonCall(
                'CinemaDCPPackageRetransmit',
                null,
                $params,
                false);
            // dump($response);
        }
        catch (\Exception $e) {
            logger($e->getMessage());
        }

        return false;

    }
}
