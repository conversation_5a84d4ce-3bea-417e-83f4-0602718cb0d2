<?php

namespace App\Console\Commands;

use App\Fazzt\Json;
use Illuminate\Console\Command;

class FazztDCPRetransmit extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fazzt:dcp-retransmit {uuid}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Retransmit a failed transmit';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        \App\Fazzt\DCP\Retransmit::exec($this->argument('uuid'));

        return Command::SUCCESS;
    }
}
