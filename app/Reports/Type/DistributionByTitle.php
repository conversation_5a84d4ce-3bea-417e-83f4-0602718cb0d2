<?php

namespace App\Reports\Type;

use App\Models\Booking;
use App\Models\Enum\BookingStatus;
use App\Models\Title;

class DistributionByTitle
{
    static function build($titleId)
    {
        $error = false;
        $tempFile = storage_path(uniqid());
        $fp = fopen($tempFile, 'w+');

        $header = [
            "Booking ID",
            "Studio",
            "Title",
            "Release Name",
            "Release Type",
            "Release Size",
            "TCN",
            "Circuit",
            "Site Name",
            "Address",
            "Play Start Date",
            "Play End Date",
            "Delivery Deadline",
            "Overall Status",
            "Actual Date Delivered",
        ];

        $title = Title::find($titleId);
        $rows = [];

        /** @var Booking $booking */
        foreach ($title->bookings as $booking) {
            $row = [
                $booking->id,
                $booking->organization->name,
                $booking->title->friendly_title,
                $booking->release ? $booking->release->package_name : 'n/a',
                $booking->release ? $booking->release->type->label() : 'n/a',
                $booking->release ? self::bytesToHuman($booking->release->content->sum('size')) : 'n/a',
                $booking->cinema->tcn,
                $booking->cinema->circuit,
                $booking->cinema->name,
                $booking->cinema->address,
                $booking->release_date ? $booking->release_date->format('m/d/Y h:i:s A e') : 'n/a',
                $booking->release_end ? $booking->release_end->format('m/d/Y h:i:s A e') : 'n/a',
                $booking->deliver_at ? $booking->deliver_at->format('m/d/Y h:i:s A e') : 'n/a',
                $booking->overall_status->label() ?? 'n/a',
                $booking->overall_status === BookingStatus::Completed ?
                    $booking->updated_at->format('m/d/Y h:i:s A e') : 'n/a',
            ];

            $rows[] = $row;
        }

        fputcsv($fp, $header);
        foreach ($rows as $row) {
            fputcsv($fp, $row);
        }
        fclose($fp);

        return [$error, $tempFile];
    }

    private static function bytesToHuman($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];

        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }
}
