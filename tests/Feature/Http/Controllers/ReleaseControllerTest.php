<?php

namespace Tests\Feature\Http\Controllers;

use App\Models\Organization;
use App\Models\Title;
use App\Models\User;
use App\Models\Version;
use App\Models\Release;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Testing\Fluent\AssertableJson;
use Spatie\LaravelPackageTools\Package;
use Tests\TestCase;

class ReleaseControllerTest extends TestCase
{

    public function test_it_gets_packages() {
        $user = User::factory()->create();
        $organization = Organization::factory()->withOwner($user)->create();
        $organization->users()->save($user);

        $response = $this->actingAs($user)->getJson(route('releases.index'));

        $response->assertSuccessful();
        $response->assertJson(fn(AssertableJson $json) => $json->has('data')->etc());
    }

    public function test_it_gets_a_package() {
        $user = User::factory()->create();
        $organization = Organization::factory()->withOwner($user)->create();
        $organization->users()->save($user);
        $title = Title::factory()->withOrganization($organization)->withCreator($user)->create();
        $release = Release::factory()->onTitle($title)->withOrganization($organization)->create();

        $response = $this->actingAs($user)->getJson(route('releases.show', $release));

        $response->assertSuccessful();
        $response->assertJson(fn(AssertableJson $json) => $json->has('data')->etc());
    }

    public function test_it_toggles_pin() {

        $user = User::factory()->create();
        $organization = Organization::factory()->withOwner($user)->create();
        $organization->users()->save($user);
        $title = Title::factory()->withOrganization($organization)->withCreator($user)->create();
        $release = Release::factory()->onTitle($title)->withOrganization($organization)->create(['is_pinned' => true]);

        $response = $this->actingAs($user)->getJson(route('releases.toggle-pin', $release));

        $response->assertSuccessful();
        $response->assertJson(fn(AssertableJson $json) => $json->has('data',
            fn(AssertableJson $json) => $json->where('is_pinned', false)->etc()
        )->etc());
        // assert is_pin = false
    }

    public function test_it_contains_total_size() {

        $user = User::factory()->create();
        $organization = Organization::factory()->withOwner($user)->create();
        $organization->users()->save($user);
        $title = Title::factory()->withOrganization($organization)->withCreator($user)->create();

        // test content
        $version1 = Version::factory()->onTitle($title)->withCreator($user)->create(['size' => 100]);
        $version2 = Version::factory()->onTitle($title)->withCreator($user)->create(['size' => 200]);
        // sum 300

        // attach to test release
        $release = Release::factory()->onTitle($title)->withOrganization($organization)->create(['is_pinned' => true]);
        $release->content()->attach([$version1->id, $version2->id]);

        $response = $this->actingAs($user)->getJson(route('releases.show', $release));

        $response->assertSuccessful();
        $response->assertJson(fn(AssertableJson $json) => $json->has('data',
            fn(AssertableJson $json) => $json
                ->where('is_pinned', true)
                ->where('size', 300)
                ->etc()
        )->etc());
    }

    public function test_it_gets_title_packages()
    {
        $user = User::factory()->create();
        $organization = Organization::factory()->withOwner($user)->create();
        $title = Title::factory()->withOrganization($organization)->withCreator($user)->create();
        $organization->users()->save($user);

        $response = $this->actingAs($user)->getJson(route('titles.releases.index', ['title' => $title]));

        $response->assertSuccessful();
        $response->assertJson(fn(AssertableJson $json) => $json->has('data')->etc());

    }

    public function test_it_adds_content_id_to_package()
    {
        $user = User::factory()->create();
        $organization = Organization::factory()->withOwner($user)->create();
        $title = Title::factory()->withOrganization($organization)->withCreator($user)->create();
        $version = Version::factory()->onTitle($title)->withCreator($user)->create();
        $package = Release::factory()->onTitle($title)->withOrganization($organization)->create();
        $organization->users()->save($user);

        $response = $this->actingAs($user)->putJson(route('titles.releases.update',
            ['title' => $title, 'release' => $package]), ['add_content_id' => $version->id]);

        $response->assertSuccessful();
        $response->assertJson(fn(AssertableJson $json) => $json->has('data')->etc());
    }

    public function test_it_removes_content_id_from_package()
    {
        $user = User::factory()->create();
        $organization = Organization::factory()->withOwner($user)->create();
        $title = Title::factory()->withOrganization($organization)->withCreator($user)->create();
        $version = Version::factory()->onTitle($title)->withCreator($user)->create();
        $package = Release::factory()->onTitle($title)->withOrganization($organization)->create();
        $organization->users()->save($user);

        $response = $this->actingAs($user)->putJson(route('titles.releases.update',
            ['title' => $title, 'release' => $package]), ['remove_content_id' => $version->id]);

        $response->assertSuccessful();
        $response->assertJson(fn(AssertableJson $json) => $json->has('data')->etc());
    }

    public function test_it_adds_content_to_package()
    {

        $user = User::factory()->create();
        $organization = Organization::factory()->withOwner($user)->create();
        $title = Title::factory()->withOrganization($organization)->withCreator($user)->create();
        $version = Version::factory()->onTitle($title)->withCreator($user)->create();
        $version2 = Version::factory()->onTitle($title)->withCreator($user)->create();
        $package = Release::factory()->onTitle($title)->withOrganization($organization)->create();
        $organization->users()->save($user);

        $response = $this->actingAs($user)->putJson(route('titles.releases.update',
            ['title' => $title, 'release' => $package]), ['attached_content' => [$version->id, $version2->id]]);

        $response->assertSuccessful();
        $response->assertJson(fn(AssertableJson $json) => $json->has('data')->etc());

    }

    public function test_it_does_not_allow_title_package_mismatch()
    {
        $user = User::factory()->create();
        $organization = Organization::factory()->withOwner($user)->create();
        $title = Title::factory()->withOrganization($organization)->withCreator($user)->create();
        $version = Version::factory()->onTitle($title)->withCreator($user)->create();
        $package = Release::factory()->onTitle($title)->withOrganization($organization)->create();
        $organization->users()->save($user);

        $anotherUser = User::factory()->create();
        $anotherOrg = Organization::factory()->withOwner($anotherUser)->create();
        $anotherOrg->users()->save($anotherUser);
        $anotherTitle = Title::factory()->withOrganization($anotherOrg)->withCreator($anotherUser)->create();
        $anotherPackage = Release::factory()->onTitle($anotherTitle)->withOrganization($anotherOrg)->create();
        $anotherVersion = Version::factory()->onTitle($anotherTitle)->withCreator($anotherUser)->create();

        $response = $this->actingAs($user)->putJson(route('titles.releases.update',
            ['title' => $title, 'release' => $anotherPackage]), ['attached_content' => [$anotherVersion->id, $version->id]]);

        $response->assertForbidden();

    }

}
