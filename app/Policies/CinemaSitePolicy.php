<?php

namespace App\Policies;

use App\Models\CinemaSite;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class CinemaSitePolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param \App\Models\User $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user)
    {
        return $user->isAdmin() || $user->canBoth('view-cinemas');
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param \App\Models\User $user
     * @param \App\Models\CinemaSite $cinemaSite
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, CinemaSite $cinemaSite)
    {
        return $user->isAdmin()
            || $user->can('view-all-cinemas')
            || ($user->canBoth('view-cinemas') && $cinemaSite->organization_id === $user->organization_id);
    }

    /**
     * Determine whether the user can create models.
     *
     * @param \App\Models\User $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user)
    {
        return $user->isAdmin() || $user->canBoth('create-cinemas');
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param \App\Models\User $user
     * @param \App\Models\CinemaSite $cinemaSite
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, CinemaSite $cinemaSite)
    {
        return $user->isAdmin()
            || ($user->canBoth('edit-cinemas') && $cinemaSite->organization_id === $user->organization_id);;
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param \App\Models\User $user
     * @param \App\Models\CinemaSite $cinemaSite
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, CinemaSite $cinemaSite)
    {
        return $user->isAdmin()
            || ($user->canBoth('delete-cinemas') && $cinemaSite->organization_id === $user->organization_id);;
    }
}
