<?php

namespace App\Policies;

use App\Models\Title;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class TitlePolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param \App\Models\User $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user)
    {
        // this will be filtered by the controller.
        return true;
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param \App\Models\User $user
     * @param \App\Models\Title $title
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, Title $title)
    {
        return $user->isAdmin()
            // or the user is the creator
            || $title->creator_id === auth()->user()->id
            // or the organization that the user is in, is also the organization the creator is in
            || $user->organization->users->contains($title->creator_id)
            || $user->canBoth('view-titles') && $title->organization->users->contains($user);

    }

    /**
     * Determine whether the user can create models.
     *
     * @param \App\Models\User $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user)
    {
        return $user->isAdmin() || $user->canBoth('create-titles');
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param \App\Models\User $user
     * @param \App\Models\Title $title
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, Title $title)
    {
        //
        return $user->isAdmin() || ($user->canBoth('update-titles') && ($user->id === $title->creator_id || $title->organization->users->contains($user->id)));
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param \App\Models\User $user
     * @param \App\Models\Title $title
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, Title $title)
    {
        //
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @param \App\Models\User $user
     * @param \App\Models\Title $titles
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function restore(User $user, Title $title)
    {
        //
        return $user->isAdmin();
    }

}
