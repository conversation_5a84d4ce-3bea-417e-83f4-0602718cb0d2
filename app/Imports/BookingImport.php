<?php

namespace App\Imports;

use App\Events\CreateImportBookings;
use App\Events\ImportBookingsUpload;
use App\Models\Booking;
use App\Models\CinemaSite;
use App\Models\Enum\BookingStatus;
use App\Models\ImportBooking;
use App\Models\ImportBookingRecord;
use App\Models\Organization;
use App\Models\User;
use Illuminate\Console\OutputStyle;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\OnEachRow;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithProgressBar;
use Maatwebsite\Excel\Events\AfterImport;
use Maatwebsite\Excel\Events\BeforeImport;
use Maatwebsite\Excel\Row;

class BookingImport implements OnEachRow, WithHeadingRow, WithProgressBar, WithEvents, WithChunkReading
{

    use Importable;

    static $processedRows = 0;
    static $importedRows = 0;

    private ImportBooking $importBooking;
    private int $organizationId;

    public function __construct(ImportBooking $importRecord)
    {
        self::$importedRows = 0;
        self::$processedRows = 0;

        $this->importBooking = $importRecord;

        $user = User::where('id', $importRecord->user_id)->first();
        $this->organizationId = $user->organization_id;

        if (count($this->importBooking->mapping) === 0) {
            $this->importBooking->error_message = "No mapping fields set. Cannot Import.";
            $this->importBooking->save();
            throw new \Exception("No mapping set for file. Cannot Import.");
        }

    }

    public function onRow(Row $row)
    {
        // find the theatre based on import record mapping -> theatre model
        $rowIndex = $row->getIndex();
        $row = $row->toArray();

        self::$processedRows++;

        $cinemaSiteQuery = CinemaSite::query();
        foreach ($this->importBooking->mapping as $local => $csv) {
            $cinemaSiteQuery->where($local, $row[$csv]);
        }

        // create a booking record for this row
        $importBookingRecord = ImportBookingRecord::make([
            'import_booking_id' => $this->importBooking->id,
            'user_id' => $this->importBooking->user_id,
            'original_record' => $row,
            'original_index' => $rowIndex,
        ]);

        if ($cinemaSite = $cinemaSiteQuery->first()) {
            // update it with any matched details
            $importBookingRecord->matched = true;
            $importBookingRecord->cinema_site_id = $cinemaSite->id;

        }

        // save the row to import_booking_records w/ results
        $importBookingRecord->save();

        $this->importBooking->num_records_imported = self::$importedRows;
        $this->importBooking->num_records_processed = self::$processedRows;
        $this->importBooking->save();
    }

    public function headingRow(): int
    {
        return $this->importBooking->header_row_index ?? 1;
    }

    public function registerEvents(): array
    {
        return [
            AfterImport::class => function (AfterImport $event) {
                $this->importBooking->processed = true;
                // the initial count includes blank/non-useful lines at end of file(s).
                $this->importBooking->num_records = $this->importBooking->num_records_processed;
                $this->importBooking->save();

            },
            BeforeImport::class => function (BeforeImport $event) {
                $totalRows = $event->getReader()->getTotalRows();

                if (!empty($totalRows)) {
                    $array = array_reverse($totalRows);
                    $this->importBooking->num_records = array_pop($array) - $this->headingRow();
                    $this->importBooking->num_records_imported = 0;
                    $this->importBooking->save();
                }
            },
        ];
    }

    public function chunkSize(): int
    {
        return 100;
    }
}
