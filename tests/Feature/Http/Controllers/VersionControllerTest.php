<?php

namespace Tests\Feature\Http\Controllers;

use App\Http\Controllers\VersionController;
use App\Models\Organization;
use App\Models\Title;
use App\Models\User;
use App\Models\Version;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class VersionControllerTest extends TestCase
{

    public function test_admin_can_see_versions_for_title()
    {

        $user = User::factory()->create()->assignRole('admin');
        $organization = Organization::factory()->withOwner($user)->create();
        $title = Title::factory()->withOrganization($organization)->withCreator($user)->create();

        $response = $this->actingAs($user)->getJson(route('versions.index-for-title', $title));
        $response->assertSuccessful();

    }

    public function test_org_user_can_see_versions_for_title()
    {

        $user = User::factory()->create();
        $organization = Organization::factory()->withOwner($user)->create();
        $title = Title::factory()->withOrganization($organization)->withCreator($user)->create();
        $organization->users()->save($user);

        $response = $this->actingAs($user)->getJson(route('versions.index-for-title', $title));
        $response->assertSuccessful();

    }

    public function test_org_user_cannot_see_versions_for_another_org_title()
    {
        $user = User::factory()->create();
        $organization = Organization::factory()->withOwner($user)->create();
        $organization->users()->save($user);

        $anotherUser = User::factory()->create();
        $anotherOrg = Organization::factory()->withOwner($user)->create();
        $anotherOrg->users()->save($anotherUser);

        $title = Title::factory()->withOrganization($anotherOrg)->withCreator($anotherUser)->create();

        $response = $this->actingAs($user)->getJson(route('versions.index-for-title', $title));
        $response->assertForbidden();
    }

    public function test_org_user_can_create_version_for_title()
    {
        $user = User::factory()->create()->assignRole('studio-read-write');
        $organization = Organization::factory()->withOwner($user)->create();
        $organization->users()->save($user);

        $title = Title::factory()->withOrganization($organization)->withCreator($user)->create();

        $response = $this->actingAs($user)->postJson(route('versions.store-for-title', $title),
            ['version_name' => uniqid('version-name'), 'nickname' => 'test']
        );
        $response->assertSuccessful();

    }

    public function test_org_user_cannot_create_version_for_non_org_title()
    {
        $user = User::factory()->create();
        $organization = Organization::factory()->withOwner($user)->create();
        $organization->users()->save($user);

        $anotherUser = User::factory()->create();
        $anotherOrg = Organization::factory()->withOwner($user)->create();
        $anotherOrg->users()->save($anotherUser);

        $title = Title::factory()->withOrganization($anotherOrg)->withCreator($anotherUser)->create();

        $response = $this->actingAs($user)->postJson(route('versions.store-for-title', $title),
            ['version_name' => uniqid('version-name'), 'nickname' => 'test']
        );

        $response->assertForbidden();
    }

    public function test_admin_can_create_version_on_title()
    {

        $user = User::factory()->create()->assignRole('admin');
        $organization = Organization::factory()->withOwner($user)->create();
        $title = Title::factory()->withOrganization($organization)->withCreator($user)->create();

        $response = $this->actingAs($user)->postJson(route('versions.store'),
            ['title_id' => $title->id, 'version_name' => uniqid('version-name'), 'nickname' => 'test']);
        $response->assertSuccessful();

    }

    public function test_admin_can_see_version()
    {

        $user = User::factory()->create()->assignRole('admin');
        $organization = Organization::factory()->withOwner($user)->create();
        $title = Title::factory()->withOrganization($organization)->withCreator($user)->create();
        $version = Version::factory()->onTitle($title)->withCreator($user)->create();

        $response = $this->actingAs($user)->getJson(route('versions.show', $version));
        $response->assertSuccessful();

    }

    public function test_org_user_cannot_can_see_non_org_version()
    {

        $user = User::factory()->create();
        $organization = Organization::factory()->withOwner($user)->create();
        $organization->users()->save($user);

        $title = Title::factory()->withOrganization($organization)->withCreator($user)->create();
        $version = Version::factory()->onTitle($title)->withCreator($user)->create();

        $anotherUser = User::factory()->create();
        $anotherOrg = Organization::factory()->withOwner($anotherUser)->create();
        $anotherOrg->users()->save($anotherUser);

        $response = $this->actingAs($anotherUser)->getJson(route('versions.show', $version));
        $response->assertForbidden();

    }

    public function test_admin_cannot_create_version_on_invalid_title()
    {

        $user = User::factory()->create()->assignRole('admin');
        $response = $this->actingAs($user)->postJson(route('versions.store'),
            ['title_id' => 'xyz', 'version_name' => uniqid('version-name'), 'nickname' => 'test']);
        $response->assertUnprocessable();

    }

    public function test_admin_cannot_create_version_on_not_found_title()
    {

        $user = User::factory()->create()->assignRole('admin');
        $response = $this->actingAs($user)->postJson(route('versions.store'),
            ['title_id' => 1000000, 'version_name' => uniqid('version-name'), 'nickname' => 'test']);
        $response->assertNotFound();

    }

    public function test_admin_can_list_all_versions()
    {
        $user = User::factory()->create()->assignRole('admin');
        $response = $this->actingAs($user)->getJson(route('versions.index'));
        $response->assertSuccessful();
    }

    public function test_user_cannot_list_all_versions()
    {
        $user = User::factory()->create();
        $response = $this->actingAs($user)->getJson(route('versions.index'));
        $response->assertForbidden();

    }
}
