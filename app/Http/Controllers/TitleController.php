<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreTitleRequest;
use App\Http\Requests\UpdateTitleRequest;
use App\Models\AsperaTransfer;
use App\Models\Enum\TitleStatus;
use App\Models\Title;
use App\Models\Version;
use App\Models\TitleUploadLink;
use Illuminate\Support\Facades\URL;

class TitleController extends Controller
{
    public function __construct()
    {
        $this->authorizeResource(Title::class);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if (auth()->user()->isAdmin() || auth()->user()->can('view-all-titles')) {
            $query = Title::query();
        }
        else {
            $query = auth()->user()->organization->titles();
        }
        $search = request()->get('search');
        $studio = request()->get('studio');
        $status = request()->get('status');

        $query->with(['organization:id,name', 'creator:id,name']);
        return $query->when($search, function ($q, $search) {
            $search = "%{$search}%";
            $q->where('friendly_title', 'like', $search);
        })->when($studio, function ($q, $studio) {
            $q->whereIn('organization_id', explode(',', $studio));
        })->when($status, function ($q, $status) {
            $q->where('status', $status);
        })
            ->orderBy(request()->sortBy ?? 'friendly_title', request()->sortDirection ?? 'ASC')
            ->paginate(request()->take);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \App\Http\Requests\StoreTitleRequest $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreTitleRequest $request)
    {
        $data = $request->validated();

        $data['creator_id'] = auth()->user()->id;
        $data['status'] = TitleStatus::STATUS_ACTIVE;

        /** @var Title $title */
        if ($data['organization_id']) {
            $title = Title::create($data);
        } else {
            $title = auth()->user()->organization->titles()->create($data);
        }

        return response(['data' => $title]);

    }

    /**
     * Display the specified resource.
     *
     * @param \App\Models\Title $title
     * @return \Illuminate\Http\Response
     */
    public function show(Title $title)
    {
        $title->load(['releases:id,title_id,package_name']);
        return response(['data' => $title]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \App\Http\Requests\UpdateTitleRequest $request
     * @param \App\Models\Title $title
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateTitleRequest $request, Title $title)
    {
        $data = $request->validated();

        $title->update($data);

        return response(['data' => $title]);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\Models\Title $title
     * @return \Illuminate\Http\Response
     */
    public function destroy(Title $title)
    {
        $title->delete();
        return response(['data' => $title]);
    }

    public function getUploadLinksForTitle(Title $title)
    {
        return response(['data' => $title->links()->orderBy('expires_at', 'DESC')->get()]);
    }

    public function storeUploadLinkForTitle(Title $title)
    {
        if (auth()->user()->cannot('create', Title::class)) {
            abort(401);
        }

        /*
         * This creates a link that anyone can access via signed and expiring URLs.
         * When they visit this URL via the UI, it will be validated and return a
         * different signed URL for the aspera uploader to use.
         *
         * This link can have a custom set expiry time or default to 7 days.
         */
        $link = TitleUploadLink::create([
            'title_id' => $title->id,
            'user_id' => auth()->user()->id,
            'is_expired' => false,
            'email' => request()->post('email', null), // take from post
            'expires_at' => request()->post('expires_at', now()->addDays(7)), // take from post
        ]);

        // todo send an email maybe or just let the creator share the URL.

        return ['data' => $link];

    }

    public function updateUploadLinkForTitle(Title $title, TitleUploadLink $link)
    {
        if (auth()->user()->cannot('update', $title)) {
            abort(401);
        }

        $link->fill(request()->only('expires_at', 'is_expired'));
        $link->save();

        return ['data' => $link];
    }

    /** Signed URL requests via the non-authenticated UI. */
    public function validateUploadLinkForTitle(TitleUploadLink $link)
    {
        // this is a signed link, the title ID will be derived from
        // the signed link data.
        $this->checkSignature($link);

        if ($link->expires_at < now()) {
            $link->is_expired = true;
            $link->save();
        }

        return $this->exposedUiData($link);

    }

    public function createAsperaUploadForLink(TitleUploadLink $link)
    {

        $this->checkSignature($link);

        $version = Version::create([
            'title_id' => $link->title_id,
            'creator_id' => $link->user_id, // this is unauthenticated, so we'll set the owner to the link owner.
        ]);

        // create the aspera transfer spec and send it back to UI.
        $payload = [
            'title_id' => $link->title_id,
            'version_id' => $version->id,
            'user_id' => $link->user_id,
            'title_upload_link_id' => $link->id,
            'aspera_data' => request()->post('dataTransfer'),
        ];

        $data = [];
        $data['aspera_transfer'] = AsperaTransfer::createUpload($payload);
        return array_merge($data, $this->exposedUiData($link));

    }

    private function exposedUiData(TitleUploadLink $link)
    {
        // return a nice unified object here for the UI to display to the
        // non authenticated user.
        $title = $link->title;
        if (!$link->is_expired) {
            $expires = $link->expires_at ?? now()->addHours(24);

            $url = URL::temporarySignedRoute(
                'start-upload-link',
                $expires,
                ['link' => $link]
            );
        }
        else {
            $url = false;
        }

        return [
            'title' => $title->friendly_title,
            'upload_url' => $url,
            'expires' => $link->expires_at,
            'uploads' => $link->transfers->each->setHidden(['aspera_transfer_data', 'transfer_spec']),
            // a list of any other uploads that are complete/inprogress for this shared link.
        ];

    }

    private function checkSignature(TitleUploadLink $link)
    {
        if (!request()->hasValidSignature()) {
            abort(401); // expired
        }
        if ($link->is_expired) {
            abort(419);
        }
    }
}
