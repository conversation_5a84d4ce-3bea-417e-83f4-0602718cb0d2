<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MediaManagerDrive extends Model
{
    use HasFactory;

    const OVERALL_HEALTH_RESULT = 'smart_overall-health_self-assessment_test_result';
    const PASSED = 'PASSED';
    const NORMAL = 'Normal';
    const NOT_AVAILABLE = 'N/A';
    const TEMPERATURE_CELCIUS = 'Temperature_Celsius';
    const LOCAL_TIME_KEY = 'local_time_is';

    protected $casts = ['smart_data' => 'json'];

    protected $fillable = [
        'drive',
        'media_manager_id',
        'smart_data',
    ];

    function parseData($results)
    {
        $newData = [];

        if (!is_array($results)) {
            $results = [$results];
        }
        foreach ($results as $result) {

            $exploded = explode("\n", $result);
            foreach ($exploded as $innerResult) {
                if (strpos($innerResult, self::TEMPERATURE_CELCIUS) !== false) {
                    if ($temperature = $this->parseTemperature($innerResult)) {
                        $newData['temperature'] = $temperature;
                    }
                }
                else {
                    $newData = array_merge($newData, $this->parseInfo($innerResult));
                }
            }
        }

        return $newData;
    }

    private function parseInfo($result)
    {
        $data = [];
        $allInfo = explode("\n", $result);
        foreach ($allInfo as $info) {
            $colonPos = strpos($info, ":");
            if ($colonPos === false) {
                continue;
            }

            $key = substr($info, 0, $colonPos);
            $value = substr($info, $colonPos + 1);
            if ($key && $value) {
                $key = str_replace(" ", "_", trim($key));
                $key = str_replace(".", "_", trim($key));
                $key = strtolower($key);
                $data[$key] = trim($value);
            }
        }
        return $data;
    }

    private function parseTemperature($result)
    {
        $allInfo = preg_split('/\s+/', $result);
        $index = 0;
        foreach ($allInfo as $info) {
            if (strpos($info, self::TEMPERATURE_CELCIUS) !== false) {
                $tempIndex = $index;
                break;
            }
            $index++;
        }

        if ($tempIndex && isset($allInfo[$tempIndex + 8])) {
            return $allInfo[$index + 8];
        }
    }

    public function getHealthStatusAttribute()
    {
        if ($this->smart_data[self::OVERALL_HEALTH_RESULT] === self::PASSED) {
            return self::NORMAL;
        }
        else {
            return $this->smart_data[self::OVERALL_HEALTH_RESULT] ?? self::NOT_AVAILABLE;
        }
    }

}
