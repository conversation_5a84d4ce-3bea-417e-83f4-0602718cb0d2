<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class FazztRequestLog extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'fazzt_request_log';

    protected $fillable = [
        'include_file',
        'method',
        'payload',
        'site_ids',
        'response_code',
        'is_completed',
    ];

    protected $casts = [
        'payload' => 'json'
    ];
}
