# DCDC API

[![Laravel Unit Tests](https://github.com/BitCine/dcdc-api/actions/workflows/laravel.yml/badge.svg)](https://github.com/BitCine/dcdc-api/actions/workflows/laravel.yml)

![Code Coverage](https://public-assets-dev.dev.cinesend.com/github-badges/dcdc-api-badge.svg)

## About

-   Built on Laravel 9+
-   MySQL 8.0+
-   Vapor deployed (staging, production)
-   This is in full-API mode (no blades, no front end routes save for the default 404's etc.)
-   To get up and running:

```
composer install
```

### edit your .env with local mysql credentials then (first time, you want to seed):

```
./artisan migrate:fresh --seed
valet secure
valet open
```

### et voila! a nice little api only.

```json
{
    "status": "ok"
}
```

### URLs

I'm using the default https://dcdc-api.test from valet, and a temporary front end on https://frontend.dcdc-api.test for consistency. This allows for same-domain cookies to work, although that requirement is currently disabled via the `session.php` config file for the sake of the non-domain vapor deployment.

Staging: https://staging-api.dcdcdistribution.com & https://staging-portal.dcdcdistribution.com

Production: https://api.dcdcdistribution.com & https://portal.dcdcdistribution.com

### ENV and Secure Variables

You can safely update the env file for each environment by doing `vapor env:pull [environment]` - this will download a `.env.[environment]` file which you can edit, then `vapor env:push [environment]` - which will then prompt you to delete the file you worked on. Do not commit these files.

Make sure that the following environment variables are set after copying them over from .env.example so that aspera uploads wont fail. (ask a dev for the values)
ASPERA_USERNAME=
ASPERA_PASSWORD=
AWS_BUCKET=

For larger values (key or certificate files, for example) use the 'Secrets' tab on the Vapor UI. These will also be exposed to the application as if they were regular ENV variables, but allow for larger values.

Changes to ENV files require a full redeploy as the settings are bundled in the lambda deployed function.

### Testing

If this is your first time running the testing suite:

1. Run `touch database/database.sqlite` from the root to create the database
2. Run `./artisan --env testing migrate` to create the tables

Testing info:

1. If you add a route, add 2 tests. One for good data. One for bad data.
2. Run the suite `./artisan test --parallel --coverage` or `./artisan test --parallel --coverage-html reports/` to generate an HTML report in `/reports/`
3. Testing outbound email - Locally you can capture all email with MailHog (`brew install mailhog`) and use the mail settings in .env.example

.
