<?php

namespace App\Fazzt\Cru;

use App\Fazzt\Json;

class SetMode
{

    static function exec($mode = 'manual', $destination = 909920)
    {
        // todo: make sure mode is only manual, auto, or 'off'.

        $params = [
            0 => 'cru-mode',
            1 => $mode,
            2 => [
                'OnClientCallback' => config('fazzt.callback_url') . 'cru-mode',
                'SiteID' => $destination, // todo: destination site ID
                'QueueName' => 'Channel 1 Preprocess', // todo: configure channels via config.
                'TransmissionModeName' => 'With Reports Command',
            ],
        ];
        try {
            $response = Json::jsonCall(
                'CinemaCmdSubmit',
                null,
                $params,
                true);
            return true;
        }
        catch (\Exception $e) {
            logger($e->getMessage());
        }

        return false;
    }
}
