<?php

namespace App\Jobs;

use App\Models\MediaManager;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class UpdateMediaLibrary implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $uniqueFor = 3600;
    public $mediaManagerId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($mediaManagerId)
    {
        //
        $this->mediaManagerId = $mediaManagerId;
    }

    public function uniqueId(): string
    {
        return $this->mediaManagerId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        //
        $mediaManager = MediaManager::find($this->mediaManagerId);
        $mediaManager->getStorage(true);
    }
}
