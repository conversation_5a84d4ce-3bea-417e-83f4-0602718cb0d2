<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FazztTransfer extends Model
{
    use HasFactory;

    protected $fillable = [
        'transmit_id', 'asset_uuid', 'site_ids', 'duration', 'percent', 'start_time', 'status', 'state', 'priority',
        'scheduled_start_at', 'actual_start_at', 'actual_completed_at', 'estimated_minutes'
    ];

    protected $casts = [
        'start_time' => 'datetime',
        'estimated_minutes' => 'integer',
        'priority' => 'integer',
        'actual_completed_at' => 'datetime',
        'actual_start_at' => 'datetime',
        'scheduled_start_at' => 'datetime',
    ];

    public function booking()
    {
        return $this->belongsTo(Booking::class);
    }

    public function version()
    {
        // this will need to reference an fpk for the sake of tracking (if the fpk is gone/missing, we need to
        // go through the upload process before this can even start.
        return $this->hasOne(Version::class, 'asset_uuid', 'asset_uuid');
    }

    public function content()
    {
        return $this->hasOne(FazztContent::class, 'asset_uuid', 'asset_uuid');
    }

    static function getMaxPriority() {
        return self::max('priority') + 1;
    }

    static function recalculatePriorityAndStartTime() {
        // grab all pending by order and re-set from base 1
        $allPending = self::where('status', 'PENDING')->orderBy('priority', 'asc')->get();
        $priority = 0;
        $lastPendingDuration = 0;

        // if there is one currently transmitting we need to use it's estimated end time as $lastEndTime.
        $activeTransmit = self::where('status', 'PROCESSING')->where('state', 'TRANSMITTING')->first();
        if ($activeTransmit) {
            $lastEndTime = $activeTransmit->actual_start_at->addMinutes($activeTransmit->estimated_minutes);
        } else {
            // otherwise, just bump these starts to ASAP.
            $lastEndTime = \Carbon\Carbon::create(now());
        }

        // in order of priority, figure out start time based on minutes_to complete and prior priority
        // The 15 minutes is just an arbitrary buffer in case of small packages, or test data to have a visible difference in start times.
        foreach ($allPending as $pending) {
            $priority++;
            $pending->priority = $priority;
            // same for now, unintentionally duplicated fields. will use scheduled_start_at and actual_start_at for tracking purposes.
            $pending->start_time = $lastEndTime->addMinutes($lastPendingDuration + 15);
            $pending->scheduled_start_at = $lastEndTime;
            $lastPendingDuration = $pending->estimated_minutes;
            $pending->save();
        }
    }
}
