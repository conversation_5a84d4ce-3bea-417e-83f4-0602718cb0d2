<?php

namespace App\Policies;

use App\Models\MediaManager;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class MediaManagerPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user)
    {
        return $user->isAdmin() || $user->canBoth('view-equipment');
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\MediaManager  $order
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, MediaManager $mediaManager)
    {
        return $user->isAdmin()
            || ($user->canBoth('view-equipment') && $mediaManager->organization->users->contains($user));
    }

    /**
     * Determine whether the user can create models.
     *
     * @param \App\Models\User $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user)
    {
        return $user->isAdmin() || $user->canBoth('create-equipment');
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param \App\Models\User $user
     * @param \App\Models\MediaManager $mediaManager
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, MediaManager $mediaManager)
    {
        return $user->isAdmin()
            || ($user->canBoth('edit-equipment') && $mediaManager->organization->users->contains($user));
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param \App\Models\User $user
     * @param \App\Models\MediaManager $mediaManager
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, MediaManager $mediaManager)
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @param \App\Models\User $user
     * @param \App\Models\MediaManager $mediaManager
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function restore(User $user, MediaManager $mediaManager)
    {
        return $user->isAdmin();
    }

}
