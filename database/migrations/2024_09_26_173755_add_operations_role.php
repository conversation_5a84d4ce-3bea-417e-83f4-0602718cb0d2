<?php

use Illuminate\Database\Migrations\Migration;
use App\Models\{Role, Permission};

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $role = Role::findOrCreate('operations');
        $viewPermissions = Permission::where('name', 'like', '%view%')->get();
        $role->givePermissionTo($viewPermissions);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        $role = Role::where('name', 'operations')->first();
        if ($role) {
            $viewPermissions = Permission::where('name', 'like', '%view%')->get();
            $role->revokePermissionTo($viewPermissions);
            $role->delete();
        }
    }
};