<?php

namespace App\Jobs;

use App\Models\OutboundNotifierLog;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RetryOutboundNotification implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $outboundNotifierLog;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(OutboundNotifierLog $outboundNotifierLog)
    {
        $this->outboundNotifierLog = $outboundNotifierLog;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        // temp log.
        logger('QUEUE: retrying notification ID ' . $this->outboundNotifierLog->id);

        $booking = $this->outboundNotifierLog->booking;

        if (method_exists($this->outboundNotifierLog->notification_class, $this->outboundNotifierLog->status->value)) {
            $this->outboundNotifierLog->notification_class::{$this->outboundNotifierLog->status->value}($booking);
        }

    }
}
