<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class WebhookLog extends Model
{
    use SoftDeletes, HasFactory;

    protected $fillable = [
        'http_status', 'http_body', 'http_error', 'payload', 'headers'
    ];

    protected $table = 'webhooks_log';
    protected $casts = ['payload' => 'json', 'headers' => 'json'];

    public function webhook()
    {
        return $this->belongsTo(OrganizationWebhookUrl::class);
    }
}
