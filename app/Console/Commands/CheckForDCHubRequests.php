<?php

namespace App\Console\Commands;

use App\Jobs\SyncPackageToDeliveries;
use App\StudioHandlers\DcHub\OutboundNotifier;
use App\Models\{Booking, Enum\BookingStatus, Title, CinemaSite};
use App\DcHub\{Exception, StatusCodes, Requests};
use App\Actions\Slack\Notify;
use CodeDredd\Soap\Facades\Soap;
use Illuminate\Console\Command;
use Carbon\Carbon;

class CheckForDCHubRequests extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'dchub:poll-requests';

    protected $slack = null;

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check for new requests and process accordingly.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // check for new, and when there are none, we'll fallback to updates/previously seen.
        try {
            $requests = Requests::getRequests(true);
        }
        catch (Exception $e) {
            // otherwise update old...
            $requests = Requests::getRequests(false);
        }

        if (count($requests) === 0) {
            return;
        }

        $this->slack = new Notify("[DCHub] Identified " . count($requests) . " new request(s)");

        foreach ($requests as $request) {
            $this->info("Handle requestId: {$request['requestId']}");
            $this->handlePendingRequest($request);
        }

        $this->slack->addDivider()->send();

        return true;
    }

    private function handlePendingRequest($request)
    {
        $cinemaSite = CinemaSite::where('dchub_cinema_id', $request['cinemaId'])->first();

        // If no cinema site exists, we reroute the request to Deluxe and notify Slack.
        if (!$cinemaSite) {
            $this->rerouteRequest($request);
        }
        else {
            // Otherwise we can create or update a booking.
            $this->createBookingFromRequest($request, $cinemaSite);
        }
    }

    private function rerouteRequest($request)
    {
        $description = "Title: " . $request['titleDescription'] . " ({$request['titleId']})" . " to Cinema: ({$request['cinemaId']}) " . $request['cinemaDescription'];
        $result = Requests::rerouteRequestToDeluxe($request['requestId']);

        if ($result) {
            $this->slack->addTextSection([
                'type' => 'mrkdwn',
                'text' => ":arrow_heading_up: *Rerouted* {$description}",
            ]);
        }
        else {
            $this->slack->addTextSection(['type' => 'mrkdwn', 'text' => ":warning: Rerouting to Deluxe failed: {$description}"]);
        }
    }

    private function createBookingFromRequest($request, $cinemaSite)
    {
        $titleId = $request['titleId'];
        $titleDescription = $request['titleDescription'];
        $versionId = $request['versionId'];
        $versionDescription = $request['versionDescription'];
        $description = $request['titleDescription'] . " to " . $request['cinemaDescription'];

        // No title...
        // we can technically put these on hold when the title or version is no existant.
        $title = Title::where('dchub_title_id', $titleId)->first();
        if (!$title) {
            logger("No title $titleId: $description");
            $this->slack->addTextSection([
                'type' => 'mrkdwn',
                'text' => ":warning: Could not find title ID: {$titleId} for {$titleDescription}",
            ]);
            return;
        }

        // No package...
        $package = $title->releases()->where('dchub_version_id', $versionId)->first();
        if (!$package) {
            logger("No package with dchub version id $versionId");
            $this->slack->addTextSection([
                'type' => 'mrkdwn',
                'text' => ":warning: Could not find a package Version ID: {$versionId} for {$titleDescription}: {$versionDescription}",
            ]);
            return;
        }

        /** @var Booking $booking */
        $booking = Booking::firstOrCreate(['external_order_id' => $request['requestId']],
            [
                'organization_id' => config('cinesend.warnerbros_org_id'),
                'creator_id' => config('cinesend.warnerbros_user_id'),
                'cinema_site_id' => $cinemaSite->id,
                'title_id' => $title->id,
                'package_id' => $package->id,
                'is_electronic' => true,
                'deliver_at' => Carbon::create($request['deliveryDate']),
                'release_date' => Carbon::create($request['startDate']),
                'notification_class' => OutboundNotifier::class,
            ]);

        // this util will add the necessary transfers to the booking (or update changes)
        SyncPackageToDeliveries::dispatch($package->id);

        $bookingUrl = config('app.frontend_url') . '/bookings/' . $booking->id;

        if ($booking->wasRecentlyCreated) {

            // make sure the booking status matches the status from the dchub request. (this could be cancelled)
            $booking->setOverallStatus(BookingStatus::Pending);
            Requests::updateRequestStatus($request['requestId'], StatusCodes::PROCESSING);

            $this->slack->addTextSection([
                'type' => 'mrkdwn',
                'text' => "Created <$bookingUrl|new booking>: {$description}",
            ]);

        }
        else {
            $this->slack->addTextSection([
                'type' => 'mrkdwn',
                'text' => "Updated <$bookingUrl|booking>: {$description}",
            ]);
        }
    }
}
