<?php

namespace App\Console\Commands;

use App\Fazzt\Json;
use App\Models\FazztRequestLog;
use Illuminate\Console\Command;

class FazztSubmitDCPIngest extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fazzt:submit-dcp-ingest {path}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Submit a DCP Ingest Job to Fazzt';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        \App\Fazzt\DCP\Ingest::exec($this->argument('path'));

        return Command::SUCCESS;
    }
}
