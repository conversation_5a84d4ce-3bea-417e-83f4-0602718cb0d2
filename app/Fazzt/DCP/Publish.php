<?php

namespace App\Fazzt\DCP;

use App\Fazzt\Exception;
use App\Fazzt\Json;
use Illuminate\Console\Command;

class Publish
{

    static function exec($uuid, $siteId)
    {
        $params = [
            0 => 'publish',
            1 => [$uuid],
            2 => [
                'OnClientCallback' => config('fazzt.callback_url') . 'publish',
                'SiteID' => $siteId,
                'QueueName' => 'Channel 1 Preprocess',
                'TransmissionModeName' => 'With Reports Command',
            ]
        ];
        try {
            $response = Json::jsonCall(
                'CinemaCmdSubmit',
                null,
                $params,
                true);
            // dump($response);

        }
        catch (\Exception $e) {
            logger("Error: " . $e->getMessage());
        }

        return false;

    }
}
