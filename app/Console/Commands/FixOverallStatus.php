<?php

namespace App\Console\Commands;

use App\Models\Booking;
use App\Models\Enum\BookingStatus;
use Illuminate\Console\Command;

class FixOverallStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix-overall-status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Turn any nulls to actual for speedy counts';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $bookings = Booking::whereNull('overall_status')->get();

        foreach ($bookings as $booking) {
            $booking->overall_status = BookingStatus::tryFrom(strtolower($booking->latest_status)) ?? 'pending';
            $booking->save();
        }

        return Command::SUCCESS;
    }
}
