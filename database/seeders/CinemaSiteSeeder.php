<?php

namespace Database\Seeders;

use App\Models\CinemaSite;
use App\Models\Organization;
use Illuminate\Database\Seeder;

class CinemaSiteSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {

        $organization = Organization::factory()->create(['type' => 'exhibitor']);
        CinemaSite::factory(10)->withOrganization($organization)->create();
    }
}
