<?php

namespace App\Console\Commands;

use App\Jobs\UpdateBookingCounts;
use App\Jobs\UpdateDistributionSummary;
use App\Jobs\UpdateTransferCounts;
use App\Models\Booking;
use App\Models\Release;
use Illuminate\Console\Command;

class FillDistributionSummary extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fill-distribution-summary {releaseId}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update a distribution summary';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        UpdateDistributionSummary::dispatch(Release::findOrFail($this->argument('releaseId')));
        return Command::SUCCESS;
    }
}
