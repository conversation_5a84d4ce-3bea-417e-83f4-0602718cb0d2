<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateMediaManagerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'serial_number' => ['sometimes', 'string'],
            'organization_id' => ['sometimes', 'integer'],
            'cinema_site_id' => ['sometimes', 'integer'],
            'ssh_port' => ['sometimes', 'nullable', 'integer'],
            'web_port' => ['sometimes', 'nullable', 'integer'],
            'is_primary' => ['sometimes', 'boolean'],
        ];
    }
}
