<?php

namespace App\Models\Enum;

use App\Models\Booking;
use App\Models\Release;

enum PackageTypes: int
{
    case TYPE_SMPTE = 1;
    case TYPE_INTEROP = 2;
    case TYPE_LAS = 3;
    case TYPE_DV = 4;
    case IMAX_LASER = 5;
    case IMAX_XENON = 6;
    case TYPE_OTHER = 127;

    // Release - SMPTE, Release - InterOp, Release - LAS, Release - Dolby Vision, Other
    public function label()
    {
        return match ($this) {
            PackageTypes::TYPE_SMPTE => "Release - SMPTE",
            PackageTypes::TYPE_INTEROP => "Release - InterOp",
            PackageTypes::TYPE_LAS => "Release - LAS",
            PackageTypes::TYPE_DV => "Release - Dolby Vision",
            PackageTypes::IMAX_LASER => "IMAX - Laser",
            PackageTypes::IMAX_XENON => "IMAX - Xenon",
            PackageTypes::TYPE_OTHER => "Other",
        };
    }

    public static function contains($value): self|null
    {
        return self::tryFrom($value);
    }
}
