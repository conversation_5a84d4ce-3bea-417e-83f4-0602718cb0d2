<?php

namespace Database\Factories;

use App\Models\CinemaSite;
use App\Models\Organization;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CinemaSite>
 */
class CinemaSiteFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'name' => $this->faker->colorName . " Cinema " . $this->faker->randomNumber(2),
            'address' => $this->faker->streetAddress,
            'city' => $this->faker->city,
            'circuit' => 'AMC',
            'state' => array_rand(CinemaSite::$stateList, 1),
            'zip' => $this->faker->postcode,
            'country_code' => 'US',
        ];
    }

    public function withOrganization(Organization $org)
    {
        return $this->state(function (array $attributes) use ($org) {
            return [
                'organization_id' => $org->id,
            ];
        });
    }

}
