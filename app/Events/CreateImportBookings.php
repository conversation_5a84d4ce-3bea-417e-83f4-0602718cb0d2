<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class CreateImportBookings implements ShouldQueue
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public int $importBookingId;

    public function __construct($importBookingId)
    {
        $this->importBookingId = $importBookingId;
    }

}
