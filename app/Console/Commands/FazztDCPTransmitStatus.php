<?php

namespace App\Console\Commands;

use App\Fazzt\Json;
use App\Models\FazztTransfer;
use Illuminate\Console\Command;

class FazztDCPTransmitStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fazzt:dcp-transmit-status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check the status of a transfer by UUID';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $ft = FazztTransfer::whereIn('state', ['SENDING','SCHEDULED'])->whereNotNull('transmit_id')->whereNotNull('actual_start_at')->first();

        if (!$ft) {
            return;
        }

        $this->info("Checking status of $ft->id / $ft->transmit_id");
        \App\Fazzt\DCP\TransmitStatus::exec($ft->transmit_id);

        return Command::SUCCESS;
    }
}
