<?php

namespace App\Listeners;

use App\Events\BookingTransactionReceived;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use App\StudioHandlers;

class ProcessBookingTransaction implements ShouldQueue
{

    /**
     * The time (seconds) before the job should be processed.
     *
     * @var int
     */
    public $delay = 30;

    /**
     * Handle the event.
     *
     * @param  \App\Events\BookingTransactionReceived  $event
     * @return void
     */
    public function handle(BookingTransactionReceived $event)
    {
        // determine which studio it is
        // and then use the associated processor.
        // process the data based on which studio it came from.
        switch ($event->studio) {
            case 'disney':
                $handler = new StudioHandlers\Disney\InboundProcessor($event->bookingId, $event->transactionId);
                break;
            case 'cielo':
                // poppin' fresh dough.
                $handler = new StudioHandlers\Cielo\InboundProcessor($event->bookingId, $event->transactionId);
                break;
            case 'paramount':
                $handler = new StudioHandlers\Paramount\InboundProcessor($event->bookingId, $event->transactionId);
                break;
            case 'lionsgate':
                $handler = new StudioHandlers\Lionsgate\InboundProcessor($event->bookingId, $event->transactionId);
                break;
            case 'sony':
                $handler = new StudioHandlers\Sony\InboundProcessor($event->bookingId, $event->transactionId);
                break;
            default:
                abort(500, "No handler for " . $event->studio);
        }

        $handler->handleInbound();

    }
}
