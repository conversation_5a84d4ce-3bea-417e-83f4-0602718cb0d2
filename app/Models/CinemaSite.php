<?php

namespace App\Models;

use App\Actions\Slack\Notify;
use GuzzleHttp\Client;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class CinemaSite extends Model
{
    use SoftDeletes, HasFactory, LogsActivity;

    // public $preventsLazyLoading = true;

    static $stateList = [
        'AL' => 'Alabama',
        'AK' => 'Alaska',
        'AS' => 'American Samoa',
        'AZ' => 'Arizona',
        'AR' => 'Arkansas',
        'CA' => 'California',
        'CO' => 'Colorado',
        'CT' => 'Connecticut',
        'DE' => 'Delaware',
        'DC' => 'District Of Columbia',
        'FM' => 'Federated States Of Micronesia',
        'FL' => 'Florida',
        'GA' => 'Georgia',
        'GU' => 'Guam',
        'HI' => 'Hawaii',
        'ID' => 'Idaho',
        'IL' => 'Illinois',
        'IN' => 'Indiana',
        'IA' => 'Iowa',
        'KS' => 'Kansas',
        'KY' => 'Kentucky',
        'LA' => 'Louisiana',
        'ME' => 'Maine',
        'MH' => 'Marshall Islands',
        'MD' => 'Maryland',
        'MA' => 'Massachusetts',
        'MI' => 'Michigan',
        'MN' => 'Minnesota',
        'MS' => 'Mississippi',
        'MO' => 'Missouri',
        'MT' => 'Montana',
        'NE' => 'Nebraska',
        'NV' => 'Nevada',
        'NH' => 'New Hampshire',
        'NJ' => 'New Jersey',
        'NM' => 'New Mexico',
        'NY' => 'New York',
        'NC' => 'North Carolina',
        'ND' => 'North Dakota',
        'MP' => 'Northern Mariana Islands',
        'OH' => 'Ohio',
        'OK' => 'Oklahoma',
        'OR' => 'Oregon',
        'PW' => 'Palau',
        'PA' => 'Pennsylvania',
        'PR' => 'Puerto Rico',
        'RI' => 'Rhode Island',
        'SC' => 'South Carolina',
        'SD' => 'South Dakota',
        'TN' => 'Tennessee',
        'TX' => 'Texas',
        'UT' => 'Utah',
        'VT' => 'Vermont',
        'VI' => 'Virgin Islands',
        'VA' => 'Virginia',
        'WA' => 'Washington',
        'WV' => 'West Virginia',
        'WI' => 'Wisconsin',
        'WY' => 'Wyoming',
    ];

    protected $fillable = [
        'organization_id',
        'name',
        'address',
        'city',
        'circuit',
        'state',
        'zip',
        'organization_id',
        'cinema_site_id', // now connected to foreign table via this
        'csx_serial_number',
        'disney_site_id',
        'paramount_theatre_id',
        'lionsgate_theatre_id',
        'sony_site_id',
        'dchub_cinema_id',
        'universal_site_id',
        'tcn',
        'rentrack',
        'secondary_tcn',
        'sage_customer_number',
        'primary_contact_name',
        'primary_contact_number',
        'primary_contact_email',
        'secondary_contact_name',
        'secondary_contact_number',
        'secondary_contact_email',
        'status',
        'typical_speeds', // calculated json object based on the speeds table.
    ];

    protected $appends = [
        'media_manager_status_updated_at',
        'satellite_capable'
    ];

    protected $hidden = ['primaryMediaManager', 'statuses'];

    protected $with = ['primaryMediaManager'];

    protected $casts = [
        'status' => Enum\SiteStatus::class,
        'typical_speeds' => 'json',
    ];

    public function bookings()
    {
        return $this->hasMany(Booking::class);
    }

    public function deliveries()
    {
        return $this->hasMany(Delivery::class);
    }

    public function mappings()
    {
        return $this->hasMany(StudioToCinemaSite::class);
    }

    public function jobs()
    {
        return $this->hasMany(MediaManagerJob::class);
    }

    public function primaryMediaManager()
    {
        return $this->hasOne(MediaManager::class)->whereIsPrimary(1);
    }

    public function mediaManagers()
    {
        return $this->hasMany(MediaManager::class);
    }

    public function cinemaProServers()
    {
        return $this->hasMany(CinemaProServer::class);
    }

    public function downloadSpeeds()
    {
        return $this->hasMany(DownloadSpeed::class, 'cinema_site_id');
    }

    public function downloadSummaries()
    {
        return $this->hasMany(MediaManagerSpeedSummary::class, 'cinema_site_id');
    }

    public function appleTelevisions()
    {
        return $this->hasMany(AppleTelevision::class);
    }

    public function getMediaManagerIsOnlineAttribute()
    {
        return $this->primaryMediaManager && $this->primaryMediaManager->isOnline();
    }

    public function getMediaManagerStatusUpdatedAtAttribute()
    {
        return $this->primaryMediaManager ? $this->primaryMediaManager->status_updated_at : null;
    }

    public function sendNextPendingJob($cameOnline = false)
    {
        if (!$this->primaryMediaManager) {
            $slack = (new Notify())->addTextSection([
                'type' => 'mrkdwn',
                'text' => ":confused: {$this->name} does not have a primary media manager. But a job has been requested.",
            ])->send();
            return false;
        }

        $hasInProgress = $this->primaryMediaManager->jobs()->whereIn('status',
            ['triggered', 'running', 'queued', 'transmitting'])->count();

        if ($hasInProgress > 0) {
            logger("{$this->name} has jobs in progress. Cannot send a new job yet.");
            return false;
        }

        /** @var MediaManagerJob $job */
        $job = $this->primaryMediaManager->jobs()->whereStatus('pending')->first();

        if (!$job) {
            if ($cameOnline) {
                $slack = (new Notify())->addTextSection([
                    'type' => 'mrkdwn',
                    'text' => ":up: {$this->name} has come online but has no pending jobs.",
                ])->send();
            }
            return false;
        }

        logger("Trying to send next job to {$this->name} - job id {$job->id}");
        return $job->sendJob();
    }

    public function getStatusLogAttribute()
    {
        $statusLog = [];
        $activities = $this->activities()->where('description', 'updated')
            ->limit(10)->latest()->get();

        foreach ($activities as $activity) {
            $properties = $activity->properties;
            if (isset($properties['old']['status'])) {
                $message = "{$properties['old']['status']} => {$properties['attributes']['status']}";
                $blob = new \stdClass();
                $blob->updated_at = $activity->created_at;
                $blob->message = $message;
                $blob->old_state = $properties['old']['status'];
                $blob->new_state = $properties['attributes']['status'];
                $blob->user_id = $activity->causer->id ?? null;
                $blob->user_name = $activity->causer->name ?? 'System';
                $statusLog[] = $blob;
            }
        }
        return $statusLog;
    }

    public function getSatelliteCapableAttribute()
    {
        return $this->cinemaProServers()->exists();
    }

    public function notes()
    {
        return $this->morphMany(Note::class, 'notable');
    }

    public function contacts()
    {
        return $this->hasMany(CinemaSiteContact::class);
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->dontSubmitEmptyLogs()
            ->logFillable()
            ->logOnlyDirty();
    }
}
