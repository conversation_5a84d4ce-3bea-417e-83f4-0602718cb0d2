<?php

namespace Database\Seeders;

use App\Models\Organization;
use Illuminate\Database\Seeder;

class ProductionSeeder extends Seeder
{

    public function run()
    {

        $this->call([PermissionSeeder::class]);

        $admin = \App\Models\User::create([
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('rz!8TpudwNf*hoMGy_!8Rbip'),
            'email_verified_at' => now(),
        ]);

        $admin->assignRole('super-admin');

        // add real admins.
        $org = Organization::create([
            'name' => 'DCDC',
            'type' => 'admin',
            'owner_id' => $admin->id,
        ]);
        $org->users()->saveMany([$admin]);


    }
}
