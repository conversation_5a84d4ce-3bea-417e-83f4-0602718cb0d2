<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('title_upload_links', function (Blueprint $table) {
            $table->id();

            $table->foreignId('title_id');
            $table->foreignId('user_id');

            $table->string('reference')->nullable();
            $table->string('email')->nullable();

            $table->boolean('is_expired')->default(false);
            $table->dateTime('expires_at')->nullable();

            $table->softDeletes();
            $table->timestamps();

            $table->foreign('title_id')->references('id')->on('titles');

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('title_upload_links');
    }
};
