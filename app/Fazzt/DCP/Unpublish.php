<?php

namespace App\Fazzt\DCP;

use App\Fazzt\Exception;
use App\Fazzt\Json;
use Illuminate\Console\Command;

class Unpublish
{

    static function exec($uuid)
    {
        $params = [
            0 => 'unpublish',
            1 => [$uuid],
            2 => [
                'OnClientCallback' => config('fazzt.callback_url') . 'unpublish',
                'SiteID' => 909920,
                'QueueName' => 'Channel 1 Preprocess',
                'TransmissionModeName' => 'With Reports Command',
            ]
        ];
        try {
            $response = Json::jsonCall(
                'CinemaCmdSubmit',
                null,
                $params,
                true);
            // dump($response);
        }
        catch (\Exception $e) {
            logger("Error: " . $e->getMessage());
        }

        return false;

    }
}
