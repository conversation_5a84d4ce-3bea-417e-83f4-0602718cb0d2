<?php

namespace App\StudioHandlers\Cielo;

use App\Actions\Dcp\VerifyDcp;
use App\Actions\Slack\Notify;
use App\Models\{Booking, CinemaSite, Version};
use App\Models\Enum\BookingStatus;
use Storage;

class InboundProcessor extends \App\StudioHandlers\InboundProcessor
{
    protected $site;

    function __construct($bookingId, $transactionId, $rerun = false)
    {
        parent::__construct($bookingId, $transactionId, $rerun);
    }

    function handleInbound()
    {
        // Add the notification class to the booking.
        $this->booking->notification_class = OutboundNotifier::class;
        $url = isset($this->transaction->transaction_data['notification_url']) ? $this->transaction->transaction_data['notification_url'] : null;
        $this->booking->notification_parameters = ['notification_url' => $url];
        $this->booking->save();

        // only ack once.
        if ($this->booking->overall_status === null) {
            $this->addStatusUpdate(OrderStatuses::ACKNOWLEDGED);
        }

        // Then process the transaction and create/update deliveries as relevant.
        $this->processBooking();

        $this->transaction->processed = 1;
        $this->transaction->save();
    }

    function addStatusUpdate($status)
    {
        $this->booking->setOverallStatus(BookingStatus::tryFrom($status['booking_code']), auth()->user()->id ?? null);
    }

    function processBooking()
    {
        $booking = $this->booking;
        $data = $this->transaction->transaction_data;

        $cielo = Storage::disk('s3-cielocinema');
        $local = Storage::disk('s3');

        // if multiple sites are provided we need to create individal bookings for those sites.
        $cinemaSite = CinemaSite::find($data['cinema_site_id']);
        // if there are multiple sites, we need to clone this booking for each one.
        // if it's just one site, we're good to go.
        if (!$cinemaSite) {
            $this->error("No cinema_site_id provided.");
            return;
        }

        // this sould be s3://cielo-cinesend/some-dcp-path/
        $s3parts = parse_url($data['s3_url']);
        if ($s3parts['host'] !== 'cielo-cinesend') {
            $this->error("Unsupported bucket in s3_url.", $s3parts);
            return;
        }

        // grab just the path
        $root = ltrim($s3parts['path'], '/');
        logger($root);
        // make sure the bucket is supported
        // verify the dcp where it lays
        $initialResults = VerifyDcp::execute($root, 's3-cielocinema');

        if ($initialResults['error'] === true) {
            // attach the error to the transaction data and save it
            $this->error("DCP Validation failed.", $initialResults);
            return;
        }

        // get the dcp uuid from the asset map
        // get the title from the asset map
        $rootUuid = str_replace('urn:uuid:', '', $initialResults['dcp_uuid']);
        $rootTitle = $initialResults['dcp_title'];
        $newRoot = str_replace($root, "cielo/$rootUuid/", $root);

        // check for a verison with that uuid
        $version = Version::where('cpl_uuid', $rootUuid)->first();
        if (!$version) {
            // we need to copy the content if it doesn't already exist on our side
            $s3folders = $cielo->allFiles($root);
            $fileSize = 0;
            $files = [];
            foreach ($s3folders as $filePath) {
                logger("origin: " . $filePath);
                $newFilePath = str_replace($root, $newRoot, $filePath);
                logger("destination: " . $newFilePath);

                if (!$local->exists($newFilePath)) {
                    $moved = $local->writeStream($newFilePath, $cielo->readStream($filePath));
                }
                /**
                 *        {
                 * "path": "aspera-uploads/8ddb04cf-d854-4dc4-b399-a5f01f1f0064/MPAA-PG13-2D_RTG_F_EN-XX_US-13_51_4K_20191210_SMPTE_OV/213998-0000_Rating-R1_a.mxf",
                 * "size": 138314446,
                 * "bytes_written": 138314446
                 * },
                 */
                $fileObject = new \StdClass();
                $fileObject->path = $newFilePath;
                $fileObject->size = $local->size($newFilePath);
                $files[] = $fileObject;
                $fileSize += $local->size($newFilePath);
            }
            // we need to create the title/version for that content.
            $s3Details = [
                'path' => $rootUuid,
                'root' => 'cielo/',
                'files' => $files,
            ];

            if (!$this->booking->title_id) {
                $title = $this->booking->title()->create(
                    [
                        'friendly_title' => $rootTitle,
                        'organization_id' => $this->booking->organization_id,
                        'creator_id' => $this->booking->creator_id,
                    ]);
            }
            $version = $this->booking->title->versions()->create(
                [
                    'version_name' => $rootTitle,
                    'cpl_uuid' => $rootUuid,
                    's3_details' => $s3Details,
                    'creator_id' => $this->booking->creator_id,
                    'size' => $fileSize,
                ]);
        }

        // we need to then adjust this booking to go to the site provided
        $this->booking->title_id = $version->title_id;
        $this->booking->cinema_site_id = $cinemaSite->id;
        $this->booking->save();

        $newDelivery = $this->booking->deliveries()->firstOrCreate([
            'organization_id' => $this->booking->organization_id,
            'title_id' => $version->title_id,
            'version_id' => $version->id,
            'cinema_site_id' => $cinemaSite->id,
            'booking_id' => $this->booking->id,
            'cpl_uuid' => $rootUuid,
        ], [
            'is_electronic' => true,
        ]);

        // then start it?
        $this->addStatusUpdate(OrderStatuses::ACCEPTED);

        try {
            $newDelivery->sendDownloadJob();
        }
        catch (\Exception $e) {
            $this->error($e->getMessage());
        }

    }

    private function error($message, $append = [])
    {
        $data = $this->transaction->transaction_data;
        $data['error'] = $message;
        $data = array_merge($data, $append);
        $this->transaction->transaction_data = $data;
        $this->transaction->save();
        $this->addStatusUpdate(OrderStatuses::ERROR);
    }
}
