<?php

namespace Tests\Feature\Http\Controllers;

use App\Models\CinemaSite;
use App\Models\Booking;
use App\Models\Enum\BookingStatus;
use App\Models\Organization;
use App\Models\Title;
use App\Models\User;
use App\Models\Release;
use App\Models\Version;
use App\StudioHandlers\Disney\OutboundNotifier;
use Tests\TestCase;

class BookingControllerTest extends TestCase
{
    /**
     * A basic feature test example.
     *
     * @return void
     */
    public function test_admin_can_list_bookings()
    {
        $admin = User::factory()->create()->assignRole('admin');
        $response = $this->actingAs($admin)->getJson(route('bookings.index'));
        $response->assertSuccessful();
    }

    public function test_admin_can_see_booking()
    {
        $admin = User::factory()->create()->assignRole('admin');
        $response = $this->actingAs($admin)->getJson(route('bookings.index'));
        $response->assertSuccessful();
    }

    public function test_admin_can_update_booking()
    {
        $admin = User::factory()->create()->assignRole('admin');
        $organization = Organization::factory()->withOwner($admin)->create();
        $title = Title::factory()->withOrganization($organization)->withCreator($admin)->create();
        $cinema = CinemaSite::factory()->withOrganization($organization)->create();
        $booking = Booking::factory()->withTitle($title)->withCreator($admin)
            ->withCinema($cinema)->withOrganization($organization)->create();

        $response = $this->actingAs($admin)->putJson(route('bookings.update', $booking),
            [
                'deliver_at' => now()->addMonth(),
            ]
        );
        $response->assertSuccessful();
    }

    public function test_admin_can_update_booking_status()
    {
        $admin = User::factory()->create()->assignRole('admin');
        $organization = Organization::factory()->withOwner($admin)->create();
        $title = Title::factory()->withOrganization($organization)->withCreator($admin)->create();
        $cinema = CinemaSite::factory()->withOrganization($organization)->create();
        $booking = Booking::factory()->withTitle($title)->withCreator($admin)
            ->withCinema($cinema)->withOrganization($organization)->create();

        $response = $this->actingAs($admin)->putJson(route('bookings.update', $booking),
            [
                'status' => "completed",
            ]
        );

        $response->assertSuccessful();
        $this->assertEquals($booking->statuses->first()->status, BookingStatus::Completed);
    }

    public function test_admin_can_bulk_update_booking_status()
    {
        $admin = User::factory()->create()->assignRole('admin');
        $organization = Organization::factory()->withOwner($admin)->create();
        $title = Title::factory()->withOrganization($organization)->withCreator($admin)->create();
        $cinema = CinemaSite::factory()->withOrganization($organization)->create();
        $booking = Booking::factory()->withTitle($title)->withCreator($admin)
            ->withCinema($cinema)->withOrganization($organization)->create();

        $response = $this->actingAs($admin)->putJson(route('bookings.bulk-update'),
            [
                'booking_ids' => [$booking->id],
                'status' => "completed",
            ]
        );

        $response->assertSuccessful();
        $this->assertEquals($booking->statuses->first()->status, BookingStatus::Completed);

    }

    public function test_non_admin_cannot_update_booking()
    {
        $user = User::factory()->create();
        $admin = User::factory()->create()->assignRole('admin');
        $organization = Organization::factory()->withOwner($admin)->create();
        $title = Title::factory()->withOrganization($organization)->withCreator($admin)->create();
        $cinema = CinemaSite::factory()->withOrganization($organization)->create();
        $booking = Booking::factory()->withTitle($title)->withCreator($admin)
            ->withCinema($cinema)->withOrganization($organization)->create();

        $response = $this->actingAs($user)->putJson(route('bookings.update', $booking),
            [
                'deliver_at' => now()->addMonth(),
            ]
        );
        $response->assertForbidden();
    }

    public function test_manual_override_checks_and_verifies_edelivery_time()
    {
        $admin = User::factory()->create()->assignRole('admin');
        $organization = Organization::factory()->withOwner($admin)->create();
        $title = Title::factory()->withOrganization($organization)->withCreator($admin)->create();
        $cinema = CinemaSite::factory()->withOrganization($organization)->create();
        $release = Release::factory()->onTitle($title)->withOrganization($organization)->create();
        $booking = Booking::factory()->withTitle($title)->withCreator($admin)
            ->withCinema($cinema)->withOrganization($organization)->withRelease($release)->create();
        $version = Version::factory()->onTitle($title)->withCreator($admin)->create();
        $version2 = Version::factory()->onTitle($title)->withCreator($admin)->create();

        // size is zero so this should pass
        $response = $this->actingAs($admin)->putJson(route('bookings.update', $booking),
            [
                'is_electronic' => 1,
            ]);
        
        $response->assertSuccessful();

        // add content
        $updatedBookingResponse = $this->actingAs($admin)->putJson(route('titles.releases.update',
            ['title' => $title, 'release' => $release]), ['attached_content' => [$version->id, $version2->id]]);
        $updatedBookingResponse->assertSuccessful();

        // change deliver at to 1 minute from now. Minimum 20 gig transfer should fail every time. 
        $updateBookingDeliverAtResponse = $this->actingAs($admin)->putJson(route('bookings.update', $booking),
        [
            'deliver_at' => now()->addMinutes(1),
        ]); 

        $updateBookingDeliverAtResponse->assertSuccessful();
        
        $lateBookingResponse = $this->actingAs($admin)->putJson(route('bookings.update', $booking),
        [
            'is_electronic' => 1,
        ]);

        $lateBookingResponse->assertUnprocessable();
    }


     public function test_in_progress_bookings_appear_first()
     {
        $admin = User::factory()->create()->assignRole('admin');
        $organization = Organization::factory()->withOwner($admin)->create();
        $title = Title::factory()->withOrganization($organization)->withCreator($admin)->create();
        $cinema = CinemaSite::factory()->withOrganization($organization)->create();
        $release = Release::factory()->onTitle($title)->withOrganization($organization)->create();

        $bookings = [];
        while (count($bookings) < 5) {
            $bookings[] = Booking::factory()
                ->withTitle($title)
                ->withCreator($admin)
                ->withCinema($cinema)
                ->withRelease($release)
                ->withOrganization($organization)
                ->state([
                    'deliver_at' => now()->addDays(count($bookings) + 1),
                ])
                ->create();
        }
        
        $bookings[2]->update(['overall_status' => BookingStatus::Transmitting->value]);

        // Secondary sort by deliver_at ASC
        $response = $this->actingAs($admin)->getJson(
            route('bookings.index', [
                'sortBy' => 'deliver_at', 
                'sortDirection' => 'DESC',
                'statusPriority' => 'on',
                ])
        );
        $response->assertSuccessful();

        $transmittingIndex = 0;
        foreach ($response->json('data') as $index => $booking) {
            if ($booking['overall_status'] === BookingStatus::Transmitting->value) {
                $transmittingIndex = $index;
            }
        }
        
        $transmittingCount = array_reduce($response->json('data'), function ($count, $booking) {
            return $count + ($booking['overall_status'] === BookingStatus::Transmitting->value ? 1 : 0);
        }, 0);

        // transmitting bookings should be sorted first
        $this->assertEquals($transmittingCount, $transmittingIndex + 1);

        // bookings should still be sorted secondarily by deliver_at ASC
        $previousDeliverAt = null;
        foreach ($response->json('data') as $index => $booking) {
            if ($booking['overall_status'] !== BookingStatus::Transmitting->value) {
                if ($previousDeliverAt !== null) {
                    $this->assertGreaterThanOrEqual( $booking['deliver_at'], $previousDeliverAt);
                }
                $previousDeliverAt = $booking['deliver_at'];
            }
        }
     }
}
