<?php

namespace App\Console\Commands;

use App\Models\{Booking, Release, Delivery};
use Illuminate\Console\Command;

class MigrateToPackages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'to-packages';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Convert existing bookings to packages and associate them';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // grab all bookings where package is null
        $bookings = Booking::whereNull('package_id')->get();

        /** @var Booking $booking */
        foreach ($bookings as $booking) {
            $this->line(str_repeat('-', 60));
            $package = false;
            $this->info("Processing booking {$booking->id}");

            $versions = [];
            /** @var Delivery $delivery */
            foreach ($booking->deliveries as $delivery) {
                $versions[] = $delivery->version_id;
            }

            sort($versions);

            if (count($versions) === 0) {
                $this->warn("No content to package up. Next up!");
                continue;
            }

            if (!$booking->title) {
                $this->warn("No title attached. Next up!");
                continue;
            }

            // if the booking has a transaction id
            if (count($booking->transactions) > 0) {
                $this->info("Has Transactions");

                $transaction = $booking->transactions[0];
                // grab that transaction
                $packageIdentifier = $transaction->transaction_data['package'] ?? false;
                if ($packageIdentifier) {
                    if ($package = Release::where('studio_data->id', $packageIdentifier['id'])
                                          ->where('title_id', $booking->title_id)->first()) {
                        $this->info("Found Existing Release.");
                    }
                    else {
                        // create
                        $package = Release::create(
                            [
                                'package_name' => $packageIdentifier['description'],
                                'title_id' => $booking->title_id,
                                'organization_id' => $booking->organization_id,
                                'studio_data' => $packageIdentifier,
                            ]);

                        $package->content()->attach($versions);

                    }
                }
                else {
                    $this->info("No disney identifier found. Don't know how to handle this one. Next up!");
                    continue;
                }

            }
            else { // if the booking has no transaction
                $this->info("No Studio Transactions found.");
                // if all the version id's match another package, use it instead
                if ($package = Release::whereJsonContains('studio_data', $versions)
                                      ->where('title_id', $booking->title_id)
                                      ->first()) {
                    $this->info("Found Existing Release.");
                }
                else {
                    // otherwise create a package from all version Ids that are attached to the booking
                    $this->info("Creating auto-package with existing CPLs");
                    $package = Release::create(
                        [
                            'package_name' => '[Automatic] Release for ' . $booking->title->friendly_title,
                            'title_id' => $booking->title_id,
                            'organization_id' => $booking->organization_id,
                            'studio_data' => $versions,
                        ]);

                    $package->content()->attach($versions);

                }
            }

            $this->info("Attaching package id to the proper parts.");
            if ($package) {
                foreach ($booking->deliveries as $delivery) {
                    // set all the booking's deliveries' package_id
                    $delivery->package_id = $package->id;
                    $delivery->save();
                }
                // set the booking's package_id
                $booking->package_id = $package->id;
                $booking->save();
            }
        }
        $this->info("Done");
        return Command::SUCCESS;
    }
}
