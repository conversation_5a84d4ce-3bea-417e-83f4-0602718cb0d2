<?php

namespace App\Console\Commands;

use App\Models\FazztTransfer;
use Illuminate\Console\Command;

class FazztDCPTransmit extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fazzt:dcp-transmit';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Transmit the next in queue';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // find the FazztTransmit record that is scheduled for now and hasn't run yet
        $ft = FazztTransfer::where('state', 'PENDING')->where('scheduled_start_at', '<', now())->orderBy('scheduled_start_at', 'ASC')->first();
        if ($ft) {

            // grab the content via asset_uuid and submit a transmit request.

            $this->info("Found Transmission ID {$ft->id} with scheduled start at {$ft->scheduled_start_at}");
            $inProgress = FazztTransfer::whereIn('state', ['SENDING','SCHEDULED'])->first();
            if ($inProgress) {
                $this->warn("-- Transmission currently in progress. We'll have to wait this out.");
                return Command::FAILURE;
            }
            // get the fazzt_content for the package name (filepath)
            if ($fc = $ft->content) {
                $package = $fc->package_file;
                $response = \App\Fazzt\DCP\Transmit::exec($package);
                $this->info("firing request to transfer $package");
                if ($response) {
                    if ($response['error']) {
                        $this->error($response['error']);
                        $ft->status = 'ERROR';
                        $ft->state = 'ERROR';
                        $ft->actual_start_at = now();
                        $ft->actual_completed_at = now();
                        $ft->save();
                        return Command::FAILURE;
                    }
                    // we're going. after this is set, the `dcp-transmit-status` command will update every minute.
                    $ft->status = 'PROCESSING';
                    $ft->state = 'SENDING';
                    $ft->priority = 0;
                    $ft->transmit_id = $response['result'];
                    $ft->actual_start_at = now();
                    $ft->save();
                }
                else {
                    $this->error("Failed to get response from Fazzt Transmit request.");
                }
            }
        }
        else {
            $this->info("No Scheduled Transmissions to trigger.");
        }

        return Command::SUCCESS;
    }
}
