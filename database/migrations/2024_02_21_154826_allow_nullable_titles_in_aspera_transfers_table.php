<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('aspera_transfers', function (Blueprint $table) {
            //

            if (config('database.default') === 'mysql') {
                DB::statement('ALTER TABLE `aspera_transfers` CHANGE `user_id` `user_id` BIGINT UNSIGNED NULL;');
                DB::statement('ALTER TABLE `aspera_transfers` CHANGE `title_id` `title_id` BIGINT UNSIGNED NULL;');
                // ALTER TABLE `aspera_transfers` CHANGE `user_id` `user_id` BIGINT  UNSIGNED  NULL;
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('aspera_transfers', function (Blueprint $table) {
            //
        });
    }
};
